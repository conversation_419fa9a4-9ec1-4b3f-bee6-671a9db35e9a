use trialslyris1;
GO

ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\TLASITES' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\TLASITES' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings */
/* ********************** */
BEGIN TRY
	-- turn off join by email for all lists
	update lists_
	set NoEmailSub_ = 'T'
	where NoEmailSub_ <> 'T'

	-- force all lists to only allow admins to add members
	update lists_ 
	set security_ = 'private'
	where security_ = 'open' and name_ not in ('eclips_js','brandigy')

	-- set all lists to invisible in Discussion Forum Interface
	update lists_ 
	set MriVisibility_ = 'I'
	where MriVisibility_ <> 'I'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* job_updateList-natle_justiceServices */
/* ********************** */
BEGIN TRY
	EXEC dbo.[job_updateList-natle_justiceServices]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.job_updateList-natle_justiceServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_doSyncListMemberData */
/* ********************** */
BEGIN TRY
	EXEC dbo.ky_doSyncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.ky_doSyncListMemberData'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO
ALTER PROC [dbo].[trialsmith_updateMarketingLists]
AS

set nocount on


IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10))
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int)
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int)
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100))


DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @currentListName varchar(100)

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\TLASITES' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\TLASITES' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END


set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'

exec TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers


if not exists (select emailaddr_ from TLASITES.transfer.dbo.trialsmithMarketingListPopulation)
BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE
BEGIN

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ = 'unsub'


    insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_
    from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null


    --delete subscribed members with email addresses that are no longer in the pool

    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from members_ m
    left outer join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') and pool.poolid is null and m.membertype_ <> 'unsub'

    delete m
    from members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_


    -- update

    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from members_ m WITH(NOLOCK)
    inner join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub'



    update m set 
	    DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_
    from members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool
	    on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ <> 'unsub'


    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join members_ m WITH(NOLOCK)
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers')


    -- insert new memberships
    insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_
    from #memberPool

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

set nocount off

GO

ALTER PROC [dbo].[mc_updateListMemberships]
	@debugMode bit = 0
AS

BEGIN TRY

	declare
		@progressLog varchar(max),
		@errorLog varchar(max),
		@CRLF varchar(2),
		@emailSubject varchar(500),
		@escalateError bit,
		@errmsg   nvarchar(2048),
		@severity tinyint,
		@state    tinyint,
		@errno    int,
		@proc     sysname,
		@lineno   int,
		@defaultMembertype varchar(100),
		@defaultSubType varchar(100),
		@defaultMCOption_keepActive bit,
		@defaultMCOption_lockAddress bit,
		@thisListName varchar(100),
		@thisListAutoID int,
		@thisListAutoManageActive bit,
		@message varchar(500)

	set @crlf = char(13) + char(10);
	set @escalateError = 0;
	set @errorLog = ''
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	set @defaultMCOption_keepActive = 0;
	set @defaultMCOption_lockAddress = 0;

	exec MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync
	CREATE TABLE #ListsForLyrisSync (
		autoid int IDENTITY(1,1), 
		siteID int, 
		siteCode varchar(10), 
		orgID int, 
		orgCode varchar(10), 
		list_ varchar(100), 
		isAutoManageActive bit
	)

	DECLARE @updatedMemberships TABLE (
		id int identity(1,1),
		DateJoined_ datetime,
		domain_ varchar(250),
		emailaddr_ varchar(100),
		fullname_ varchar(100),
		list_ varchar(60),
		usernameLc_ varchar(100),
		ExternalMemberID varchar(100)
	)


	truncate table MC_ListMembersForLyris

	insert into #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	from membercentral.datatransfer.dbo.ListsForLyris
	order by orgcode, list_

	insert into MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, fullname_, functionName, isAutoManageActive,domain_,usernameLC_)
	select lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.emailaddr_, lmfl.fullname_, lmfl.functionName, lfl.isAutoManageActive, right(lmfl.emailaddr_,len(lmfl.emailaddr_)-charindex('@',lmfl.emailaddr_)), left(lmfl.emailaddr_,charindex('@',lmfl.emailaddr_)-1)
	from membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	inner join #ListsForLyrisSync lfl on lfl.list_ = lmfl.list_

	-- null blank emails
	update MC_ListMembersForLyris
	set emailaddr_ = null
	where ltrim(rtrim(isnull(emailaddr_,''))) = ''

	-- null blank externalMemberIDs
	update MC_ListMembersForLyris
	set externalMemberID = null
	where ltrim(rtrim(isnull(externalMemberID,''))) = ''

	select @thisListAutoID = min(autoID) from #ListsForLyrisSync
	while @thisListAutoID is not null BEGIN

		select @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		from #ListsForLyrisSync 
		where autoID = @thisListAutoID

		set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive as varchar(5))
		set @progressLog = @progressLog + @crlf + @message
			if @debugMode = 1 RAISERROR(@message,0,1)

		-- mark rows with email addresses to update , regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)'
			set @progressLog = @progressLog + @crlf + @message
				if @debugMode = 1 RAISERROR(@message,0,1)
			update lm
			set updateStatus = 'update'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and functionName in ('managePopulation','manageStatus') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
				inner join members_ m
					on lm.list_ = m.list_
					and lm.list_ = @thisListName
					and lm.emailaddr_ is not null
					and lm.externalMemberID = m.externalMemberID
					and (lm.emailaddr_ <> m.emailaddr_ or lm.fullname_ <> m.fullname_)
					and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0

				left outer join members_ existingAddresses
					on lm.list_ = existingAddresses.list_
					and (
						(lm.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
					)
			where existingAddresses.memberID_ is null


			update lm2 set 
				updateStatus = 'updateSkipped-targetsMultipleListMemberships'
			from MC_ListMembersForLyris lm2
			inner join (
				select lm.emailaddr_, lm.list_, lm.externalMemberID
				from MC_ListMembersForLyris lm
					inner join members_ m
						on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus = 'update'
						and isnull(m.MCOption_lockAddress,0) = 0
				group by lm.emailaddr_, lm.list_, lm.externalMemberID
				having count(*) > 1
			) as temp
				on lm2.emailaddr_ = temp.emailaddr_
				and lm2.list_ = temp.list_
				and lm2.externalMemberID = temp.externalMemberID


			if @thisListAutoManageActive = 1 BEGIN
				-- update the email addresses that have been marked
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updating memberships (Pass 1) - Records: ' + cast(@@rowcount as varchar(10))
				set @progressLog = @progressLog + @crlf + @message
					if @debugMode = 1 RAISERROR(@message,0,1)
				update m 
				set domain_ = lm.domain_,
					emailaddr_ = lm.emailaddr_,
					fullname_ = lm.fullname_,
					usernameLc_ = lm.usernamelc_
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'update'
					and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT
		END CATCH

		


		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update , regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)'
			set @progressLog = @progressLog + @crlf + @message
			if @debugMode = 1 RAISERROR(@message,0,1)

			update lm
				set updateStatus = 'update1'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation','manageStatus') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
				inner join members_ m
					on lm.list_ = m.list_
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.emailaddr_ is not null
					and (lm.emailaddr_ <> m.emailaddr_ or lm.fullname_ <> m.fullname_)
					and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0
				left outer join members_ existingAddresses
					on lm.list_ = existingAddresses.list_
					and (
						(lm.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
					)
			where existingAddresses.memberID_ is null


			update lm2 set 
				updateStatus = 'updateSkipped-targetsMultipleListMemberships'
			from MC_ListMembersForLyris lm2
			inner join (
				select lm.emailaddr_, lm.list_, lm.externalMemberID
				from MC_ListMembersForLyris lm
					inner join members_ m
						on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus = 'update1'
						and isnull(m.MCOption_lockAddress,0) = 0
				group by lm.emailaddr_, lm.list_, lm.externalMemberID
				having count(*) > 1
			) as temp
				on lm2.emailaddr_ = temp.emailaddr_
				and lm2.list_ = temp.list_
				and lm2.externalMemberID = temp.externalMemberID



			if @thisListAutoManageActive = 1
			BEGIN
				-- update the email addresses that have been marked
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updating memberships (Pass 2) - Records: ' + cast(@@rowcount as varchar(10))
				set @progressLog = @progressLog + @crlf + @message
				if @debugMode = 1 RAISERROR(@message,0,1)
				update m set 
					domain_ = lm.domain_,
					emailaddr_ = lm.emailaddr_,
					fullname_ = lm.fullname_,
					usernameLc_ = lm.usernamelc_
				from MC_ListMembersForLyris lm
					inner join members_ m
						on lm.list_ = m.list_ 
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'update1'
						and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0
			END
			--update MC_ListMembersForLyris set updateStatus = 'update' where updateStatus = 'update1' and list_ = @thisListName
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT
		END CATCH


		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding memberships to expire'
			set @progressLog = @progressLog + @crlf + @message
			if @debugMode = 1 RAISERROR(@message,0,1)
			insert into MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, fullname_, functionName,updateStatus,isAutoManageActive)
			select
				l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, null as functionName, 'expired' as updateStatus, l.isAutoManageActive
			from #ListsForLyrisSync l
				inner join members_ m
					on l.autoID = @thisListAutoID
					and l.list_ = m.list_ collate Latin1_General_CI_AI
					and m.membertype_ in ('confirm','held','normal')
					and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
				left outer join MC_ListMembersForLyris lm
					on m.externalMemberID = lm.externalMemberID collate Latin1_General_CI_AI
					and m.list_ = lm.list_ 
			where (lm.autoID is null and isnull(m.MCOption_keepActive,@defaultMCOption_keepActive) = 0)
				--and lm.functionName in ('managePopulation','manageStatus')


			if @thisListAutoManageActive = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expiring members - Records: ' + cast(@@rowcount as varchar(10))
				set @progressLog = @progressLog + @crlf + @message
				if @debugMode = 1 RAISERROR(@message,0,1)
				update m set
					membertype_ = 'expired',
					ExpireDate_ = getdate()
				from MC_ListMembersForLyris lm
					inner join members_ m
						on lm.list_ = m.list_
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'expired'
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding reactivations'
			set @progressLog = @progressLog + @crlf + @message
			if @debugMode = 1 RAISERROR(@message,0,1)
			update lm set
				updateStatus = 'reactivate'
			from members_ m
				inner join MC_ListMembersForLyris lm
					on m.externalMemberID = lm.externalMemberID
					and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
					and m.list_ = lm.list_
					and m.list_ = @thisListName
					and m.membertype_ = 'expired'
					and lm.functionName in ('managePopulation','manageStatus')

			if @thisListAutoManageActive = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Reactivating memberships - Records: ' + cast(@@rowcount as varchar(10))
				set @progressLog = @progressLog + @crlf + @message
				if @debugMode = 1 RAISERROR(@message,0,1)
				update m set
					membertype_ = 'normal',
					ExpireDate_ = null

				from MC_ListMembersForLyris lm
					inner join members_ m
						on lm.list_ = m.list_
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'reactivate'
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT
		END CATCH

		-- add new memberships
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding new memberships'
			set @progressLog = @progressLog + @crlf + @message
			if @debugMode = 1 RAISERROR(@message,0,1)

			update lm
				set updateStatus = 'added'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					and nullif(lm.emailaddr_,'') is not null
				left outer join members_ m
					on lm.list_ = m.list_
					and (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = right(lm.emailaddr_,len(lm.emailaddr_)-charindex('@',lm.emailaddr_)) and m.usernamelc_ = left(lm.emailaddr_,charindex('@',lm.emailaddr_)-1))
					)
			where m.memberID_ is null

			if @thisListAutoManageActive = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Adding new memberships - Records: ' + cast(@@rowcount as varchar(10))
				set @progressLog = @progressLog + @crlf + @message
				if @debugMode = 1 RAISERROR(@message,0,1)
				insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				select 
					getdate() as DateJoined_,
					domain = lm.domain_,
					lm.emailaddr_,
					lm.fullname_,
					lm.list_,
					usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID,
					@defaultMembertype as membertype_,
					@defaultSubType as subype_

				from MC_ListMembersForLyris lm
				where lm.list_ = @thisListName
					and lm.updateStatus = 'added'
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT
		END CATCH

		select @thisListAutoID = min(autoID)
		from #ListsForLyrisSync
		where autoID > @thisListAutoID
	END
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line()
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg
	END
	set @escalateError = 1
END CATCH


--if @debugMode = 1 RAISERROR(@errorLog,0,1)

if len(rtrim(ltrim(@errorLog))) > 0
BEGIN
	set @errorLog = @errorLog + @crlf + @crlf + @crlf + isnull(@progressLog,'')

	set @emailSubject =  convert(varchar(19), getdate(), 121) + ' - MC ListSync Process: Errors Generated'
	exec membercentral.membercentral.dbo.sys_sendEmail
		@from='<EMAIL>',
		@to='<EMAIL>',
		@cc='',
		@bcc='',
		@subject=@emailSubject,
		@message=@errorLog, 
		@priority='normal', 
		@smtpserver='***********', 
		@authUsername='', 
		@authPassword=''

		set @message =  convert(varchar(19), getdate(), 121) + ' : Sent Error Log Email'
		if @debugMode = 1 RAISERROR(@message,0,1)



END

if ( @escalateError = 1)
BEGIN
	set @message =  convert(varchar(19), getdate(), 121) + ' : Escalating Fatal Error'
	if @debugMode = 1 RAISERROR(@message,0,1)

	RAISERROR (@errmsg, @severity, @state, @errno)
END

RETURN

GO