use trialslyris1
GO

CREATE NONCLUSTERED INDEX [IX_lists___AdminSend___Includes3] ON [dbo].[lists_] ([AdminSend_]) INCLUDE ([CreatStamp_],[DescShort_],[Name_])
GO

use lyrisarchive
GO

ALTER PROC dbo.lists_listEngagementByMemberReportExport
@orgcode varchar(10),
@startDate datetime = null,
@endDate datetime = null,
@filename varchar(800)

AS

/* Do not change */
if @startdate is null 
	set @startdate = '1/1/1900';

if @enddate is null 
	set @enddate = getdate();

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;

declare @uniqueLists TABLE (listID int PRIMARY KEY, list_ varchar(100), orgcode varchar(10));
declare @uniqueSenders TABLE (id int IDENTITY(1,1) PRIMARY KEY, emailaddr varchar(200), ExternalMemberID varchar(100), 
	listID int, numNewMessages int NOT NULL DEFAULT 0, numReplies int NOT NULL DEFAULT 0);

insert into @uniqueLists (listID, list_, orgcode)
select ml.listID, l.name_, 	lf.orgcode
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name
	and l.adminSend_ = 'F'
	and lf.orgcode = isnull(@orgcode,lf.orgcode)
inner join dbo.messageLists ml on ml.list = l.name_;

insert into @uniqueSenders (emailaddr, ExternalMemberID, listID)
select hdrfromspc_, max(mem.ExternalMemberID) as ExternalMemberID, ml.listID
from dbo.messages_ m
inner join @uniqueLists ml on m.listID = ml.listID
	and m.creatStamp_ between @startdate and @enddate
left outer join trialslyris1.dbo.members_ mem on mem.list_ = ml.list_
	and mem.EmailAddr_ = m.HdrFromSpc_
group by hdrfromspc_, ml.listID;

update s 
set s.numNewMessages= temp.numNewMessages
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numNewMessages
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ = messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

update s 
set s.numReplies= temp.numReplies
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numReplies
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ <> messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

select ml.orgcode, ml.list_, s.emailaddr, s.ExternalMemberID, s.numNewMessages, s.numReplies 
into #tmpListEngagementbyMemberExport
from @uniqueLists ml 
inner join @uniqueSenders s on s.listID = ml.listID
order by orgcode, emailaddr;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementbyMemberExport order by orgcode, list_';

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;
GO

ALTER PROC dbo.lists_listEngagementReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;

select lf.orgcode, lf.subjecttag, l.name_, l.creatStamp_, l.descShort_, 0 as numTotalMembers, 0 as numNonSenders, 
	0 as [Original Message senders], 0 as [Reply Message Senders], 0 as [Original Message Count], 
	0 as [Reply Message Count]
into #tmpListEngagementExport
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and l.adminSend_ = 'F'
	and lf.orgcode = @orgcode;

UPDATE tmp
set tmp.numTotalMembers = (select count(*) from trialslyris1.dbo.members_ where list_ = tmp.name_ COLLATE Latin1_General_CI_AI and membertype_ in ('normal','held'))
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.numNonSenders = 
	(
		select count(*)
		from trialslyris1.dbo.members_
		where list_ = tmp.name_ COLLATE Latin1_General_CI_AI
		and membertype_ in ('normal','held')
		and emailaddr_ not in (
			select hdrfromspc_ COLLATE Latin1_General_CI_AI
			from dbo.messages_ m
			inner join dbo.messageLists ml on m.listID = ml.listID
				and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
				and m.creatStamp_ between @startdate and @enddate
		) 
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementExport order by orgcode, name_';

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;
GO
