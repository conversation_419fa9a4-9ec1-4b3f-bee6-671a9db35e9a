use trialslyris1
GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

set nocount on

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10),depomemberdataid int)
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int)
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int)
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100))


DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @currentListName varchar(100)

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END


set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'

exec TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers


if not exists (select emailaddr_ from TLASITES.transfer.dbo.trialsmithMarketingListPopulation)
BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE
BEGIN

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ = 'unsub'


    insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
    from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null


    --delete subscribed members with email addresses that are no longer in the pool

    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from members_ m
    left outer join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') and pool.poolid is null and m.membertype_ <> 'unsub'

    delete m
    from members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_


    -- update

    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from members_ m WITH(NOLOCK)
    inner join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
				or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub'


    update m set 
	    DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_,
		depomemberdataid = pool.depomemberdataid
    from members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool
	    on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ <> 'unsub'


    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join members_ m WITH(NOLOCK)
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers')

    -- insert new memberships
    insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid
    from #memberPool


	--update trialsmithUsage
	truncate table dbo.tsdata
	insert into dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, expList, numBadSrc, subType, expires)
	select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
	from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
	where depomemberdataid is not null

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

set nocount off

GO

ALTER PROC dbo.[swl_updateMarketingList-Natle]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100);
DECLARE @listName varchar(100), @now datetime;

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

create TABLE #swl_eligibleForNatleMarketingList (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(100), 
	email varchar(100),
	usernameLC_ varchar(100),
	domain_ varchar(250)
);

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

exec membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList;

if not exists (select * from tlasites.dataTransfer.dbo.swl_eligibleForNatleMarketingList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList ended with no rows in table  membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.swl_eligibleForNatleMarketingList;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

ALTER PROC [dbo].[job_updateList-natle_justiceServices]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @listName varchar(100), @now datetime

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers

create TABLE #tempListMembers (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(500), 
	email varchar(200),
	usernameLC_ varchar(100),
	domain_ varchar(150)
)


set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

exec membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList

if not exists (select * from tlasites.dataTransfer.dbo.natle_eligibleForJusticeServicesList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList ended with no rows in table  membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.natle_eligibleForJusticeServicesList



	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email))


	-- update email addresses/fullname based on matching membernumber and association
	update m set
		domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers tmp
	inner join members_ m
		on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
	left outer join members_ prexistingEmail
		on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null

	-- mark email addresses that are NOT in temp table as expired
	update m set
		membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
		and tmp.email is null
		and m.membertype_ in ('normal','held')
		and m.association_ not in ('CT')

	-- reactivate previously expired email addresses that are in temp table
	update m set
		membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired'
		and m.association_ not in ('CT')

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from dbo.members_
		where list_ = @listname 
	) 

	insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now,domain_,email,fullname,@listName ,usernameLc_ ,memberNumber as ExternalMemberID,orgcode
	from #tempListMembers
	where orgcode not in ('CT')
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers
GO

ALTER PROC dbo.job_runHourlyJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings  */
/* ********************** */
BEGIN TRY
	exec dbo.job_enforceListSettings
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC tlasites.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


RETURN 0
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* job_updateList-natle_justiceServices */
/* ********************** */
BEGIN TRY
	EXEC dbo.[job_updateList-natle_justiceServices]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.job_updateList-natle_justiceServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_doSyncListMemberData */
/* ********************** */
BEGIN TRY
	EXEC dbo.ky_doSyncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.ky_doSyncListMemberData'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

