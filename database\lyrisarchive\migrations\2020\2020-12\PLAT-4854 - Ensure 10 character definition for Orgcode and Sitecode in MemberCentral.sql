use trialslyris1
GO

DROP INDEX [_dta_index_lists_format_c_6_1965250056__K2_K3] ON [dbo].[lists_format] WITH ( ONLINE = OFF )
GO
DROP INDEX [IX_lists_format__hidden__name__orgcode] ON [dbo].[lists_format]
GO
DROP STATISTICS [dbo].[lists_format].[_dta_stat_1965250056_3_2]
GO
ALTER TABLE dbo.lists_format ALTER COLUMN orgcode varchar(10) null;
GO
CREATE CLUSTERED INDEX [IX_lists_format__name__orgcode] ON [dbo].[lists_format]
(
	[name] ASC,
	[orgcode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IX_lists_format__hidden__name__orgcode] ON [dbo].[lists_format]
(
	[hidden] ASC,
	[name] ASC,
	[orgcode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

ALTER TABLE dbo.lists_headerfooter ALTER COLUMN orgcode varchar(10) null;
GO

ALTER TABLE dbo.sw_marketing ALTER COLUMN orgcode varchar(10) null;
GO

use lyrisarchive
GO

ALTER PROC dbo.dash_lists_messagePerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_newTopicsPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_topicRepliesPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_uniqueTopicRepliersPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_uniqueTopicStartersPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.lists_listActivityReportExport
@orgcode varchar(10),
@orgEmailDomain varchar(100),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport

select 
	l.name_,
	l.creatStamp_,
	l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI) as numTotalMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numStaffMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numNonStaffMembers,
	(
		select max(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
		group by ml.list
	) as mostRecentMessage,
	(
		select count(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_  COLLATE Latin1_General_CI_AI
		and right(hdrFromSpc_,len(hdrFromSpc_) - charindex('@',hdrFromSpc_)) not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')
		group by ml.list
	) as numMessagesInArchive
into ##tmpListActvityExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select name_, creatStamp_, descShort_, numTotalMembers, numStaffMembers, numNonStaffMembers, mostRecentMessage, numMessagesInArchive from ##tmpListActvityExport order by name_'

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport
GO

ALTER PROC dbo.lists_listEngagementByMemberReportExport
@orgcode varchar(10),
@startDate datetime = null,
@endDate datetime = null,
@filename varchar(800)

AS

/* Do not change */
if @startdate is null 
	set @startdate = '1/1/1900'

if @enddate is null 
	set @enddate = getdate()

IF OBJECT_ID('tempdb..##tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementbyMemberExport

declare @uniqueLists TABLE (listID int PRIMARY KEY, list_ varchar(100), orgcode varchar(10));
declare @uniqueSenders TABLE (id int IDENTITY(1,1) PRIMARY KEY, emailaddr varchar(200), ExternalMemberID varchar(100), 
	listID int, numNewMessages int NOT NULL DEFAULT 0, numReplies int NOT NULL DEFAULT 0);

insert into @uniqueLists (listID, list_, orgcode)
select ml.listID, l.name_, 	lf.orgcode
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf
	on l.name_ = lf.name
	and l.adminSend_ = 'F'
	and lf.orgcode = isnull(@orgcode,lf.orgcode)
inner join dbo.messageLists ml 
	on ml.list = l.name_

insert into @uniqueSenders (emailaddr, ExternalMemberID, listID)
select hdrfromspc_, max(mem.ExternalMemberID) as ExternalMemberID, ml.listID
from messages_ m
inner join @uniqueLists ml on m.listID = ml.listID
	and m.creatStamp_ between @startdate and @enddate
left outer join trialslyris1.dbo.members_ mem
	on mem.list_ = ml.list_
	and mem.EmailAddr_ = m.HdrFromSpc_
group by hdrfromspc_, ml.listID


update s set
	 numNewMessages= temp.numNewMessages
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numNewMessages
	from messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ = messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr


update s set
	 numReplies= temp.numReplies
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numReplies
	from messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ <> messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr

select 
	ml.orgcode, ml.list_, s.emailaddr, 
	s.ExternalMemberID, s.numNewMessages, s.numReplies
	into ##tmpListEngagementbyMemberExport
from @uniqueLists ml 
inner join @uniqueSenders s
	on s.listID = ml.listID
order by orgcode, emailaddr

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListEngagementbyMemberExport order by orgcode, list_'

IF OBJECT_ID('tempdb..##tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementbyMemberExport
GO

ALTER PROC dbo.lists_listEngagementReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListEngagementExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementExport

select lf.orgcode, lf.subjecttag, l.name_, l.creatStamp_, l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and membertype_ in ('normal','held')) as numTotalMembers,
	(
		select count(*)
		from lyris.trialslyris1.dbo.members_
		where list_ = l.name_ COLLATE Latin1_General_CI_AI
		and membertype_ in ('normal','held')
		and emailaddr_ not in (
			select hdrfromspc_ COLLATE Latin1_General_CI_AI
			from dbo.messages_ m
			inner join dbo.messageLists ml on m.listID = ml.listID
				and ml.list = l.name_ COLLATE Latin1_General_CI_AI
				and m.creatStamp_ between @startdate and @enddate
		) 
	) as numNonSenders,
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	) as [Original Message senders],
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	) as [Reply Message Senders],
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	) as [Original Message Count],
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	) as [Reply Message Count]
into ##tmpListEngagementExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and l.adminSend_ = 'F'
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListEngagementExport order by orgcode, name_'

IF OBJECT_ID('tempdb..##tmpListEngagementExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementExport
GO

ALTER PROC dbo.lists_listMessagePerDayReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport

declare @listcounts TABLE (list varchar(100) PRIMARY KEY, membercount int)
insert into @listcounts (list, membercount)
select m.list_, count(*)
from lyris.trialslyris1.dbo.members_ as m
inner join lyris.trialslyris1.dbo.lists_format lf on m.list_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
where m.membertype_ in ('normal','held')
and m.list_ not in ('seminarweblive')
group by m.list_

select CONVERT(VARCHAR(10),creatStamp_,101) as date, 
	dayofweek = case datepart(dw,creatStamp_)
		when 1 then 'Sunday'
		when 2 then 'Monday'
		when 3 then 'Tuesday'
		when 4 then 'Wednesday'
		when 5 then 'Thursday'
		when 6 then 'Friday'
		when 7 then 'Saturday'
	end
	, sum(lc.membercount) totalMessagesSent, count(*) as uniqueMessageCount
	, ROW_NUMBER() OVER (ORDER BY CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)) as row
into ##tmpListPerDayExport
from messagelists ml WITH(nolock)
inner join messages_ m WITH(nolock) on m.listID = ml.listID
	and creatStamp_ between @startDate and @endDate
inner join @listcounts lc on lc.list = ml.list
group by CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select [date], [dayofweek], uniqueMessageCount from ##tmpListPerDayExport order by row'

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport
GO

ALTER PROC dbo.lists_listMessagePerYearReportExport
@orgcode varchar(10),
@filename varchar(800)

AS

declare @minYear int, @maxYear int, @yrList varchar(200), @yrListSelect varchar(max), @pvtQry varchar(1000);

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;

CREATE TABLE #tmpData (list varchar(60), msgYear int, msgCount int);

-- get unpivoted data
INSERT INTO #tmpData (list, msgYear, msgCount)
select ml.list, year(creatStamp_), count(*)
from dbo.messages_ m 
inner join dbo.messageLists ml on m.listID = ml.listID
inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
group by lf.orgcode, ml.list, year(creatStamp_);

-- get min and max years in unpivoted data
select @minYear=min(msgYear), @maxYear=max(msgYear)
from #tmpData;

-- generate all years between min and max years as a string for pivot
with yearCTE as (
	select @minYear as yr
		union all
	select yr + 1
	from yearCTE
	where yr < @maxYear
)
select @yrList = COALESCE(@yrList + ',','') + quoteName(yr), 
	@yrListSelect = COALESCE(@yrListSelect + ',','') + 'isnull(pvt.' + quoteName(yr) + ',0) as ' + quoteName(yr)
from yearCTE
order by yr;

-- pivot the data
set @pvtQry = 'select pvt.list, ' + @yrListSelect + ' 
	into ##tmpListPerYearExport
	from #tmpData as rawdata 
	PIVOT (sum(msgCount) for msgYear in (' + @yrList + ')) as pvt;';
EXEC(@pvtQry);

-- export it
EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport order by 1, 2'

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;
GO

ALTER PROC dbo.lists_listPowerUsersReportExport
@orgcode varchar(10),
@startYear varchar(4),
@includeLists varchar(max),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport

declare @optionalIncludeListFilter TABLE ( listname varchar(100));
insert into @optionalIncludeListFilter (listname)
select listitem from dbo.fn_varcharListToTable(@includeLists,',') where listitem <> ''

declare @listnames varchar(max)
SELECT @listnames = COALESCE(@listnames + ',','') + '''' + listname + '''' FROM @optionalIncludeListFilter

declare @yearlist varchar(500)
; with yearsCTE as (
	select cast(@startYear as int) as theYear
		union all
	select yearsCTE.theYear + 1 as theYear
	from yearsCTE
	where yearsCTE.theYear < year(getdate())
)
SELECT @yearlist = COALESCE(@yearlist + ',','') + quoteName(CAST(theYear AS varchar(4)))
FROM yearsCTE

declare @sql varchar(8000)
set @sql = '
	select list, hdrfromspc_, fullname_, ' + @yearlist + '
	into ##tmpListPowerUsersExport
	from 
		(
			select ml.list, m.hdrfromspc_,mem.fullname_, year(creatStamp_) as year, count(*) as messageCount
			from dbo.messages_ m 
			inner join dbo.messageLists ml on m.listID = ml.listID '
if @listnames is not null
	set @sql = @sql + 'and ml.list in (' + @listnames + ') '
set @sql = @sql + '
			inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI 
				and lf.orgcode = ''' + @orgcode + '''
			left outer join lyris.trialslyris1.dbo.members_ mem
				on mem.emailaddr_ = m.hdrfromspc_ COLLATE Latin1_General_CI_AI
				and mem.list_ = lf.name COLLATE Latin1_General_CI_AI
			group by ml.list, m.hdrfromspc_, mem.fullname_, year(creatStamp_)
		) as rawdata
	PIVOT (sum(messageCount) for year in (' + @yearlist + ')) as pivottable'
exec(@sql)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPowerUsersExport order by 1'

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport
GO

use lyrisCustom
GO

ALTER PROC dbo.list_queueDigest

@isTestMode bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyToProcessStatusID INT, @digestDate DATETIME, @digestDateStart DATETIME, @digestDateEnd DATETIME;
	DECLARE @listsWithMCThreadIndexRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(10));
	DECLARE @listsWithMCThreadDigestRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(10));

	declare @testModeGroupCode varchar(25) = 'DigestTestModeGroup', @testModeGroupID int, @testModeGroupHasMembers bit;

	SET @digestDate = DATEADD(D,-1,GETDATE());
	SET @digestDateStart = cast(@digestDate as date);
	SET @digestDateEnd = DATEADD(MS,-3,DATEADD(d,1,@digestDateStart));

	SELECT @queueTypeID = queueTypeID
	FROM memberCentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'listDigests';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM memberCentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'ReadyToProcess';


	IF @isTestMode = 0 BEGIN

		INSERT INTO @listsWithMCThreadIndexRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadIndex = 1
			AND m.memberType_ in ('normal','held')
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadIndex = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		INSERT INTO @listsWithMCThreadDigestRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadDigest = 1
			AND m.memberType_ in ('normal','held')
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadDigest = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		END ELSE BEGIN


			select @testModeGroupID = groupID
			from membercentral.membercentral.dbo.ams_groups g
			where orgID=1 and g.groupCode = @testModeGroupCode and g.status='A'

			select @testModeGroupHasMembers = case when count(*) > 0 then 1 else 0 end
			from membercentral.membercentral.dbo.cache_members_groups mg
			where groupID = @testModeGroupID


			IF @testModeGroupHasMembers = 1 BEGIN
				INSERT INTO @listsWithMCThreadIndexRecipients(list)
				select lf.name
				from trialslyris1.dbo.lists_format lf 
				inner join membercentral.membercentral.dbo.sites s 
					on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
				inner join membercentral.membercentral.dbo.lists_lists l
					on l.listName = lf.name collate Latin1_General_CI_AI
					and l.supportsMCThreadIndex = 1
				inner join membercentral.membercentral.dbo.cms_siteResources sr
					on sr.siteResourceID = l.siteResourceID
					and sr.siteResourceStatusID=1
					group by lf.name;

				INSERT INTO @listsWithMCThreadDigestRecipients(list)
				select lf.name
				from trialslyris1.dbo.lists_format lf 
				inner join membercentral.membercentral.dbo.sites s 
					on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
				inner join membercentral.membercentral.dbo.lists_lists l
					on l.listName = lf.name collate Latin1_General_CI_AI
					and l.supportsMCThreadDigest = 1
				inner join membercentral.membercentral.dbo.cms_siteResources sr
					on sr.siteResourceID = l.siteResourceID
					and sr.siteResourceStatusID=1
					group by lf.name;
			END
		END

	IF EXISTS (select * from @listsWithMCThreadIndexRecipients) BEGIN
		UPDATE t 
		SET t.orgcode = lf.orgcode
		FROM @listsWithMCThreadIndexRecipients t
		INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
			AND lf.disabled = 0;

		-- populate platformQueue table
		INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
		select l.orgcode, ml.list, 'ListThreadIndex', @digestDate, @readyToProcessStatusID, @isTestMode
		from @listsWithMCThreadIndexRecipients l
		inner join lyrisarchive.dbo.messageLists ml on ml.list = l.list collate Latin1_General_CI_AI
		inner join lyrisarchive.dbo.messages_ m on m.listID = ml.listID
			and m.creatStamp_ between @digestDateStart and @digestDateEnd
		group by l.orgcode, ml.list;
	END


	IF EXISTS (select * from @listsWithMCThreadDigestRecipients) BEGIN
		
		UPDATE t 
		SET t.orgcode = lf.orgcode
		FROM @listsWithMCThreadDigestRecipients t
		INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
			AND lf.disabled = 0;


		INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
		select l.orgcode, ml.list, 'ListThreadDigest', @digestDate, @readyToProcessStatusID, @isTestMode
		from @listsWithMCThreadDigestRecipients l
		inner join lyrisarchive.dbo.messageLists ml
		on ml.list = l.list collate Latin1_General_CI_AI
		inner join lyrisarchive.dbo.messages_ m	on m.listID = ml.listID
		and m.creatStamp_ between @digestDateStart and @digestDateEnd
		group by l.orgcode, ml.list;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.natle_justiceServices

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(10), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.natle_justiceServicesEligible;

if not exists (select * from tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_justiceServicesEligible ended with no rows in table membercentral.dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held')
	and m.association_ not in ('CT');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired'
	and m.association_ not in ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers
	where orgcode not in ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

ALTER PROC dbo.natle_seminarWebLive

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
create TABLE #swl_eligibleForNatleMarketingList (platform varchar(20), orgcode varchar(10), memberID int, 
	membernumber varchar(100), fullname varchar(100), email varchar(100), usernameLC_ varchar(100), 
	domain_ varchar(250));

exec membercentral.customApps.dbo.natle_seminarWebLiveEligible;

if not exists (select * from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table  membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.' ;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join trialslyris1.dbo.members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from trialslyris1.dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

ALTER PROC dbo.ts_membercentraladmins

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'membercentraladmins'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(10), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.ts_membercentraladminsEligible;

if not exists (select * from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating MemberCentralAdmins List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.ts_membercentraladminsEligible ended with no rows in table membercentral.dataTransfer.dbo.ts_membercentraladminsEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired';

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers;
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO


