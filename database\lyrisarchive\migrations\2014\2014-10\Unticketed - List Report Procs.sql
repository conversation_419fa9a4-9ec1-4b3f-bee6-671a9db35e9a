use lyrisarchive
GO

CREATE Function [dbo].[fn_varCharListToTable] (@varcharlist varchar(max), @delimiter varchar(1)) 
returns @VarcharTable table (autoid int IDENTITY (1,1), listitem varchar(max))
AS
begin

	declare @strAsXML as xml
	set @strAsXML = cast(('<x>'+replace(replace(@varcharlist,'&','&amp;'),@delimiter,'</x><x>')+'</x>') as xml)
	
	insert into @varchartable(listitem)
	select N.value('.','varchar(max)') as value
	from @strAsXML.nodes('x') as T(N)

	return
end
GO

CREATE ASSEMBLY [fileSystemOperations]
AUTHORIZATION [dbo]
FROM 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
WITH PERMISSION_SET = EXTERNAL_ACCESS

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0x7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A7573696E672053797374656D2E546578743B0D0A7573696E672053797374656D2E546578742E526567756C617245787072657373696F6E733B0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C436861727320666E5F436F6E736F6C6964617465576869746553706163652853716C43686172732066696C65636F6E74656E74290D0A202020207B0D0A2020202020202020737472696E67207061747465726E203D2040225C737B322C7D223B0D0A202020202020202052656765782072656765784F626A203D206E6577205265676578287061747465726E2C2052656765784F7074696F6E732E436F6D70696C6564207C2052656765784F7074696F6E732E49676E6F726543617365293B0D0A20202020202020202F2F53716C537472696E672072657475726E537472696E67203D206E65772053716C537472696E672872656765784F626A2E5265706C6163652866696C65636F6E74656E742E546F537472696E6728292C2022202229293B0D0A202020202020202053716C43686172732072657475726E4368617273203D206E65772053716C43686172732872656765784F626A2E5265706C6163652866696C65636F6E74656E742E546F53716C537472696E6728292E546F537472696E6728292C2022202229293B0D0A202020202020202072657475726E2072657475726E43686172733B0D0A202020207D0D0A7D3B
AS N'ConsolidateWhiteSpace.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0x7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A7573696E672053797374656D2E494F3B0D0A0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C426F6F6C65616E20666E5F46696C6545786973747328537472696E672046696C6550617468290D0A202020207B0D0A202020202020202072657475726E2046696C652E4578697374732846696C6550617468293B0D0A202020207D0D0A7D3B0D0A0D0A
AS N'FileExists.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0xEFBBBF7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A7573696E672053797374656D2E494F3B0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C426F6F6C65616E20666E5F636F707946696C6528537472696E6720536F75726546696C65506174682C20537472696E67204465737446696C65506174682C2053716C426F6F6C65616E20616C6C6F774F7665727772697465290D0A202020207B0D0A20202020202020202F2F2050757420796F757220636F646520686572650D0A202020202020202053716C426F6F6C65616E20737563636573733B0D0A0D0A20202020202020207472790D0A20202020202020207B0D0A20202020202020202020202046696C652E436F707928536F75726546696C65506174682C204465737446696C65506174682C2028426F6F6C65616E29616C6C6F774F7665727772697465293B0D0A20202020202020202020202073756363657373203D20747275653B0D0A20202020202020207D0D0A202020202020202063617463680D0A20202020202020207B0D0A20202020202020202020202073756363657373203D2066616C73653B0D0A20202020202020207D0D0A0D0A202020202020202072657475726E20737563636573733B0D0A202020207D0D0A7D3B0D0A0D0A
AS N'fn_copyFile.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0xEFBBBF7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A7573696E672053797374656D2E494F3B0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C426F6F6C65616E20666E5F6372656174654469726563746F727928537472696E672070617468290D0A202020207B0D0A20202020202020202F2F2050757420796F757220636F646520686572650D0A202020202020202053716C426F6F6C65616E20737563636573733B0D0A0D0A20202020202020207472790D0A202020202020207B0D0A0D0A20202020202020202020202069662028214469726563746F72792E457869737473287061746829290D0A2020202020202020202020207B0D0A202020202020202020202020202020204469726563746F72792E4372656174654469726563746F72792870617468293B0D0A2020202020202020202020207D0D0A20202020202020202020202073756363657373203D20747275653B0D0A0D0A202020207D0D0A202020202020202063617463680D0A20202020202020207B0D0A20202020202020202020202073756363657373203D2066616C73653B0D0A20202020202020207D0D0A0D0A202020202020202072657475726E20737563636573733B0D0A202020207D0D0A7D3B0D0A0D0A
AS N'fn_createDirectory.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0xEFBBBF7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A7573696E672053797374656D2E494F3B0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C426F6F6C65616E20666E5F4469726563746F727945786973747328537472696E672070617468290D0A202020207B0D0A202020202020202072657475726E202853716C426F6F6C65616E294469726563746F72792E4578697374732870617468293B0D0A202020207D0D0A7D3B0D0A0D0A
AS N'fn_DirectoryExists.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 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
AS N'fn_ReadFile.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 0xEFBBBF7573696E672053797374656D3B0D0A7573696E672053797374656D2E446174613B0D0A7573696E672053797374656D2E446174612E53716C436C69656E743B0D0A7573696E672053797374656D2E446174612E53716C54797065733B0D0A7573696E67204D6963726F736F66742E53716C5365727665722E5365727665723B0D0A0D0A7075626C6963207061727469616C20636C6173732055736572446566696E656446756E6374696F6E730D0A7B0D0A202020205B4D6963726F736F66742E53716C5365727665722E5365727665722E53716C46756E6374696F6E5D0D0A202020207075626C6963207374617469632053716C426F6F6C65616E20666E5F577269746546696C652853716C537472696E672066696C655F6E616D652C205B53716C4661636574284D617853697A65203D202D31295D2053716C537472696E672066696C655F636F6E74656E74732C2053716C426F6F6C65616E206F7665727772697465290D0A202020207B0D0A202020202020202053716C426F6F6C65616E2073756363657373203D2066616C73653B0D0A20202020202020202F2F2050757420796F757220636F646520686572650D0A20202020202020206966202821666E5F46696C654578697374732866696C655F6E616D652E546F537472696E67282929207C7C206F7665727772697465290D0A20202020202020207B0D0A202020202020202020202020747279207B0D0A2020202020202020202020202020202053797374656D2E494F2E46696C652E5772697465416C6C546578742866696C655F6E616D652E546F537472696E6728292C2066696C655F636F6E74656E74732E546F537472696E672829293B0D0A2020202020202020202020202020202073756363657373203D20747275653B0D0A2020202020202020202020207D206361746368207B0D0A2020202020202020202020202020202073756363657373203D2066616C73653B0D0A2020202020202020202020207D0D0A0D0A20202020202020207D0D0A202020202020202072657475726E20737563636573733B0D0A202020207D0D0A7D3B0D0A0D0A
AS N'fn_WriteFile.cs'

GO
ALTER ASSEMBLY [fileSystemOperations]
ADD FILE FROM 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
AS N'Properties\AssemblyInfo.cs'

GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'ASSEMBLY',@level0name=N'fileSystemOperations'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyProjectRoot', @value=N'\\Devserver\e$\ConsoleDevelopment\SQLCLR\fileSystemOperations\fileSystemOperations' , @level0type=N'ASSEMBLY',@level0name=N'fileSystemOperations'
GO

CREATE FUNCTION [dbo].[fn_WriteFile](@file_name [nvarchar](4000), @file_contents [nvarchar](max), @overwrite [bit])
RETURNS [bit] WITH EXECUTE AS CALLER
AS 
EXTERNAL NAME [fileSystemOperations].[UserDefinedFunctions].[fn_WriteFile]
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_WriteFile'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFile', @value=N'fn_WriteFile.cs' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_WriteFile'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFileLine', @value=9 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_WriteFile'
GO

CREATE FUNCTION [dbo].[fn_ReadFile](@FilePath [nvarchar](4000), @doCompression [bit], @useCurrentSecurityContext [bit])
RETURNS [nvarchar](max) WITH EXECUTE AS CALLER
AS 
EXTERNAL NAME [fileSystemOperations].[UserDefinedFunctions].[fn_ReadFile]
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_ReadFile'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFile', @value=N'fn_ReadFile.cs' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_ReadFile'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFileLine', @value=13 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'FUNCTION',@level1name=N'fn_ReadFile'
GO

CREATE PROCEDURE [dbo].[up_exportCSV]
@csvfilename varchar(400),
@sql varchar(max)

AS

-- drop the temp tables
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol

-- run query into temp table
select @sql = stuff(@sql, charIndex('from',@sql), len('from'), 'into ##tmpCSVExport from')
EXEC(@sql)

-- export as chr(31) column delimiter and chr(30) row delimiter
DECLARE @cmd varchar(7000)
set @cmd = 'bcp ##tmpCSVExport out "' + @csvfilename + '" -c -t0x1F -r0x1E -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- read in file to replace beeps with commas and escape quotes and replace nulls with empty string
declare @tmpFile varchar(max), @trash bit
select @tmpFile = dbo.fn_ReadFile(@csvfilename,0,1)
if len(@tmpFile) > 0 BEGIN
	select @tmpFile = replace(left(@tmpFile,len(@tmpFile)-1),'"','""') -- escape all quotes
	select @tmpFile = replace(replace(@tmpFile,char(13),' '),char(10),' ') -- replace cr and lf with space
	select @tmpFile = replace(@tmpFile COLLATE SQL_Latin1_General_CP1_CS_AS,char(0),'')		-- replace nulls with empty string
	select @tmpFile = replace(@tmpFile,char(31),'","')	-- replace 31 with column separator
	select @tmpFile = replace(@tmpFile,char(30),'"' + char(13) + char(10) + '"') -- replace 30 with crlf
	select @tmpFile = '"' + @tmpFile + '"'
end

-- record 1st row as field names and save file
CREATE TABLE #tmpCol (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)
INSERT INTO #tmpCol
EXEC tempdb.dbo.SP_COLUMNS ##tmpCSVExport
declare @colList varchar(max)
select @colList = COALESCE(@colList + ',', '') + '"' + column_name + '"'
	from #tmpCol 
	order by ORDINAL_POSITION
select @trash = dbo.fn_WriteFile(@csvfilename,@colList + char(13) + char(10) + @tmpFile,1)

-- get fields returned
SELECT COLUMN_NAME 
FROM #tmpCol
order by ORDINAL_POSITION

-- drop temp tables
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
GO


CREATE PROC dbo.lists_listActivityReportExport
@orgcode varchar(5),
@orgEmailDomain varchar(100),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport

select 
	l.name_,
	l.creatStamp_,
	l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI) as numTotalMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numStaffMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numNonStaffMembers,
	(
		select max(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
		group by ml.list
	) as mostRecentMessage,
	(
		select count(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_  COLLATE Latin1_General_CI_AI
		and right(hdrFromSpc_,len(hdrFromSpc_) - charindex('@',hdrFromSpc_)) not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')
		group by ml.list
	) as numMessagesInArchive
into ##tmpListActvityExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select name_, creatStamp_, descShort_, numTotalMembers, numStaffMembers, numNonStaffMembers, mostRecentMessage, numMessagesInArchive from ##tmpListActvityExport order by name_'

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport
GO

CREATE PROC dbo.lists_listEngagementReportExport
@orgcode varchar(5),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListEngagementExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementExport

select lf.orgcode, lf.subjecttag, l.name_, l.creatStamp_, l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and membertype_ in ('normal','held')) as numTotalMembers,
	(
		select count(*)
		from lyris.trialslyris1.dbo.members_
		where list_ = l.name_ COLLATE Latin1_General_CI_AI
		and membertype_ in ('normal','held')
		and emailaddr_ not in (
			select hdrfromspc_ COLLATE Latin1_General_CI_AI
			from dbo.messages_ m
			inner join dbo.messageLists ml on m.listID = ml.listID
				and ml.list = l.name_ COLLATE Latin1_General_CI_AI
				and m.creatStamp_ between @startdate and @enddate
		) 
	) as numNonSenders,
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	) as [Original Message senders],
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	) as [Reply Message Senders],
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	) as [Original Message Count],
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	) as [Reply Message Count]
into ##tmpListEngagementExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and l.adminSend_ = 'F'
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListEngagementExport order by orgcode, name_'

IF OBJECT_ID('tempdb..##tmpListEngagementExport') IS NOT NULL 
	DROP TABLE ##tmpListEngagementExport
GO

CREATE PROC dbo.lists_listMessagePerYearReportExport
@orgcode varchar(5),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport

select *
into ##tmpListPerYearExport
from (
	select ml.list, year(creatStamp_) as year, count(*) as messageCount
	from messages_ m 
	inner join dbo.messageLists ml on m.listID = ml.listID
	inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI
		and lf.orgcode = @orgcode
	group by lf.orgcode, ml.list, year(creatStamp_)
) as rawdata
PIVOT (sum(messageCount) for year in ([1998],[1999],[2000],[2001],[2002],[2003],[2004],[2005],[2006],[2007],[2008],[2009],[2010],[2011],[2012],[2013],[2014],[2015])) as pivottable

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport order by 1, 2'

IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport
GO

CREATE PROC dbo.lists_listMessagePerDayReportExport
@orgcode varchar(5),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport

declare @listcounts TABLE (list varchar(100) PRIMARY KEY, membercount int)
insert into @listcounts (list, membercount)
select m.list_, count(*)
from lyris.trialslyris1.dbo.members_ as m
inner join lyris.trialslyris1.dbo.lists_format lf on m.list_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
where m.membertype_ in ('normal','held')
and m.list_ not in ('seminarweblive')
group by m.list_

select CONVERT(VARCHAR(10),creatStamp_,101) as date, 
	dayofweek = case datepart(dw,creatStamp_)
		when 1 then 'Sunday'
		when 2 then 'Monday'
		when 3 then 'Tuesday'
		when 4 then 'Wednesday'
		when 5 then 'Thursday'
		when 6 then 'Friday'
		when 7 then 'Saturday'
	end
	, sum(lc.membercount) totalMessagesSent, count(*) as uniqueMessageCount
	, ROW_NUMBER() OVER (ORDER BY CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)) as row
into ##tmpListPerDayExport
from messagelists ml WITH(nolock)
inner join messages_ m WITH(nolock) on m.listID = ml.listID
	and creatStamp_ between @startDate and @endDate
inner join @listcounts lc on lc.list = ml.list
group by CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select [date], [dayofweek], uniqueMessageCount from ##tmpListPerDayExport order by row'

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport
GO

CREATE PROC dbo.lists_listPowerUsersReportExport
@orgcode varchar(5),
@startYear varchar(4),
@includeLists varchar(max),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport

declare @optionalIncludeListFilter TABLE ( listname varchar(100));
insert into @optionalIncludeListFilter (listname)
select listitem from dbo.fn_varcharListToTable(@includeLists,',') where listitem <> ''

declare @listnames varchar(max)
SELECT @listnames = COALESCE(@listnames + ',','') + '''' + listname + '''' FROM @optionalIncludeListFilter

declare @yearlist varchar(500)
; with yearsCTE as (
	select cast(@startYear as int) as theYear
		union all
	select yearsCTE.theYear + 1 as theYear
	from yearsCTE
	where yearsCTE.theYear < year(getdate())
)
SELECT @yearlist = COALESCE(@yearlist + ',','') + quoteName(CAST(theYear AS varchar(4)))
FROM yearsCTE

declare @sql varchar(8000)
set @sql = '
	select list, hdrfromspc_, fullname_, ' + @yearlist + '
	into ##tmpListPowerUsersExport
	from 
		(
			select ml.list, m.hdrfromspc_,mem.fullname_, year(creatStamp_) as year, count(*) as messageCount
			from dbo.messages_ m 
			inner join dbo.messageLists ml on m.listID = ml.listID '
if @listnames is not null
	set @sql = @sql + 'and ml.list in (' + @listnames + ') '
set @sql = @sql + '
			inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI 
				and lf.orgcode = ''' + @orgcode + '''
			left outer join lyris.trialslyris1.dbo.members_ mem
				on mem.emailaddr_ = m.hdrfromspc_ COLLATE Latin1_General_CI_AI
				and mem.list_ = lf.name COLLATE Latin1_General_CI_AI
			group by ml.list, m.hdrfromspc_, mem.fullname_, year(creatStamp_)
		) as rawdata
	PIVOT (sum(messageCount) for year in (' + @yearlist + ')) as pivottable'
exec(@sql)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPowerUsersExport order by 1'

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport
GO

