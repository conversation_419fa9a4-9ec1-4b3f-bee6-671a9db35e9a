use lyrisCustom
GO

ALTER PROC dbo.job_runDailyMaintenanceChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @errorSubject VARCHAR(300), @errmsg VARCHAR(max), @tableHTML VARCHAR(MAX), @tier VARCHAR(12);
	
	SET @tier = 'PRODUCTION'
	IF @@SERVERNAME IN ('MCDEVAG1','MCDEVAG2','MCDEV01\MEMBERCENTRAL','MCDEV01\LYRISARCHIVE')
		SET @tier = 'DEVELOPMENT'
	IF @@SERVERNAME IN ('MCBETAAG1','MCBETAAG2','MCBETA01\MEMBERCENTRAL','MCBETA01\LYRISARCHIVE')
		SET @tier = 'BETA'
	
	/* Databases with fullbackups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;

			DECLARE @fullbackupCheck_now DATETIME = GETDATE()
			DECLARE @fullbackup<PERSON>heck_defaultOldestAllowedDate DATETIME = DATEADD(day,-1,@fullbackupCheck_now)
			DECLARE @fullbackupCheck_oneMonthAgo DATETIME = DATEADD(month,-1,@fullbackupCheck_now)
			DECLARE @fullbackupCheck_firstsaturdayThisMonth DATETIME, @fullbackupCheck_firstsaturdayLastMonth DATETIME
			
			SELECT @fullbackupCheck_firstsaturdayThisMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0))
			SELECT @fullbackupCheck_firstsaturdayLastMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),-1)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),0))

			DECLARE @fullbackupCheck_oldestDateOverrides TABLE (database_name VARCHAR(100), warningDate DATETIME) 

			--trialslyris1 full backups are twice a week .... should also be one within last 4 days
			INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('trialslyris1',DATEADD(day,-4,GETDATE()))

			-- lyrisarchive fullbacks are the first saturday of the month
			-- if been at least 1 day since midnight of first saturday of month use first this Saturday, otherwise use last month's first Saturday
			IF (DATEDIFF(day,@fullbackupCheck_firstsaturdayThisMonth,@fullbackupCheck_now) >= 1 )
				INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('lyrisarchive',@fullbackupCheck_firstsaturdayThisMonth)
			ELSE 
				INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('lyrisarchive',@fullbackupCheck_firstsaturdayLastMonth)

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Full Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					LEFT OUTER JOIN @fullbackupCheck_oldestDateOverrides o
						on o.database_name = bs.database_name COLLATE Latin1_General_CI_AI
					WHERE bs.type = 'D'
					GROUP BY bs.database_name, o.warningDate
					HAVING MAX(bs.backup_finish_date) < CASE WHEN o.warningDate IS NOT NULL THEN o.warningDate ELSE @fullbackupCheck_defaultOldestAllowedDate end
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');

			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with full backups behind schedule';
				SET @errmsg = 'These are the databases where the full backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;
				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with diff backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			DECLARE @backupcheck_twodaysago DATETIME = DATEADD(hour,-2,GETDATE())

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Diff Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					WHERE bs.type = 'I'
					GROUP BY bs.database_name
					HAVING MAX(bs.backup_finish_date) < @backupcheck_twodaysago
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');

			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with full backups behind schedule';
				SET @errmsg = 'These are the databases where the diff backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;
				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with log backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			DECLARE @backupcheck_twohoursago DATETIME = DATEADD(minute,-2,GETDATE())

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Log Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					WHERE bs.type = 'L' AND bs.recovery_model = 'FULL'
					GROUP BY bs.database_name, recovery_model
					HAVING MAX(bs.backup_finish_date) < @backupcheck_twohoursago
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');

			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with log backups behind schedule';
				SET @errmsg =  'These are the databases (full recovery model only) where the log backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;
				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END
	
	/* missing applications in TrialSmith */
	BEGIN TRY
		SET XACT_ABORT OFF;
		EXEC dbo.ts_problemSitesReport;
		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO