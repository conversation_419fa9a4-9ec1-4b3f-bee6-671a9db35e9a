use trialslyris1;
GO

CREATE PROC dbo.datadog_lyrisSendingSpeedByNode
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    declare @midnightToday datetime, @oldest datetime;
    set @oldest = dateadd(minute,-1,GETDATE());

    select 'lyris.sendingSpeedByNode' as metric, 'gauge' as [type], sum(e.value) as value, 'shortname:' + m.shortname + ', nodename:' + n.nodename as tags
    from dbo.lyrPerformanceMetrics m
    inner join dbo.lyrMetricEvents e
        on e.metricKey = m.metrickey
        and m.shortname in ('mailsentgood','mailsentbad')
    inner join dbo.lyrConfigNodeSettings n
        on n.nodeID = e.nodeID
    where eventtime > @oldest
    group by n.nodename, m.shortname;


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	RETURN -1;
END CATCH
GO

GRANT EXECUTE ON [dbo].[datadog_lyrisSendingSpeedByNode] To Public
GO


