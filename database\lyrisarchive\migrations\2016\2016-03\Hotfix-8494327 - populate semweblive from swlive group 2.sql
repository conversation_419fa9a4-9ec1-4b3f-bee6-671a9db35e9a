use trialslyris1
GO

ALTER PROC dbo.[swl_updateMarketingList-Natle]
AS

SET NOCOUNT ON;

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100);
DECLARE @listName varchar(100), @now datetime;

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

create TABLE #swl_eligibleForNatleMarketingList (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(100), 
	email varchar(100),
	usernameLC_ varchar(100),
	domain_ varchar(250)
);

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

exec membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList;

insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
select platform, orgcode, memberID, membernumber, fullname, email
from membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList;

CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

update #swl_eligibleForNatleMarketingList 
set usernameLC_ = left(email,charindex('@',email)-1),
	domain_ = right(email,len(email)-charindex('@',email));

-- update fullname/association based on matching email address
update m 
set association_ = tmp.orgcode,
    fullname_ = tmp.fullname
from #swl_eligibleForNatleMarketingList tmp
inner join members_ m on m.list_ = @listName
    and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
    and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

-- mark email addresses that are NOT in temp table as expired (and not admins)
update m 
set membertype_ = 'expired',
    ExpireDate_ = @now
from members_ m 
left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
where m.list_ = @listName 
and tmp.email is null
and isListAdm_ <> 'T'
and m.membertype_ in ('normal','held');

-- reactivate previously expired email addresses that are in temp table
update m 
set membertype_ = 'normal',
    ExpireDate_ = null
from members_ m 
inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
    and m.list_ = @listName
    and m.membertype_ = 'expired';

-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
delete ec
from #swl_eligibleForNatleMarketingList ec
where exists (
	select usernameLc_, domain_
	from dbo.members_
	where list_ = @listname 
	and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
	and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
);

-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
into #swl_eligibleForNatleMarketingList2
from #swl_eligibleForNatleMarketingList;

delete from #swl_eligibleForNatleMarketingList2
where rowNum > 1;

insert into dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
from #swl_eligibleForNatleMarketingList2;

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO
