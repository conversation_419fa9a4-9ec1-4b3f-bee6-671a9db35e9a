use lyrisarchive;
GO

declare @messagesWithAttachments TABLE (messageID int PRIMARY KEY)

insert into @messagesWithAttachments (messageID)
select messageID_
from messages_
where CreatStamp_ > '12/1/2016'

intersect

select [key]
from containstable(dbo.messageSearchText,searchtext,'tsattachmentflagxxx')

update m set attachmentflag = 1
from @messagesWithAttachments a
inner join messages_ m
	on m.MessageID_ = a.messageID