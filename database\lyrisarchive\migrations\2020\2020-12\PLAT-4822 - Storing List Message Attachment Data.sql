use lyrisarchive
GO

CREATE TABLE dbo.messageAttachments (
	attachmentID int IDENTITY(1,1) NOT NULL,
	messageID int NOT NULL,
	listID int NOT NULL,
	attachedMessagePath varchar(50) NULL,
	attachmentIndex int NOT NULL,
	originalFilename varchar(255) NOT NULL,
	savedFilename varchar(100) NOT NULL,
	extension varchar(50) NOT NULL,
	bytes int NOT NULL,
 CONSTRAINT [PK_messageAttachments] PRIMARY KEY CLUSTERED 
(
	attachmentID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.messageAttachments ADD CONSTRAINT
	FK_messageAttachments_messages_ FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.messages_
	(
	messageID_
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

ALTER TABLE dbo.messageAttachments ADD CONSTRAINT
	FK_messageAttachments_messageLists FOREIGN KEY
	(
	listID
	) REFERENCES dbo.messageLists
	(
	listID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

CREATE TABLE dbo.messageAttachmentSearchText (
	attachmentTextID int IDENTITY(1,1) NOT NULL,
	attachmentID int NOT NULL,
	messageID int NOT NULL,
	searchText varchar(max) NULL,
 CONSTRAINT [PK_messageAttachmentSearchText] PRIMARY KEY CLUSTERED 
(
	attachmentTextID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.messageAttachmentSearchText ADD CONSTRAINT
	FK_messageAttachmentSearchText_messageAttachments FOREIGN KEY
	(
	attachmentID
	) REFERENCES dbo.messageAttachments
	(
	attachmentID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

ALTER TABLE dbo.messageAttachmentSearchText ADD CONSTRAINT
	FK_messageAttachmentSearchText_messages_ FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.messages_
	(
	messageID_
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO