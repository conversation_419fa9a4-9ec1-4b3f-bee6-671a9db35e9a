use lyrisarchive;
GO

ALTER PROC dbo.lyris_removeMessage
@messageid_ int,
@filelocation varchar(200)

AS

SET NOCOUNT ON;

-- queue deletion in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3;
CREATE TABLE #tmpS3 (objectKey varchar(200), size bigint);

DECLARE @sql varchar(max);
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')';
EXEC(@sql);

DECLARE @s3DeleteReadyStatusID int, @siteResourceID int;
	
select @s3DeleteReadyStatusID = qs.queueStatusID
from membercentral.platformQueue.dbo.tblQueueTypes as qt
inner join membercentral.platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
where qt.queueType = 's3Delete'
and qs.queueStatus = 'readyToProcess';

INSERT INTO membercentral.platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
SELECT @s3DeleteReadyStatusID, 'messages.membercentral.com', objectKey, getdate(), getdate()
FROM #tmpS3;

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3;

-- delete from lyris and lyrisarchive
delete from dbo.messagesToArchive
where messageid_ = @messageid_;

delete from [dbo].[messageAttachmentSearchText]
where messageid = @messageid_;

delete from [dbo].[messageAttachments]
where messageid = @messageid_;

delete from lyrisarchive.dbo.messages_ 
where messageid_ = @messageid_;

delete from dbo.messageSearchText 
where messageid_ = @messageid_;

delete from trialslyris1.dbo.messages_ 
where messageid_ = @messageid_;

RETURN 0;
GO