use lyrisArchive
GO

ALTER PROCEDURE dbo.lyris_renameArchiveAndSettings
@oldname varchar(100),
@newname varchar(100),
@filelocation varchar(200)

AS

SET NOCOUNT ON

declare @preexistingListIDForNewName int, @oldNameListID int
declare @listFormatID_NEW int, @listFormatID_OLD int, @listsLists_NEW int, @listsLists_OLD int

select @oldname = lower(@oldname)
select @newname = lower(@newname)

-- check to see if a listID already exists for new name
-- this happens when list is already active under the new name before we move archives
select @preexistingListIDForNewName = listID from dbo.messageLists where list = @newname

-- get listID for oldname
select @oldNameListID = listID from dbo.messageLists where list = @oldname

select @listFormatID_NEW = id FROM LYRIS.trialslyris1.dbo.lists_format where [name] = @newname
select @listFormatID_OLD = id FROM LYRIS.trialslyris1.dbo.lists_format where [name] = @oldname
select @listsLists_NEW = listID FROM membercentral.membercentral.dbo.lists_lists where listname = @newname
select @listsLists_OLD = listID FROM membercentral.membercentral.dbo.lists_lists where listname = @oldname


-- queue rename in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3
CREATE TABLE #tmpS3 (objectKey varchar(200))

DECLARE @sql varchar(max)
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')'
EXEC(@sql)

INSERT INTO membercentral.platformQueue.dbo.queue_S3Rename (s3bucketName, objectKey, newObjectKey)
SELECT 'messages.membercentral.com', objectKey, replace(replace(objectKey,'lyrisarchivemessages/'+@oldname+'/','lyrisarchivemessages/'+@newname+'/'),'lyrisarchivemessagecomponents/'+@oldname+'/','lyrisarchivemessagecomponents/'+@newname+'/')
FROM #tmpS3

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3


-- check list_format for NEW name
-- if there is one:  delete lists_format for OLD name, keep NEW name
-- if there is not one: rename OLD to NEW name
IF @listFormatID_NEW is not null and @listFormatID_OLD is not null
	delete from LYRIS.trialslyris1.dbo.lists_format
	where id = @listFormatID_OLD
IF @listFormatID_NEW is null and @listFormatID_OLD is not null
	update LYRIS.trialslyris1.dbo.lists_format
	set [name] = @newname
	where id = @listFormatID_OLD


-- update lyrisarchive
update dbo.messageLists 
set list = @newname
where list = @oldname

if @preexistingListIDForNewName is not null	BEGIN
	update dbo.messages_ 
	set listID = @oldNameListID
	where listID = @preexistingListIDForNewName

	delete from dbo.messagelists 
	where listID = @preexistingListIDForNewName
END

insert into dbo.messageChangeQueue (messageid_, operationid)
select messageid_, 1 as operationid
from dbo.messages_ as m
inner join dbo.messageLists ml on m.listid = ml.listid and ml.list = @newname


-- check lists_lists for NEW name
-- if there is one:  delete lists_lists for OLD name, keep NEW name
-- if there is not one: rename OLD to NEW name
IF @listsLists_NEW is not null and @listsLists_OLD is not null
	delete from membercentral.membercentral.dbo.lists_lists
	where listID = @listsLists_OLD
IF @listsLists_NEW is null and @listsLists_OLD is not null
	update membercentral.membercentral.dbo.lists_lists
	set listname = @newname
	where listID = @listsLists_OLD


SET NOCOUNT OFF

RETURN 0
GO
