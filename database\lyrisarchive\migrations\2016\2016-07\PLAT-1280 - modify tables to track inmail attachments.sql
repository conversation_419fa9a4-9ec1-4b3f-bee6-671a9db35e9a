use lyrisarchive;
GO
DROP TABLE [dbo].[inmailMessageAttachments] 
GO

TRUNCATE TABLE [dbo].[inmailMessages] 
GO

CREATE TABLE [dbo].[inmailMessageAttachments](
	[attachmentID] [int] IDENTITY(1,1) NOT NULL,
	[inmailID] [int] NOT NULL,
	[filename] [varchar](500) NOT NULL,
	[extension] [varchar](200) NULL,
	bytes int NULL,
	[attachmentKey] [uniqueIdentifier] NOT NULL CONSTRAINT DF_inmailMessageAttachments_attachmentID DEFAULT newid(),
 CONSTRAINT [PK_inmailMessageAttachments] PRIMARY KEY CLUSTERED 
(
	[attachmentID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO