use lyrisCustom
GO

ALTER PROCEDURE dbo.lyris_renameArchiveAndSettings
@oldname varchar(100),
@newname varchar(100),
@filelocation varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @oldname = @newname
		GOTO on_done;
	
	declare @messageListsListIDNew int, @messageListsListIDOld int, @listFormatIDNew int, @listFormatIDOld int,
		@listsListsNew int, @listsListsOld int, @s3RenameReadyStatusID int, @sql varchar(max);
	set @oldname = lower(@oldname);
	set @newname = lower(@newname);

	select @messageListsListIDNew = listID from lyrisArchive.dbo.messageLists where list = @newname;
	IF @messageListsListIDNew is null BEGIN
		INSERT INTO lyrisArchive.dbo.messageLists (list)
		VALUES (@newname);

		SELECT @messageListsListIDNew = SCOPE_IDENTITY();
	E<PERSON>

	select @messageListsListIDOld = listID from lyrisArchive.dbo.messageLists where list = @oldname;
	IF @messageListsListIDOld is null BEGIN
		INSERT INTO lyrisArchive.dbo.messageLists (list)
		VALUES (@oldname);

		SELECT @messageListsListIDOld = SCOPE_IDENTITY();
	END

	select @s3RenameReadyStatusID = qs.queueStatusID
	from membercentral.platformQueue.dbo.tblQueueTypes as qt
	inner join membercentral.platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
	where qt.queueType = 's3Rename'
	and qs.queueStatus = 'readyToProcess';

	-- queue rename in S3
	IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
		DROP TABLE #tmpS3;
	CREATE TABLE #tmpS3 (objectKey varchar(200), size bigint);

	SET @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + '\1.txt'' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@sql);
	SET @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + '\2.txt'' WITH (FIELDTERMINATOR = ''|'')';
	EXEC(@sql);

	INSERT INTO membercentral.platformQueue.dbo.queue_S3Rename (statusID, s3bucketName, objectKey, newObjectKey, dateAdded, dateUpdated)
	SELECT @s3RenameReadyStatusID, 'messages.membercentral.com', objectKey, replace(replace(objectKey,'lyrisarchivemessages/'+@oldname+'/','lyrisarchivemessages/'+@newname+'/'),'lyrisarchivemessagecomponents/'+@oldname+'/','lyrisarchivemessagecomponents/'+@newname+'/'),
		getdate(), getdate()
	FROM #tmpS3;

	IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
		DROP TABLE #tmpS3;


	-- check list_format for NEW name
	-- if there is one:  delete lists_format for OLD name, keep NEW name
	-- if there is not one: rename OLD to NEW name
	select @listFormatIDNew = id FROM trialslyris1.dbo.lists_format where [name] = @newname;
	select @listFormatIDOld = id FROM trialslyris1.dbo.lists_format where [name] = @oldname;

	IF @listFormatIDNew is not null and @listFormatIDOld is not null
		delete from trialslyris1.dbo.lists_format
		where id = @listFormatIDOld;

	IF @listFormatIDNew is null and @listFormatIDOld is not null
		update trialslyris1.dbo.lists_format
		set [name] = @newname
		where id = @listFormatIDOld;


	-- update lyrisarchive
	update lyrisArchive.dbo.messages_ 
	set listID = @messageListsListIDNew
	where listID = @messageListsListIDOld;

	delete from lyrisArchive.dbo.messagelists 
	where listID = @messageListsListIDOld;

	insert into lyrisArchive.dbo.messageChangeQueue (messageid_, operationid)
	select messageid_, 1 as operationid
	from lyrisArchive.dbo.messages_
	where listID = @messageListsListIDNew;


	-- check lists_lists for NEW name
	-- if there is one:  delete lists_lists for OLD name, keep NEW name
	-- if there is not one: rename OLD to NEW name
	select @listsListsNew = listID FROM membercentral.membercentral.dbo.lists_lists where listname = @newname;
	select @listsListsOld = listID FROM membercentral.membercentral.dbo.lists_lists where listname = @oldname;

	IF @listsListsNew is not null and @listsListsOld is not null begin
		delete from membercentral.membercentral.dbo.comm_lists
		where listID = @listsListsOld;

		delete from membercentral.membercentral.dbo.lists_lists
		where listID = @listsListsOld;
	end
	IF @listsListsNew is null and @listsListsOld is not null
		update membercentral.membercentral.dbo.lists_lists
		set listname = @newname
		where listID = @listsListsOld;


	on_done:

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	RETURN -1;
END CATCH
GO