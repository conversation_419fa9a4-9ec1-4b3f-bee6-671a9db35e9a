use lyrisArchive
GO

ALTER PROCEDURE dbo.lyris_renameArchiveAndSettings
@oldname varchar(100),
@newname varchar(100),
@filelocation varchar(200)

AS

SET NOCOUNT ON

declare @preexistingListIDForNewName int, @oldNameListID int

select @oldname = lower(@oldname)
select @newname = lower(@newname)

-- check to see if a listID already exists for new name
-- this happens when list is already active under the new name before we move archives
select @preexistingListIDForNewName = listID from dbo.messageLists where list = @newname

-- get listID for oldname
select @oldNameListID = listID from dbo.messageLists where list = @oldname


-- queue rename in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3
CREATE TABLE #tmpS3 (objectKey varchar(200))

DECLARE @sql varchar(max)
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')'
EXEC(@sql)

INSERT INTO membercentral.platformQueue.dbo.queue_S3Rename (s3bucketName, objectKey, newObjectKey)
SELECT 'messages.membercentral.com', objectKey, replace(objectKey,'/'+@oldname+'/','/'+@newname+'/')
FROM #tmpS3

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3


-- rename in lyris, membercentral, and lyrisarchive
update LYRIS.trialslyris1.dbo.lists_format
set [name] = @newname
where [name] = @oldname

update dbo.messageLists 
set list = @newname
where list = @oldname

if @preexistingListIDForNewName is not null	BEGIN
	update dbo.messages_ 
	set listID = @oldNameListID
	where listID = @preexistingListIDForNewName

	delete from dbo.messagelists 
	where listID = @preexistingListIDForNewName
END

insert into dbo.messageChangeQueue (messageid_, operationid)
select messageid_, 1 as operationid
from dbo.messages_ as m
inner join dbo.messageLists ml on m.listid = ml.listid and ml.list = @newname

update membercentral.membercentral.dbo.lists_lists
set listname = @newname
where listname = @oldname


SET NOCOUNT OFF

RETURN 0
GO