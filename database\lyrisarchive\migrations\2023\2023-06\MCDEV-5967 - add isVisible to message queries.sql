use lyrisCustom;
GO
ALTER PROC dbo.checkForFailedDigests
@dateoflastdigest datetime,
@daysToLookBack int,
@fixList bit,
@badlist varchar(60) OUTPUT

AS

-- whenever the overnight digest process fails, it's usually because a corrupted message cause an internal exception in lyris
-- this script will figure out which list likely failed and mark all of the messages that <PERSON><PERSON>U<PERSON> have been in that digest as "DIGESTED"
-- This way the bad message will likely be skipped the next time digests run

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @dateOfLookBackStart datetime;

	-- if didn't pass a specific date, look up most recent digest
	if @dateoflastdigest IS NULL
		select top 1 @dateoflastdigest = Created_
		from trialslyris1.dbo.outmail_
		where type_ in ('digest','mdigest','index')
		order by Created_ desc;

	if @daysToLookBack IS NULL
		set @daysToLookBack = 1;

	set @dateOfLookBackStart = dateadd(day,-1 * @daysToLookBack,@dateoflastdigest);

	select distinct top 1 @badlist = m.list_
	from trialslyris1.dbo.messages_ as m
	inner join trialslyris1.dbo.members_ as lm on lm.list_ = m.list_
	where lm.membertype_ in ('normal','held')
	and lm.SubType_ in ('digest','mimedigest','index')
	and m.CreatStamp_ between @dateOfLookBackStart and @dateoflastdigest
	and m.digested_ = 'F'
	order by m.list_;

	IF @fixList = 1 and @badlist is not null
		update trialslyris1.dbo.messages_ 
		set digested_ = 'T'
		where list_ = @badlist
		and CreatStamp_ between @dateOfLookBackStart and @dateoflastdigest
		and digested_ = 'F';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
ALTER PROC dbo.list_queueDigest

@isTestMode bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyToProcessStatusID INT, @digestDate DATETIME, @digestDateStart DATETIME, @digestDateEnd DATETIME;
	DECLARE @listsWithMCThreadIndexRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(10));
	DECLARE @listsWithMCThreadDigestRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(10));

	declare @testModeGroupCode varchar(25) = 'DigestTestModeGroup', @testModeGroupID int, @testModeGroupHasMembers bit;

	SET @digestDate = DATEADD(D,-1,GETDATE());
	SET @digestDateStart = cast(@digestDate as date);
	SET @digestDateEnd = DATEADD(MS,-3,DATEADD(d,1,@digestDateStart));

	SELECT @queueTypeID = queueTypeID
	FROM memberCentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'listDigests';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM memberCentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'ReadyToProcess';


	IF @isTestMode = 0 BEGIN

		INSERT INTO @listsWithMCThreadIndexRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadIndex = 1
			AND m.memberType_ in ('normal','held')
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadIndex = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		INSERT INTO @listsWithMCThreadDigestRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadDigest = 1
			AND m.memberType_ in ('normal','held')
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadDigest = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		END ELSE BEGIN


			select @testModeGroupID = groupID
			from membercentral.membercentral.dbo.ams_groups g
			where orgID=1 and g.groupCode = @testModeGroupCode and g.status='A'

			select @testModeGroupHasMembers = case when count(*) > 0 then 1 else 0 end
			from membercentral.membercentral.dbo.cache_members_groups mg
			where groupID = @testModeGroupID


			IF @testModeGroupHasMembers = 1 BEGIN
				INSERT INTO @listsWithMCThreadIndexRecipients(list)
				select lf.name
				from trialslyris1.dbo.lists_format lf 
				inner join membercentral.membercentral.dbo.sites s 
					on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
				inner join membercentral.membercentral.dbo.lists_lists l
					on l.listName = lf.name collate Latin1_General_CI_AI
					and l.supportsMCThreadIndex = 1
				inner join membercentral.membercentral.dbo.cms_siteResources sr
					on sr.siteResourceID = l.siteResourceID
					and sr.siteResourceStatusID=1
					group by lf.name;

				INSERT INTO @listsWithMCThreadDigestRecipients(list)
				select lf.name
				from trialslyris1.dbo.lists_format lf 
				inner join membercentral.membercentral.dbo.sites s 
					on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
				inner join membercentral.membercentral.dbo.lists_lists l
					on l.listName = lf.name collate Latin1_General_CI_AI
					and l.supportsMCThreadDigest = 1
				inner join membercentral.membercentral.dbo.cms_siteResources sr
					on sr.siteResourceID = l.siteResourceID
					and sr.siteResourceStatusID=1
					group by lf.name;
			END
		END

	IF EXISTS (select * from @listsWithMCThreadIndexRecipients) BEGIN
		UPDATE t 
		SET t.orgcode = lf.orgcode
		FROM @listsWithMCThreadIndexRecipients t
		INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
			AND lf.disabled = 0;

		-- populate platformQueue table
		INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
		select l.orgcode, ml.list, 'ListThreadIndex', @digestDate, @readyToProcessStatusID, @isTestMode
		from @listsWithMCThreadIndexRecipients l
		inner join lyrisarchive.dbo.messageLists ml on ml.list = l.list collate Latin1_General_CI_AI
		inner join lyrisarchive.dbo.messages_ m on m.listID = ml.listID
			and m.creatStamp_ between @digestDateStart and @digestDateEnd and m.isVisible=1
		group by l.orgcode, ml.list;
	END


	IF EXISTS (select * from @listsWithMCThreadDigestRecipients) BEGIN
		
		UPDATE t 
		SET t.orgcode = lf.orgcode
		FROM @listsWithMCThreadDigestRecipients t
		INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
			AND lf.disabled = 0;


		INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
		select l.orgcode, ml.list, 'ListThreadDigest', @digestDate, @readyToProcessStatusID, @isTestMode
		from @listsWithMCThreadDigestRecipients l
		inner join lyrisarchive.dbo.messageLists ml
		on ml.list = l.list collate Latin1_General_CI_AI
		inner join lyrisarchive.dbo.messages_ m	on m.listID = ml.listID
		and m.creatStamp_ between @digestDateStart and @digestDateEnd and m.isVisible=1
		group by l.orgcode, ml.list;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

use lyrisarchive;
GO

ALTER PROC dbo.dash_lists_messagePerMonth
@siteid int,
@sitecode varchar(5),
@nummonths int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @reportDate date, @startRange date;

	-- start with the end of this month
	set @reportDate = EOMONTH(GETDATE());
	set @startRange = EOMONTH(DATEADD(MONTH,@nummonths*-1,@reportDate));

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (reportMonth varchar(10), reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (reportMonth varchar(10));
	
	with yearmonthrange(yearmonthinrange) as (
		select 0
			union all
		select yearmonthinrange+1 
		from yearmonthrange
		where yearmonthinrange < @nummonths-1
	)
	insert into #tmpListCountsExpectedRows (reportmonth)
	select 
		  format(dateadd(month,-yearmonthinrange,getdate()),'yyyy-MM') as reportmonth
	from yearmonthrange

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (reportMonth, reportCount)
	select format(creatStamp_,'yyyy-MM') as reportMonth, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and format(m.creatStamp_,'yyyy-MM') > format(@startRange,'yyyy-MM')
	group by format(creatStamp_,'yyyy-MM');

	-- fill in missing expected rows
	insert into #tmpListCounts (reportMonth)
	select reportMonth
	from #tmpListCountsExpectedRows
		except
	select reportMonth
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select reportmonth as reportmonth, reportcount
		from #tmpListCounts as datarow
		order by reportmonth
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_messagePerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_newTopicsPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_topicRepliesPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_uniqueTopicRepliersPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_lists_uniqueTopicStartersPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_MC_lists_messagePerYear
@numpreviousyears int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- all lists
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.messagelists ml
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_MC_lists_newTopicsPerYear
@numpreviousyears int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- all lists
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.messagelists ml
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_MC_lists_topicRepliesPerYear
@numpreviousyears int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- all lists
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.messagelists ml
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_MC_lists_uniqueTopicRepliersPerYear
@numpreviousyears int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- all lists
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.messagelists ml
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ <> m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.dash_MC_lists_uniqueTopicStartersPerYear
@numpreviousyears int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- all lists
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.messagelists ml
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(distinct hdrfromspc_) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC [dbo].[lists_getDigestInfo]
@listname varchar(100),
@startDate datetime,
@endDate datetime

AS


declare @digestThreads TABLE (messageid_ int PRIMARY KEY, threadStartDate datetime, firstMessageInDateRange datetime, digestMessageCount int, totalMessageCount int, totalParticipantCount int, subject varchar(1000), hdrFrom varchar(200), fromAddress varchar(200), fullname varchar(500), memberNumber varchar (100))
declare @digestMessages TABLE (messageid_ int PRIMARY KEY, threadMessageID int, messagedate datetime, hdrFrom varchar(200), fromaddress varchar(200), hasAttachments bit, memberNumber varchar (100),fullname varchar(500))
declare @digestMemberPhotos TABLE (memberNumber varchar (100) PRIMARY KEY, hasPhoto bit)



insert into @digestThreads (messageid_, threadStartDate, firstMessageInDateRange, digestMessageCount, subject, hdrFrom, fromAddress)
SELECT max(m.parentid_) as parentid_, parentMessage.CreatStamp_, min(m.CreatStamp_), count(m.messageid_), parentMessage.hdrSubject_,parentMessage.HdrFrom_, parentMessage.HdrFromSpc_
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
inner join dbo.messages_ parentmessage on m.parentID_ = parentMessage.messageID_
where ml.list = @listname 
and m.creatstamp_ between @startDate and @enddate
and m.parentid_ is not null
and m.isVisible=1
group by m.parentid_, parentMessage.hdrSubject_, parentMessage.CreatStamp_, parentMessage.HdrFrom_, parentMessage.HdrFromSpc_

update t2 SET
    totalMessageCount = temp.totalMessageCount,
    totalParticipantCount = temp.totalParticipantCount
from @digestThreads t2
inner join (
    select t.messageid_, count(*) as totalMessageCount, count(distinct m.hdrfromspc_) as totalParticipantCount
    from @digestThreads t
    inner join messages_ m 
        on m.ParentID_ = t.messageid_
        and m.CreatStamp_ < @enddate
        and m.isVisible=1
    group by t.messageid_
) as temp on temp.messageid_ = t2.messageid_


update @digestThreads SET
    totalMessageCount = 0
where totalMessageCount is null

insert into @digestMessages  (messageid_, threadMessageID,messagedate, HdrFrom,  fromaddress, hasAttachments)
select m.MessageID_, t.messageid_, m.CreatStamp_, m.HdrFrom_,m.HdrFromSpc_, isnull(m.attachmentflag,0)
from @digestThreads t
inner join messages_ m
    on t.messageid_ = m.ParentID_
    and m.creatstamp_ between @startDate and @enddate
    and m.isVisible=1


update t set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestThreads t
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = t.fromaddress
    and m.List_ = @listname

update m2 set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestMessages m2
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = m2.fromaddress
    and m.List_ = @listname


select t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,
    t.totalParticipantCount, digestParticipantCount = count(distinct dm.fromaddress),
    messagesWithAttachments = sum( case when dm.hasAttachments = 1 then cast(1 as int) else cast(0 as int) end),
    numReplies = sum( case when dm.fromaddress = t.fromAddress then cast(0 as int) else cast(1 as int) end),
    participants = lyrisCustom.dbo.PipeList(distinct isnull(dm.fullname, dm.fromAddress))
from @digestThreads t
inner join @digestMessages dm
    on dm.threadMessageID = t.messageid_
group by t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,t.totalParticipantCount


select messageid_, threadMessageID , messagedate, hdrFrom, fromaddress, hasAttachments, memberNumber,fullname
from @digestMessages
order by threadMessageID, messageID_

GO

ALTER PROC dbo.lists_listActivityReportExport
@orgcode varchar(10),
@orgEmailDomain varchar(100),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport

select 
	l.name_,
	l.creatStamp_,
	l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI) as numTotalMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numStaffMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numNonStaffMembers,
	(
		select max(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.isVisible=1
		group by ml.list
	) as mostRecentMessage,
	(
		select count(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_  COLLATE Latin1_General_CI_AI
		and right(hdrFromSpc_,len(hdrFromSpc_) - charindex('@',hdrFromSpc_)) not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')
		and m.isVisible=1
		group by ml.list
	) as numMessagesInArchive
into ##tmpListActvityExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select name_, creatStamp_, descShort_, numTotalMembers, numStaffMembers, numNonStaffMembers, mostRecentMessage, numMessagesInArchive from ##tmpListActvityExport order by name_'

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport
GO

ALTER PROC dbo.lists_listEngagementByMemberReportExport
@orgcode varchar(10),
@startDate datetime = null,
@endDate datetime = null,
@filename varchar(800)

AS

/* Do not change */
if @startdate is null 
	set @startdate = '1/1/1900';

if @enddate is null 
	set @enddate = getdate();

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;

declare @uniqueLists TABLE (listID int PRIMARY KEY, list_ varchar(100), orgcode varchar(10));
declare @uniqueSenders TABLE (id int IDENTITY(1,1) PRIMARY KEY, emailaddr varchar(200), ExternalMemberID varchar(100), 
	listID int, numNewMessages int NOT NULL DEFAULT 0, numReplies int NOT NULL DEFAULT 0);

insert into @uniqueLists (listID, list_, orgcode)
select ml.listID, l.name_, 	lf.orgcode
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name
	and l.adminSend_ = 'F'
	and lf.orgcode = isnull(@orgcode,lf.orgcode)
inner join dbo.messageLists ml on ml.list = l.name_;

insert into @uniqueSenders (emailaddr, ExternalMemberID, listID)
select hdrfromspc_, max(mem.ExternalMemberID) as ExternalMemberID, ml.listID
from dbo.messages_ m
inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
	and m.creatStamp_ between @startdate and @enddate
left outer join trialslyris1.dbo.members_ mem on mem.list_ = ml.list_
	and mem.EmailAddr_ = m.HdrFromSpc_
group by hdrfromspc_, ml.listID;

update s 
set s.numNewMessages= temp.numNewMessages
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numNewMessages
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ = messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

update s 
set s.numReplies= temp.numReplies
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numReplies
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ <> messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

select ml.orgcode, ml.list_, s.emailaddr, s.ExternalMemberID, s.numNewMessages, s.numReplies 
into #tmpListEngagementbyMemberExport
from @uniqueLists ml 
inner join @uniqueSenders s on s.listID = ml.listID
order by orgcode, emailaddr;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementbyMemberExport order by orgcode, list_';

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;
GO

ALTER PROC dbo.lists_listEngagementReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;

select lf.orgcode, lf.subjecttag, l.name_, l.creatStamp_, l.descShort_, 0 as numTotalMembers, 0 as numNonSenders, 
	0 as [Original Message senders], 0 as [Reply Message Senders], 0 as [Original Message Count], 
	0 as [Reply Message Count]
into #tmpListEngagementExport
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and l.adminSend_ = 'F'
	and lf.orgcode = @orgcode;

UPDATE tmp
set tmp.numTotalMembers = (select count(*) from trialslyris1.dbo.members_ where list_ = tmp.name_ COLLATE Latin1_General_CI_AI and membertype_ in ('normal','held'))
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.numNonSenders = 
	(
		select count(*)
		from trialslyris1.dbo.members_
		where list_ = tmp.name_ COLLATE Latin1_General_CI_AI
		and membertype_ in ('normal','held')
		and emailaddr_ not in (
			select hdrfromspc_ COLLATE Latin1_General_CI_AI
			from dbo.messages_ m
			inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
				and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
				and m.creatStamp_ between @startdate and @enddate
		) 
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementExport order by orgcode, name_';

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;
GO
ALTER PROC dbo.lists_listMessagePerDayReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport

declare @listcounts TABLE (list varchar(100) PRIMARY KEY, membercount int)
insert into @listcounts (list, membercount)
select m.list_, count(*)
from lyris.trialslyris1.dbo.members_ as m
inner join lyris.trialslyris1.dbo.lists_format lf on m.list_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
where m.membertype_ in ('normal','held')
and m.list_ not in ('seminarweblive')
group by m.list_

select CONVERT(VARCHAR(10),creatStamp_,101) as date, 
	dayofweek = case datepart(dw,creatStamp_)
		when 1 then 'Sunday'
		when 2 then 'Monday'
		when 3 then 'Tuesday'
		when 4 then 'Wednesday'
		when 5 then 'Thursday'
		when 6 then 'Friday'
		when 7 then 'Saturday'
	end
	, sum(lc.membercount) totalMessagesSent, count(*) as uniqueMessageCount
	, ROW_NUMBER() OVER (ORDER BY CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)) as row
into ##tmpListPerDayExport
from messagelists ml WITH(nolock)
inner join messages_ m WITH(nolock) on m.listID = ml.listID and m.isVisible=1
	and creatStamp_ between @startDate and @endDate
inner join @listcounts lc on lc.list = ml.list
group by CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select [date], [dayofweek], uniqueMessageCount from ##tmpListPerDayExport order by row'

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport
GO

ALTER PROC dbo.lists_listMessagePerYearReportExport
@orgcode varchar(10),
@filename varchar(800)

AS

declare @minYear int, @maxYear int, @yrList varchar(200), @yrListSelect varchar(max), @pvtQry varchar(4000);

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;

CREATE TABLE #tmpData (list varchar(60), msgYear int, msgCount int);

-- get unpivoted data
INSERT INTO #tmpData (list, msgYear, msgCount)
select ml.list, year(creatStamp_), count(*)
from dbo.messages_ m 
inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
group by lf.orgcode, ml.list, year(creatStamp_);

-- get min and max years in unpivoted data
select @minYear=min(msgYear), @maxYear=max(msgYear)
from #tmpData;

-- generate all years between min and max years as a string for pivot
with yearCTE as (
	select @minYear as yr
		union all
	select yr + 1
	from yearCTE
	where yr < @maxYear
)
select @yrList = COALESCE(@yrList + ',','') + quoteName(yr), 
	@yrListSelect = COALESCE(@yrListSelect + ',','') + 'isnull(pvt.' + quoteName(yr) + ',0) as ' + quoteName(yr)
from yearCTE
order by yr;

-- pivot the data
set @pvtQry = 'select pvt.list, ' + @yrListSelect + ' 
	into ##tmpListPerYearExport
	from #tmpData as rawdata 
	PIVOT (sum(msgCount) for msgYear in (' + @yrList + ')) as pvt;';
EXEC(@pvtQry);

-- export it
EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport order by 1, 2'

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;
GO

ALTER PROC dbo.lists_listPowerUsersReportExport
@orgcode varchar(10),
@startYear varchar(4),
@includeLists varchar(max),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport

declare @optionalIncludeListFilter TABLE ( listname varchar(100));
insert into @optionalIncludeListFilter (listname)
select listitem from dbo.fn_varcharListToTable(@includeLists,',') where listitem <> ''

declare @listnames varchar(max)
SELECT @listnames = COALESCE(@listnames + ',','') + '''' + listname + '''' FROM @optionalIncludeListFilter

declare @yearlist varchar(500)
; with yearsCTE as (
	select cast(@startYear as int) as theYear
		union all
	select yearsCTE.theYear + 1 as theYear
	from yearsCTE
	where yearsCTE.theYear < year(getdate())
)
SELECT @yearlist = COALESCE(@yearlist + ',','') + quoteName(CAST(theYear AS varchar(4)))
FROM yearsCTE

declare @sql varchar(8000)
set @sql = '
	select list, hdrfromspc_, fullname_, ' + @yearlist + '
	into ##tmpListPowerUsersExport
	from 
		(
			select ml.list, m.hdrfromspc_,mem.fullname_, year(creatStamp_) as year, count(*) as messageCount
			from dbo.messages_ m 
			inner join dbo.messageLists ml on m.listID = ml.listID  and m.isVisible=1'
if @listnames is not null
	set @sql = @sql + 'and ml.list in (' + @listnames + ') '
set @sql = @sql + '
			inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI 
				and lf.orgcode = ''' + @orgcode + '''
			left outer join lyris.trialslyris1.dbo.members_ mem
				on mem.emailaddr_ = m.hdrfromspc_ COLLATE Latin1_General_CI_AI
				and mem.list_ = lf.name COLLATE Latin1_General_CI_AI
			group by ml.list, m.hdrfromspc_, mem.fullname_, year(creatStamp_)
		) as rawdata
	PIVOT (sum(messageCount) for year in (' + @yearlist + ')) as pivottable'
exec(@sql)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPowerUsersExport order by 1'

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport
GO

ALTER PROCEDURE [dbo].[lyris_messageViewer]
@messageid int,
@lists varchar(max),
@AttachedMessage varchar(max),
@AttachmentID int,
@AttachmentPath varchar(100)

AS

SET NOCOUNT ON

-- global vars
DECLARE @tmpIndex int

-- get message info based on msgID and lists
DECLARE @qry varchar(max)
SELECT @qry = 'select top 1 messageid_, hdrall_, body_, list_ FROM messages_ '
SELECT @qry = @qry + 'where messageid_ = ' + CAST(@messageid as varchar(10)) + ' '
SELECT @qry = @qry + 'and list_ in (' + @lists + ')  and isVisible=1'

-- put message into temp table
CREATE TABLE #tmpMessage (
	tblmessageid_ int, 
	tblhdrall_ varchar(max),
	tblbody_ varchar(max),
	tbllist_ varchar(60),
	spMimeMessage varchar(max) NULL,
	objsubject varchar(200) NULL,
	objnumattachedmessages int NULL,
	objnumattachments int NULL,
	objnumto int NULL,
	objnumcc int NULL,
	objmsgfrom varchar(200) NULL,
	objmsgfromaddress varchar(200) NULL,
	objemaildate datetime NULL,
	objhtmlbody varchar(max) NULL,
	objtextbody varchar(max) NULL
)
INSERT INTO #tmpMessage (tblmessageid_, tblhdrall_, tblbody_, tbllist_)
EXEC(@qry)

-- Create holding tables
DECLARE @tmpAttachments TABLE (attfilename varchar(500), attfilesize bigint)
DECLARE @tmpAttachedMsgs TABLE (subject varchar(200))
DECLARE @tmpAttachedMsgList TABLE (autoID int IDENTITY(1,1), attachID int)

-- set unlock code for mailman
DECLARE @unlockCode varchar(50)
SELECT @unlockCode = 'ATrialSmithMAIL_CaMM1czz6T3w'

-- create objects and unlock component
DECLARE @objMail int, @objWebMail int, @isUnlocked int
EXEC sp_OACreate 'ChilkatWebMail2.WebEmail2', @objMail OUTPUT
EXEC sp_OACreate 'ChilkatWebMail2.WebMailMan2', @objWebMail OUTPUT
EXEC sp_OAMethod @objWebMail, 'UnlockComponent', @isUnlocked OUTPUT, @unlockCode

-- construct whole message
DECLARE @lf char(1), @crlf char(2)
SELECT @lf = char(10)
SELECT @crlf = char(13) + @lf
UPDATE #tmpMessage SET spMimeMessage = replace(replace(tblhdrall_ + @crlf + @crlf + tblbody_,@crlf,@lf),@lf,@crlf)

-- set from mime text
DECLARE @MimeMessage varchar(max)
SELECT @MimeMessage = spMimeMessage FROM #tmpMessage
EXEC sp_OAMethod @objMail, 'setFromMimeText', NULL, @MimeMessage

-- if attachedmessage is not null, get the real message to show
IF @AttachedMessage IS NOT NULL
BEGIN
	-- put list into table
	INSERT INTO @tmpAttachedMsgList (AttachID)
	SELECT ListItem
	FROM dbo.fn_NumberListToTable(@AttachedMessage)

	-- loop over list to get the final attached message
	DECLARE @minAutoID int, @realAttachID int, @newobjMail int
	SELECT @minAutoID = min(autoID) from @tmpAttachedMsgList
	WHILE @minAutoID IS NOT NULL
	BEGIN
		SELECT @realAttachID = attachID from @tmpAttachedMsgList where autoID = @minAutoID
		
		EXEC sp_OAMethod @objMail, 'GetAttachedMessage', @newobjMail OUTPUT, @realAttachID
		SELECT @objMail = @newobjMail
	
		SELECT @minAutoID = min(autoID) from @tmpAttachedMsgList WHERE autoID > @minAutoID
	END
END

-- if attachmentID is null, we are getting the message info
IF @AttachmentID IS NULL
BEGIN
	-- get properties
	DECLARE @subject varchar(200), @numattachedmessages int, @numattachments int,
		@msgfrom varchar(200), @numto int, @numcc int, @msgfromaddress varchar(200),
		@emaildate datetime
	EXEC sp_OAGetProperty @objMail, 'subject', @subject OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numattachedmessages', @numattachedmessages OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numattachments', @numattachments OUTPUT
	EXEC sp_OAGetProperty @objMail, 'from', @msgfrom OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numto', @numto OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numcc', @numcc OUTPUT
	EXEC sp_OAGetProperty @objMail, 'fromaddress', @msgfromaddress OUTPUT
	EXEC sp_OAGetProperty @objMail, 'emaildate', @emaildate OUTPUT

	-- get properties that are really methods
	DECLARE @tmpMessageHTML TABLE (htmlbody varchar(max))
		INSERT INTO @tmpMessageHTML (htmlbody)
		EXEC sp_OAMethod @objMail, 'gethtmlbody'
	DECLARE @tmpMessageTEXT TABLE (textbody varchar(max))
		INSERT INTO @tmpMessageTEXT (textbody)
		EXEC sp_OAMethod @objMail, 'getplaintextbody'

	-- update message table
	UPDATE #tmpMessage 
	set objsubject = @subject, 
		objnumattachedmessages = @numattachedmessages,
		objnumattachments = @numattachments,
		objmsgfrom = @msgfrom,
		objnumto = @numto,
		objnumcc = @numcc,
		objmsgfromaddress = @msgfromaddress,
		objemaildate = @emaildate,
		objhtmlbody = (select htmlbody from @tmpMessageHTML),
		objtextbody = (select textbody from @tmpMessageTEXT)

	-- get the attachments
	DECLARE @attfilename varchar(500), @attfilesize bigint
	SELECT @tmpIndex = 0
	WHILE @tmpIndex < @numattachments
	BEGIN
		EXEC sp_OAMethod @objMail, 'GetAttachmentFilename', @attfilename OUTPUT, @tmpIndex
		EXEC sp_OAMethod @objMail, 'GetAttachmentSize', @attfilesize OUTPUT, @tmpIndex

		INSERT INTO @tmpAttachments (attfilename, attfilesize)
		VALUES(@attfilename, @attfilesize)

		SELECT @tmpIndex = @tmpIndex + 1
	END

	-- get the attached messages
	DECLARE @attEmail int
	SELECT @tmpIndex = 0
	WHILE @tmpIndex < @numattachedmessages
	BEGIN
		EXEC sp_OAMethod @objMail, 'getattachedmessage', @attEmail OUTPUT, @tmpIndex
			
		INSERT INTO @tmpAttachedMsgs (subject)
		EXEC sp_OAGetProperty @attEmail, 'subject'

		SELECT @tmpIndex = @tmpIndex + 1
	END

	-- Query 1: message table
	SELECT tblmessageid_, tbllist_, objsubject, objnumto, objnumcc, 
		objmsgfrom, objmsgfromaddress, objemaildate, objhtmlbody, objtextbody
	from #tmpMessage

	-- Query 2: attachments
	SELECT attfilename, attfilesize from @tmpAttachments

	-- Query 3: attached messages
	SELECT subject from @tmpAttachedMsgs
END

-- if attachmentID is gte 0, save and return the attachment info
IF @AttachmentID >= 0
BEGIN
	DECLARE @savedFile int	
	EXEC sp_OAMethod @objMail, 'SaveAttachedFile', @savedFile OUTPUT, @AttachmentID, @AttachmentPath

	-- get the attachments
	EXEC sp_OAMethod @objMail, 'GetAttachmentFilename', @attfilename OUTPUT, @AttachmentID
	EXEC sp_OAMethod @objMail, 'GetAttachmentSize', @attfilesize OUTPUT, @AttachmentID

	INSERT INTO @tmpAttachments (attfilename, attfilesize)
	VALUES(@attfilename, @attfilesize)	

	-- Query 1: attachments
	SELECT attfilename, attfilesize from @tmpAttachments
END

-- drop temp tables
DROP TABLE #tmpMessage

-- clear objects
EXEC sp_OADestroy @objWebMail
EXEC sp_OADestroy @objMail
GO

ALTER PROCEDURE [dbo].[lyris_ThreadedView2]
@startrow int = 1,
@top int = 100,
@listname varchar(60) = '',
@startdate smalldatetime = NULL

as

SET NOCOUNT ON

-- set startdate if null passed in so we dont have to duplicate queries below
if (@startdate is null) 
	SELECT @startdate = '1-1-1995'

-- set this range
DECLARE @startWithTop int, @firstMessageIDForPage int
SELECT @startWithTop = @top + @startrow

DECLARE @tmpMessages TABLE (messageid_ int, parentid_ int, numMessages int, progressiveCount int)

SET ROWCOUNT @startWithTop

INSERT INTO @tmpMessages (messageid_, parentId_, numMessages, progressiveCount)
SELECT max(m.messageid_) as messageID_, max(m.parentid_) as parentid_, count(m.messageid_) as nummessages, 0
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
where ml.list = @listname 
and m.creatstamp_ >= @startdate
and m.parentid_ is not null
and m.isVisible=1
group by m.parentid_
order by messageid_ desc

SET ROWCOUNT 0

-- update progressive message count field
UPDATE tmp
SET progressiveCount = (SELECT sum(nummessages) FROM @tmpMessages as t2 WHERE t2.messageID_ >= tmp.messageID_)
FROM @tmpMessages as tmp


-- remove threads from previous paging calls 
delete from @tmpMessages where progressiveCount < @startrow

-- get first MessageID for this page
select top 1 @firstMessageIDForPage = messageid_ 
from @tmpMessages 
order by progressiveCount

-- remove threads that will be on subsequent paging calls, keeping at least the first thread
delete from @tmpMessages 
where progressiveCount > @startWithTop 
and messageid_ <> @firstMessageIDForPage


-- return joined query
SELECT 1 as orderNode, tmp.messageid_ as tmpMessageID, m.messageid_, m.parentid_, m.creatstamp_, m.hdrsubject_, m.hdrfrom_, m.hdrfromspc_, m.attachmentflag 
from messages_ m
inner join dbo.messageLists as ml on ml.listid = m.listid and m.isVisible=1
INNER JOIN @tmpMessages tmp ON m.messageid_ = tmp.parentid_ AND m.messageID_ = m.parentID_
WHERE ml.list = @listname
union
SELECT 2 as orderNode, tmp.messageid_ as tmpMessageID, m.messageid_, m.parentid_, m.creatstamp_, m.hdrsubject_, m.hdrfrom_, m.hdrfromspc_, m.attachmentflag 
from messages_ m
inner join dbo.messageLists as ml on ml.listid = m.listid and m.isVisible=1
INNER JOIN @tmpMessages tmp ON m.parentid_ = tmp.parentid_ AND m.messageid_ <> m.parentid_
WHERE ml.list = @listname
and creatstamp_ >= @startdate
ORDER BY tmpMessageID desc, orderNode asc, m.messageid_ asc

SET NOCOUNT OFF
GO

ALTER PROCEDURE [dbo].[mc_getSingleThreadXML]
@listname varchar(60),
@messageID int

as

SET NOCOUNT ON

DECLARE @messagesXML varchar(max);
DECLARE @headXML varchar(max);
DECLARE @messageCount int;

SELECT @messageCount = count(*)
FROM	messages_ m
	INNER JOIN messages_ p on m.parentID_ = p.parentID_ and p.isVisible=1
WHERE	m.messageID_ = @messageID and m.isVisible=1


select @headXML = '<rows parent="0" pos="0" total_count="' + cast(@messageCount as varchar(10)) + '"><head>
			<column align="left" sort="na" type="datetime" width="150">Date</column>
			<column align="left" sort="na" type="ro" width="150">Contributor</column>
			<column align="center" sort="na" type="img" width="25">#cspan</column>
			<column align="left" sort="na" type="tree" width="*">Subject</column>
			<beforeInit>
				<call command="setImagePath"><param>/assets/common/javascript/dhtmlxgrid/imgs/</param></call>
				<call command="setSkin"><param>modern</param></call>
				<call command="enableColumnMove"><param>false</param></call>
				<call command="enableRowsHover"><param>true</param><param>grid_hover</param></call>
				<call command="attachEvent"><param>onXLS</param><param>mcg_xls</param></call>
				<call command="attachEvent"><param>onXLE</param><param>mcg_xle</param></call>
				<call command="enableSmartRendering"><param>true</param></call>
			</beforeInit>
			<settings>
				<colwidth>px</colwidth>
			</settings>
		</head>';


 select @messagesXML = isNull(((
	select
		thread.messageID_ as "@id",
		'tsAppBodyText' as "@class",
		1 as "@open",
		'tsAppBodyText' as "mc_timecell/@class",
		thread.creatStamp_ as "mc_timecell",
		'tsAppBodyText' as "mc_fromcell/@class",
		thread.hdrfrom_ as "mc_fromcell",
		"mc_attachmentcell" = case when thread.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
		'tsAppBodyText' as "mc_subjectcell/@class",
		thread.hdrSubject_ as "mc_subjectcell/@title",
		'<a href="javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')">' + thread.hdrSubject_ + '</a>' as "mc_subjectcell",
		(
			select
				m.messageID_ as "@id",
				'tsAppBodyText' as "@class",
				'tsAppBodyText' as "mc_timecell/@class",
				m.creatStamp_ as "mc_timecell",
				'tsAppBodyText' as "mc_fromcell/@class",
				m.hdrfrom_ as "mc_fromcell",
				mc_attachmentcell = case when m.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
				'tsAppBodyText' as "mc_subjectcell/@class",
				m.hdrSubject_ as "mc_subjectcell/@title",
				'<a href="javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')">' + m.hdrSubject_ + '</a>' as "mc_subjectcell"
			from messages_ m
			inner join messageLists ml
				on m.listID = ml.listID
				and ml.list = @listname
				and m.parentID_ = thread.messageID_
				and m.parentID_ <> m.messageID_
				order by m.creatStamp_
				for xml path('row'), type)
			
	FROM messages_ thread
		INNER JOIN messageLists threadml
			ON thread.listID = threadml.listID
			AND threadml.list = @listname
			AND thread.messageID_ = thread.parentID_
		INNER JOIN messages_ p on thread.parentID_ = p.parentID_ and p.isVisible=1
	WHERE	thread.messageID_ = @messageID and thread.isVisible=1
	ORDER BY thread.creatStamp_ desc
	for xml path('row'), root('rows')
)),'<rows></rows>')

select @messagesXML = replace(@messagesXML, '<rows>', @headXML);

select convert(xml,dbo.fn_RegExReplace(@messagesXML,'(mc_attachmentcell|mc_timecell|mc_fromcell|mc_subjectcell)','cell')) as messagesXML

SET NOCOUNT OFF
GO

ALTER PROCEDURE [dbo].[mc_getThreadedXML]
@listname varchar(60),
@startdate smalldatetime = null

as

SET NOCOUNT ON

-- set startdate if null passed in so we dont have to duplicate queries below
if (@startdate is null) 
	SELECT @startdate = '1-1-2008'

DECLARE @messagesXML varchar(max);
DECLARE @headXML varchar(max);
DECLARE @messageCount int;

select @messageCount = count(*)
from dbo.messages_ m
inner join dbo.messageLists ml on m.listID = ml.listID
	and ml.list = @listname
	and m.creatStamp_ > @startdate
	and m.isVisible=1
option(recompile)

select @headXML = '<rows parent="0" pos="0" total_count="' + cast(@messageCount as varchar(10)) + '"><head>
			<column align="left" sort="na" type="ro" width="150">Date</column>
			<column align="left" sort="na" type="ro" width="150">Contributor</column>
			<column align="center" sort="na" type="img" width="25">#cspan</column>
			<column align="left" sort="na" type="tree" width="*">Subject</column>
			<settings>
				<colwidth>px</colwidth>
			</settings>
		</head>';

IF OBJECT_ID('tempdb..#tmpThread') IS NOT NULL 
	DROP TABLE #tmpThread

select thread.messageID_, dbo.fn_RegExReplace(thread.hdrSubject_,'[^\x20-\x7E]','') as hdrSubject_
into #tmpThread
from dbo.messages_ thread
inner join dbo.messageLists threadml on thread.listID = threadml.listID
	and threadml.list = @listname
	and thread.creatStamp_ > @startdate
	and thread.isVisible=1
order by thread.creatStamp_

select @messagesXML = isNull(((
     select
		thread.messageID_ as "@id",
		'tsAppBodyText' as "@class",
		1 as "@open",
		'tsAppBodyText' as "mc_timecell/@class",
		CONVERT(VARCHAR(10),thread.creatStamp_,101) + ' ' + RIGHT(CONVERT(VARCHAR,thread.creatStamp_,100),7) as "mc_timecell",
		'tsAppBodyText' as "mc_fromcell/@class",
		thread.hdrfrom_ as "mc_fromcell",
		"mc_attachmentcell" = case when thread.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
		'tsAppBodyText' as "mc_subjectcell/@class",
		tmp.hdrSubject_ as "mc_subjectcell/@title",
		'<a href="javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')">' + tmp.hdrSubject_ + '</a>' as "mc_subjectcell",
		(
			select
				m.messageID_ as "@id",
				'tsAppBodyText' as "@class",
				'tsAppBodyText' as "mc_timecell/@class",
				CONVERT(VARCHAR(10),m.creatStamp_,101) + ' ' + RIGHT(CONVERT(VARCHAR,m.creatStamp_,100),7) as "mc_timecell",
				'tsAppBodyText' as "mc_fromcell/@class",
				m.hdrfrom_ as "mc_fromcell",
				mc_attachmentcell = case when m.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
				'tsAppBodyText' as "mc_subjectcell/@class",
				tmpM.hdrSubject_ as "mc_subjectcell/@title",
				'<a href="javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')">' + tmpM.hdrSubject_ + '</a>' as "mc_subjectcell"
            from #tmpThread as tmpM
			inner join messages_ as m on m.messageID_ = tmpM.messageID_
			where m.parentID_ = thread.messageID_
			and m.parentID_ <> m.messageID_
			and m.isVisible=1
			order by m.creatStamp_
			for xml path('row'), type)

	from #tmpThread as tmp
	inner join messages_ as thread on thread.messageID_ = tmp.messageID_
	where thread.messageID_ = thread.parentID_ and thread.isVisible=1

	order by thread.creatStamp_ desc
	for xml path('row'), root('rows')
)),'<rows></rows>')
option(recompile)

IF OBJECT_ID('tempdb..#tmpThread') IS NOT NULL 
	DROP TABLE #tmpThread

select @messagesXML = replace(@messagesXML, '<rows>', @headXML);
select convert(xml,dbo.fn_RegExReplace(@messagesXML,'(mc_attachmentcell|mc_timecell|mc_fromcell|mc_subjectcell)','cell')) as messagesXML

SET NOCOUNT OFF
GO
