use lyrisCustom
GO

ALTER DATABASE lyrisCustom SET TRUSTWORTHY ON
GO
ALTER AUTHORIZATION ON DATABASE::lyrisCustom TO [sa];
GO
ALTER DATABASE [lyrisCustom] SET ENABLE_BROKER WITH NO_WAIT;
GO

CREATE TABLE [dbo].[sb_ServiceBrokerErrorLogs](
	[LogID] [bigint] IDENTITY(1,1) NOT NULL,
	[LogDate] [datetime] NOT NULL,
	[ErrorMessage] [nvarchar](2048) NULL,
	[MessageBody] [varbinary](max) NULL,
 CONSTRAINT [PK_sb_ServiceBrokerErrorLogs] PRIMARY KEY CLUSTERED 
(
	[LogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[sb_ServiceBrokerErrorLogs] ADD  DEFAULT (getdate()) FOR [LogDate]
GO


CREATE QUEUE OpenTrackingQueue;
GO
CREATE MESSAGE TYPE [OpenTrackingQueue/OpenTrackingRequest] VALIDATION = WELL_FORMED_XML;
CREATE MESSAGE TYPE [OpenTrackingQueue/OpenTrackingResponse] VALIDATION = WELL_FORMED_XML;
GO
CREATE CONTRACT [OpenTrackingQueue/OpenTrackingContract] ([OpenTrackingQueue/OpenTrackingRequest] SENT BY INITIATOR, [OpenTrackingQueue/OpenTrackingResponse] SENT BY TARGET);
GO
CREATE SERVICE [OpenTrackingQueue/OpenTrackingService] ON QUEUE OpenTrackingQueue ([OpenTrackingQueue/OpenTrackingContract]);
GO

CREATE QUEUE ClickTrackingQueue;
GO
CREATE MESSAGE TYPE [ClickTrackingQueue/ClickTrackingRequest] VALIDATION = WELL_FORMED_XML;
CREATE MESSAGE TYPE [ClickTrackingQueue/ClickTrackingResponse] VALIDATION = WELL_FORMED_XML;
GO
CREATE CONTRACT [ClickTrackingQueue/ClickTrackingContract] ([ClickTrackingQueue/ClickTrackingRequest] SENT BY INITIATOR, [ClickTrackingQueue/ClickTrackingResponse] SENT BY TARGET);
GO
CREATE SERVICE [ClickTrackingQueue/ClickTrackingService] ON QUEUE ClickTrackingQueue ([ClickTrackingQueue/ClickTrackingContract]);
GO

ALTER QUEUE OpenTrackingQueue WITH POISON_MESSAGE_HANDLING (STATUS = OFF);
GO
ALTER QUEUE ClickTrackingQueue WITH POISON_MESSAGE_HANDLING (STATUS = OFF);
GO


CREATE PROC dbo.up_ErrorHandler

AS

DECLARE @errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int;

SELECT	@errmsg = error_message(), @severity = error_severity(), @state = error_state(), 
		@errno = error_number(), @proc = error_procedure(), @lineno = error_line();
   
IF @errmsg NOT LIKE '***%'
	SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + ', Line ' + ltrim(str(@lineno)) + '. Errno ' + ltrim(str(@errno)) + ': ' + @errmsg;

RAISERROR('%s', @severity, @state, @errmsg); -- use this approach in case errmsg has a percent character

on_done:
RETURN 0;
GO

CREATE PROC dbo.openTracking_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [OpenTrackingQueue/OpenTrackingService]
		TO SERVICE N'OpenTrackingQueue/OpenTrackingService'
		ON CONTRACT [OpenTrackingQueue/OpenTrackingContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal 
		MESSAGE TYPE [OpenTrackingQueue/OpenTrackingRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.clickTracking_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [ClickTrackingQueue/ClickTrackingService]
		TO SERVICE N'ClickTrackingQueue/ClickTrackingService'
		ON CONTRACT [ClickTrackingQueue/ClickTrackingContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal 
		MESSAGE TYPE [ClickTrackingQueue/ClickTrackingRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.openTracking_track
@ipNumber int, 
@memberID int,
@messageID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, GroupID_)
		values (@ipNumber, @memberID, @messageID, getdate(), 0);

		UPDATE trialslyris1.dbo.members_ 
		SET ReadsHtml_ = 'T' 
		WHERE MemberID_ = @memberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.clickTracking_track
@ipNumber int, 
@memberID int,
@messageID int,
@groupID int,
@linkID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, GroupID_)
		SELECT @ipNumber, @memberID, @messageID, getdate(), @groupID
		WHERE NOT EXISTS ( 
			SELECT 1 
			FROM trialslyris1.dbo.clicktracking_ 
			WHERE IPAddress_= @ipNumber
			AND MemberID_ = @memberID
			AND MessageID_ = @messageID
			AND GroupID_ = @groupID
			AND StreamWebPageName_ IS NULL 
			AND UrlID_ IS NULL
		); 

		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, UrlID_, GroupID_)
		values (@ipNumber, @memberID, @messageID, getdate(), @linkID, @groupID);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.openTracking_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
		@xmldata xml, @memberID int, @ErrorMessage nvarchar(2048), @ErrorNumber int,
		@DialogHandleFinal uniqueidentifier, @xmldataFinal xml, @ipNumber int, @messageID int;

WHILE 1 = 1
BEGIN TRY

	SELECT	@DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @ipNumber = NULL,  
			@memberID = NULL, @ErrorMessage = NULL, @ErrorNumber = NULL, @xmldataFinal = NULL, @messageID = NULL;

	BEGIN TRANSACTION;

	WAITFOR (
		RECEIVE TOP (1) @DialogHandle = conversation_handle,
						@MessageType = message_type_name,
						@MessageBody = message_body
		FROM dbo.OpenTrackingQueue
	), TIMEOUT 1000;

	IF @DialogHandle IS NULL BEGIN
		COMMIT TRANSACTION;
		BREAK;
	END

	IF @MessageType = N'OpenTrackingQueue/OpenTrackingRequest' BEGIN
		BEGIN TRY
			SET @xmldata = cast(@MessageBody as xml);

			SELECT	@ipNumber = @xmldata.value('(/l/@i)[1]','int'),
					@memberID = @xmldata.value('(/l/@m)[1]','int'),
					@messageID = @xmldata.value('(/l/@g)[1]','int');

			IF @messageID is not null AND @memberID is not null
				EXEC dbo.openTracking_track @ipNumber=@ipNumber, @memberID=@memberID, @messageID=@messageID;

			END CONVERSATION @DialogHandle;
		END TRY		
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			SET @ErrorMessage = N'openTracking_Activated - ' + ERROR_MESSAGE();
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
			END CONVERSATION @DialogHandle;
		END CATCH
	END
	ELSE BEGIN
		IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
			END CONVERSATION @DialogHandle;
		END 
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
				SET @xmldata = cast(@MessageBody as xml);						
				with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
				select @ErrorMessage = N'openTracking_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				SET @ErrorMessage = N'openTracking_Activated - Unexpected message type received: ' + @MessageType; 
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
		END
	END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.clickTracking_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
		@xmldata xml, @memberID int, @ErrorMessage nvarchar(2048), @ErrorNumber int,
		@DialogHandleFinal uniqueidentifier, @xmldataFinal xml, @ipNumber int, @messageID int,
		@groupID int, @linkID int;

WHILE 1 = 1
BEGIN TRY

	SELECT	@DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @ipNumber = NULL,  
			@memberID = NULL, @ErrorMessage = NULL, @ErrorNumber = NULL, @xmldataFinal = NULL, @messageID = NULL,
			@groupID = NULL, @linkID = NULL;

	BEGIN TRANSACTION;

	WAITFOR (
		RECEIVE TOP (1) @DialogHandle = conversation_handle,
						@MessageType = message_type_name,
						@MessageBody = message_body
		FROM dbo.OpenTrackingQueue
	), TIMEOUT 1000;

	IF @DialogHandle IS NULL BEGIN
		COMMIT TRANSACTION;
		BREAK;
	END

	IF @MessageType = N'ClickTrackingQueue/ClickTrackingRequest' BEGIN
		BEGIN TRY
			SET @xmldata = cast(@MessageBody as xml);

			SELECT	@ipNumber = @xmldata.value('(/l/@i)[1]','int'),
					@memberID = @xmldata.value('(/l/@m)[1]','int'),
					@messageID = @xmldata.value('(/l/@g)[1]','int'),
					@groupID = @xmldata.value('(/l/@p)[1]','int'),
					@linkID = @xmldata.value('(/l/@k)[1]','int');

			IF @messageID is not null AND @memberID is not null
				EXEC dbo.clickTracking_track @ipNumber=@ipNumber, @memberID=@memberID, @messageID=@messageID, @groupID=@groupID, @linkID=@linkID;

			END CONVERSATION @DialogHandle;
		END TRY		
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			SET @ErrorMessage = N'clickTracking_Activated - ' + ERROR_MESSAGE();
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
			END CONVERSATION @DialogHandle;
		END CATCH
	END
	ELSE BEGIN
		IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
			END CONVERSATION @DialogHandle;
		END 
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
				SET @xmldata = cast(@MessageBody as xml);						
				with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
				select @ErrorMessage = N'clickTracking_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				SET @ErrorMessage = N'clickTracking_Activated - Unexpected message type received: ' + @MessageType; 
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
		END
	END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

ALTER QUEUE OpenTrackingQueue WITH
      STATUS = ON,
      ACTIVATION (STATUS = ON,
                  PROCEDURE_NAME = dbo.openTracking_Activated,
                  MAX_QUEUE_READERS = 1,
                  EXECUTE AS OWNER)
GO
ALTER QUEUE ClickTrackingQueue WITH
      STATUS = ON,
      ACTIVATION (STATUS = ON,
                  PROCEDURE_NAME = dbo.clickTracking_Activated,
                  MAX_QUEUE_READERS = 1,
                  EXECUTE AS OWNER)
GO

