use lyrisCustom
GO

ALTER PROC dbo.clickTracking_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
		@xmldata xml, @memberID int, @ErrorMessage nvarchar(2048), @ErrorNumber int,
		@DialogHandleFinal uniqueidentifier, @xmldataFinal xml, @ipNumber int, @messageID int,
		@groupID int, @linkID int;

WHILE 1 = 1
BEGIN TRY

	SELECT	@DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @ipNumber = NULL,  
			@memberID = NULL, @ErrorMessage = NULL, @ErrorNumber = NULL, @xmldataFinal = NULL, @messageID = NULL,
			@groupID = NULL, @linkID = NULL;

	BEGIN TRANSACTION;

	WAITFOR (
		RECEIVE TOP (1) @DialogHandle = conversation_handle,
						@MessageType = message_type_name,
						@MessageBody = message_body
		FROM dbo.ClickTrackingQueue
	), TIMEOUT 1000;

	IF @DialogHandle IS NULL BEGIN
		COMMIT TRANSACTION;
		BREAK;
	END

	IF @MessageType = N'ClickTrackingQueue/ClickTrackingRequest' BEGIN
		BEGIN TRY
			SET @xmldata = cast(@MessageBody as xml);

			SELECT	@ipNumber = @xmldata.value('(/l/i)[1]','int'),
					@memberID = @xmldata.value('(/l/m)[1]','int'),
					@messageID = @xmldata.value('(/l/g)[1]','int'),
					@groupID = @xmldata.value('(/l/p)[1]','int'),
					@linkID = @xmldata.value('(/l/k)[1]','int');

			IF @messageID is not null AND @memberID is not null
				EXEC dbo.clickTracking_track @ipNumber=@ipNumber, @memberID=@memberID, @messageID=@messageID, @groupID=@groupID, @linkID=@linkID;

			END CONVERSATION @DialogHandle;
		END TRY		
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			SET @ErrorMessage = N'clickTracking_Activated - ' + ERROR_MESSAGE();
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
			END CONVERSATION @DialogHandle;
		END CATCH
	END
	ELSE BEGIN
		IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
			END CONVERSATION @DialogHandle;
		END 
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
				SET @xmldata = cast(@MessageBody as xml);						
				with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
				select @ErrorMessage = N'clickTracking_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				SET @ErrorMessage = N'clickTracking_Activated - Unexpected message type received: ' + @MessageType; 
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
		END
	END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.openTracking_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
		@xmldata xml, @memberID int, @ErrorMessage nvarchar(2048), @ErrorNumber int,
		@DialogHandleFinal uniqueidentifier, @xmldataFinal xml, @ipNumber int, @messageID int;

WHILE 1 = 1
BEGIN TRY

	SELECT	@DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @ipNumber = NULL,  
			@memberID = NULL, @ErrorMessage = NULL, @ErrorNumber = NULL, @xmldataFinal = NULL, @messageID = NULL;

	BEGIN TRANSACTION;

	WAITFOR (
		RECEIVE TOP (1) @DialogHandle = conversation_handle,
						@MessageType = message_type_name,
						@MessageBody = message_body
		FROM dbo.OpenTrackingQueue
	), TIMEOUT 1000;

	IF @DialogHandle IS NULL BEGIN
		COMMIT TRANSACTION;
		BREAK;
	END

	IF @MessageType = N'OpenTrackingQueue/OpenTrackingRequest' BEGIN
		BEGIN TRY
			SET @xmldata = cast(@MessageBody as xml);

			SELECT	@ipNumber = @xmldata.value('(/l/i)[1]','int'),
					@memberID = @xmldata.value('(/l/m)[1]','int'),
					@messageID = @xmldata.value('(/l/g)[1]','int');

			IF @messageID is not null AND @memberID is not null
				EXEC dbo.openTracking_track @ipNumber=@ipNumber, @memberID=@memberID, @messageID=@messageID;

			END CONVERSATION @DialogHandle;
		END TRY		
		BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
			SET @ErrorMessage = N'openTracking_Activated - ' + ERROR_MESSAGE();
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
			END CONVERSATION @DialogHandle;
		END CATCH
	END
	ELSE BEGIN
		IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
			END CONVERSATION @DialogHandle;
		END 
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
				SET @xmldata = cast(@MessageBody as xml);						
				with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
				select @ErrorMessage = N'openTracking_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				SET @ErrorMessage = N'openTracking_Activated - Unexpected message type received: ' + @MessageType; 
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END
		END
	END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

