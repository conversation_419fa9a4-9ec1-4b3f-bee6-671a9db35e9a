USE trialslyris1
GO

ALTER PROC dbo.job_enforceListSettings
@listName varchar(60) = NULL

AS

-- turn off join by email for all lists
update dbo.lists_
set NoEmailSub_ = 'T'
where NoEmailSub_ <> 'T'
and name_ = isnull(@listName,name_);

-- force all lists to only allow admins to add members
update dbo.lists_ 
set security_ = 'private'
where security_ = 'open' 
and name_ not in ('eclips_js','brandigy')
and name_ = isnull(@listName,name_);

-- set all lists to invisible in Discussion Forum Interface
update dbo.lists_ 
set MriVisibility_ = 'I'
where MriVisibility_ <> 'I'
and name_ = isnull(@listName,name_);

-- update maxmesssize to 50000 for lists using offloadattachments with groovy script
update l 
set l.maxmessSiz_ = 50000
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where lf.offloadAttachments = 1 
and lf.disableHTML=0
and cast(l.pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
and l.maxmessSiz_ <> 50000
and l.name_ = isnull(@listName,l.name_);

-- update maxmesssize to 1500 for discussion lists not using offloadattachments
update l 
set l.maxmessSiz_ = 1500
from dbo.lists_ l
inner join (
	select name_
	from dbo.lists_
	where maxmessSiz_ <> 1500 and adminsend_ = 'F'
		except
	select name_
	from dbo.lists_ l2
	inner join dbo.lists_format lf on lf.name = l2.name_
	where lf.offloadAttachments = 1 
	and disableHTML=0
	and cast(pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
	) as temp on temp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- turn on advanced scripting for all lists using HTMLConversion
update l 
set l.MergeCapabilities_ = 2
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
and l.MergeCapabilities_ <> 2
and cast(l.pgmbefore_ as varchar(500))<> ''
and lf.disableHTML=0
and l.name_ = isnull(@listName,l.name_);

-- move digest, mimedigest, index subtypes -> mail for all TS, SW, MC hidden marketing lists
declare @hiddenMarketingLists TABLE (list_ varchar(200) primary key);
insert into @hiddenMarketingLists (list_)
select l.name_
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where AdminSend_= 'T' 
and lf.hidden = 1 
and lf.orgcode in ('TS','MC','SW')
and l.name_ = isnull(@listName,l.name_)
order by lf.orgcode;

update m 
set m.subType_ = 'mail'
from @hiddenMarketingLists l
inner join dbo.members_ m on m.list_ = l.list_ collate Latin1_General_CI_AI
	and m.SubType_ in ('digest','mimedigest','index');

-- mark all messages sent to segments as digested
declare @twohourago datetime = dateadd(hour,-2,getdate());
update dbo.messages_ 
set Digested_ = 'T'
where SubsetID_ > 0 
and Digested_ = 'F'
and CreatStamp_ > @twohourago;

-- make sure there are no NULL MC Digest Columns
IF EXISTS(select * from dbo.members_ where receiveMCThreadIndex is null)
    update dbo.members_ 
	set receiveMCThreadIndex = 0
    where receiveMCThreadIndex is null;

IF EXISTS(select * from dbo.members_ where receiveMCThreadDigest is null)
    update dbo.members_ 
	set receiveMCThreadDigest = 0
    where receiveMCThreadDigest is null;

-- keep MCSetting_ListType updated
DECLARE @eclipsLists TABLE (listname varchar(100) PRIMARY KEY);

insert into @eclipsLists
select distinct lyrislistname
from membercentral.trialsmith.dbo.eclipsPublications
where lyrislistname <> '';

UPDATE l
SET l.MCSetting_ListType = tmp.MCSetting_ListType
FROM dbo.lists_ as l
INNER JOIN (
	select name_, MCSetting_ListType = case when el.listname is not null then 'eclips' when l.AdminSend_ = 'T' then 'marketing' else 'discussion' end
	from dbo.lists_ l
	left outer join @eclipsLists el on el.listname = l.name_ collate Latin1_General_CI_AI
) as tmp on tmp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- create sendgrid subusers if needed
exec dbo.createSendgridSubusersAndDomains;

RETURN 0;

GO