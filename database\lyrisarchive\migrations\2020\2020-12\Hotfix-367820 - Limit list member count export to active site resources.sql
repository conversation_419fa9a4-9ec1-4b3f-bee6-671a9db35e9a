use lyrisCustom
GO

ALTER PROC dbo.ts_listMemberCountReport
@siteCode varchar(10),
@filename varchar(800)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	/*
	@sitecode can be null (all lists), blank (only unassigned lists), or a sitecode
	*/

	IF OBJECT_ID('tempdb..#tmpMCLists') IS NOT NULL 
		DROP TABLE #tmpMCLists;
	IF OBJECT_ID('tempdb..#tmpListMembers') IS NOT NULL 
		DROP TABLE #tmpListMembers;
	CREATE TABLE #tmpMCLists (siteCode varchar(10), listName varchar(60));

	-- determine lists to limit to
	IF @siteCode is not null and @siteCode <> ''
		INSERT INTO #tmpMCLists (siteCode, listName)
		select s.siteCode, ll.listName
		from membercentral.membercentral.dbo.lists_lists as ll
		inner join membercentral.membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ll.siteResourceID and sr.siteResourceStatusID = 1
		inner join membercentral.membercentral.dbo.sites as s on s.siteID = sr.siteID
		where s.siteCode = @siteCode;
	ELSE BEGIN
		IF @siteCode = ''
			INSERT INTO #tmpMCLists (siteCode, listName)
			select null, name_
			from trialslyris1.dbo.lists_
			where name_ not in (select listName COLLATE Latin1_General_CI_AI from membercentral.membercentral.dbo.lists_lists);
		ELSE BEGIN
			INSERT INTO #tmpMCLists (siteCode, listName)
			select s.siteCode, ll.listName
			from membercentral.membercentral.dbo.lists_lists as ll
			inner join membercentral.membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ll.siteResourceID and sr.siteResourceStatusID = 1
			inner join membercentral.membercentral.dbo.sites as s on s.siteID = sr.siteID;

			INSERT INTO #tmpMCLists (siteCode, listName)
			select null, name_
			from trialslyris1.dbo.lists_
			where name_ not in (select listName COLLATE Latin1_General_CI_AI from #tmpMCLists);
		END
	END

	select siteCode, list_ as listName, isnull(confirm,0) as confirm, isnull(expired,0) as expired, isnull(held,0) as held,
		isnull([member not on list],0) as [member not on list], isnull(normal,0) as normal, 
		isnull([private],0) as [private], isnull(referred,0) as referred, isnull(unsub,0) as unsub
	into #tmpListMembers
	from (
		select tmp.siteCode, m.list_, m.MemberType_, count(*) as memberCount
		from trialslyris1.dbo.members_ as m
		inner join #tmpMCLists as tmp on tmp.listName = m.list_ COLLATE Latin1_General_CI_AI
		group by tmp.siteCode, m.list_, m.MemberType_
	) as tmp
	PIVOT (MAX(memberCount) FOR MemberType_ in ([confirm],[expired],[held],[member not on list],[normal],[private],[referred],[unsub])) as pvt
	order by siteCode, list_;

	EXEC lyrisarchive.dbo.up_exportCSV @csvfilename=@fileName, @sql='select siteCode, listName, confirm, expired, held, [member not on list], normal, [private], referred, unsub from #tmpListMembers order by siteCode, listName';

	IF OBJECT_ID('tempdb..#tmpMCLists') IS NOT NULL 
		DROP TABLE #tmpMCLists;
	IF OBJECT_ID('tempdb..#tmpListMembers') IS NOT NULL 
		DROP TABLE #tmpListMembers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
