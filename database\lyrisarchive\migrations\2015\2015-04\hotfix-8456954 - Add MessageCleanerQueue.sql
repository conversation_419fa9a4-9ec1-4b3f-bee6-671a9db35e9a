use trialslyris1;
GO

CREATE TABLE dbo.messageCleanerQueue
	(
	   messageid_ int NOT NULL,
	   dategrabbed datetime null
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.messageCleanerQueue ADD CONSTRAINT
	PK_messageCleanerQueue PRIMARY KEY CLUSTERED 
	(
	messageid_
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
-- populate the last 2 days worth of messages 
insert into messageCleanerQueue (messageid_)
select messageID_
from messages_
where digested_ = 'F'
and creatStamp_ > dateadd(day,-2,getdate())
GO