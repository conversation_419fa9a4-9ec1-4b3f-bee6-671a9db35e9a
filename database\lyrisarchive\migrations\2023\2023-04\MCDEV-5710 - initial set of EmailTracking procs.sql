use EmailTracking;
GO

ALTER TABLE dbo.recipients ADD sendingApplicationRecipientID int
GO
ALTER TABLE dbo.recipients ADD dateAcknowledged smalldatetime
GO
ALTER TABLE dbo.recipients ADD deliverySeconds int
GO
ALTER TABLE dbo.recipients DROP COLUMN dateFirstAttempted
GO
DROP INDEX [idx_recipientDeliveryMessages_siteID_domainID_username] ON [dbo].[recipientDeliveryMessages]
GO
ALTER TABLE dbo.recipientDeliveryMessages DROP COLUMN messageStatusID
GO
ALTER TABLE dbo.recipientDeliveryMessages ADD deliveryStatusID int
GO
CREATE NONCLUSTERED INDEX [idx_recipientDeliveryMessages_siteID_domainID_username] ON [dbo].[recipientDeliveryMessages]
(
	[siteID] ASC,
	[domainID] ASC,
	[username] ASC
)
INCLUDE([dateReceived],[deliveryStatusID],[MessageTemplateSHA1]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE PROC dbo.emailTracking_getApplicationID
@applicationName VARCHAR(100),
@applicationType VARCHAR(50),
@sendingApplicationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @sendingApplicationID = sendingApplicationID 
	FROM dbo.sendingApplications 
	WHERE applicationType = @applicationType 
	and applicationName=@applicationName;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_createApplicationID
@applicationName VARCHAR(100),
@applicationType VARCHAR(50),
@sendingApplicationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @sendingApplicationIDVar TABLE (sendingApplicationID INT);

	BEGIN TRAN;
		MERGE INTO dbo.sendingApplications WITH (HOLDLOCK) AS Target
		USING (SELECT @applicationName AS applicationName, @applicationType as applicationType) AS Source
			ON Target.applicationName = Source.applicationName 
			and target.applicationType = source.applicationType
		WHEN MATCHED THEN
			UPDATE SET @sendingApplicationID = Target.sendingApplicationID
		WHEN NOT MATCHED THEN
			INSERT (applicationType, applicationName) VALUES (Source.applicationType, source.applicationName)
			OUTPUT INSERTED.sendingApplicationID INTO @sendingApplicationIDVar;

		SET @sendingApplicationID = (SELECT sendingApplicationID FROM @sendingApplicationIDVar)
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_createDeliveryMessageTemplate
@statusMessageTemplate VARCHAR(1000),
@messageTemplateSHA1 varchar(40)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	BEGIN TRAN;
		INSERT INTO dbo.deliveryMessageTemplates (messageTemplateSHA1, dateCreated, statusMessageTemplate)
		SELECT @messageTemplateSHA1, getdate(), @statusMessageTemplate
		WHERE NOT EXISTS (
			SELECT 1
			FROM dbo.deliveryMessageTemplates
			WHERE messageTemplateSHA1 = @MessageTemplateSHA1
		);
	COMMIT TRAN;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_existsDeliveryMessageTemplate
@statusMessageTemplate VARCHAR(1000),
@messageTemplateSHA1 varchar(42),
@exists bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF EXISTS (SELECT 1 FROM dbo.deliveryMessageTemplates WHERE messageTemplateSHA1 = @MessageTemplateSHA1)
		set @exists=1;
	ELSE 
		set @exists=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_getDomainID
@domain VARCHAR(255),
@domainID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @domainID = domainID 
	FROM dbo.recipientDomains 
	WHERE domain = @domain;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_createDomainID
@domain VARCHAR(255),
@domainID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @domainIDVar TABLE (domainID INT);

	BEGIN TRAN;
		MERGE INTO dbo.recipientDomains WITH (HOLDLOCK) AS Target
		USING (SELECT @domain AS domain) AS Source
			ON Target.domain = Source.domain 
		WHEN MATCHED THEN
			UPDATE SET @domainID = Target.domainID
		WHEN NOT MATCHED THEN
			INSERT (domain) VALUES (source.domain)
			OUTPUT INSERTED.domainID INTO @domainIDVar;

		SET @domainID = (SELECT domainID FROM @domainIDVar)
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_createMessageStats
@sendingApplicationID int,
@sendingApplicationMessageID int,
@siteID int,
@ipPoolID int,
@sendgrid_subuserID int,
@sendgrid_subuserDomainID int,
@periodCode int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	BEGIN TRAN;
		INSERT INTO dbo.messageStats (sendingApplicationID, sendingApplicationMessageID, siteID, ipPoolID,
			sendgrid_subuserID, sendgrid_subuserDomainID, periodCode, uniqueOpens, uniqueClicks, totalOpens,
			totalClicks, totalSpamReports)
		SELECT @sendingApplicationID, @sendingApplicationMessageID, @siteID, @ipPoolID, @sendgrid_subuserID,
			@sendgrid_subuserDomainID, @periodCode, 0, 0, 0, 0, 0
		WHERE NOT EXISTS (
			SELECT 1
			FROM dbo.messageStats
			WHERE sendingApplicationID=@sendingApplicationID 
			and sendingApplicationMessageID=@sendingApplicationMessageID
		);
	COMMIT TRAN;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_createUserAgentID
@useragentString VARCHAR(500),
@useragentSHA1 VARCHAR(40),
@userAgentID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @userAgentIDVar TABLE (userAgentID INT);

	BEGIN TRAN;
		MERGE INTO dbo.userAgents WITH (HOLDLOCK) AS Target
		USING (SELECT @useragentString AS useragentString, @useragentSHA1 AS useragentSHA1) AS Source
			ON Target.useragentString = Source.useragentString 
			and Target.useragentSHA1 = Source.useragentSHA1 
		WHEN MATCHED THEN
			UPDATE SET @userAgentID = Target.userAgentID
		WHEN NOT MATCHED THEN
			INSERT (useragentString,useragentSHA1) VALUES (source.useragentString,source.useragentSHA1)
			OUTPUT INSERTED.userAgentID INTO @userAgentIDVar;

		SET @userAgentID = (SELECT userAgentID FROM @userAgentIDVar);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.emailTracking_getUserAgentID
@useragentSHA1 VARCHAR(255),
@UserAgentID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @UserAgentID = UserAgentID 
	FROM dbo.userAgents 
	WHERE useragentSHA1 = @useragentSHA1;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
