use trialslyris1
GO

DROP PROC dbo.[job_updateList-natle_justiceServices]
GO

use lyrisCustom
GO

CREATE PROC dbo.natle_justiceServices

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(5), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.natle_justiceServicesEligible;

if not exists (select * from tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_justiceServicesEligible ended with no rows in table membercentral.dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held')
	and m.association_ not in ('CT');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired'
	and m.association_ not in ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers
	where orgcode not in ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

use trialslyris1
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* ky_doSyncListMemberData */
/* ********************** */
BEGIN TRY
	EXEC dbo.ky_doSyncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.ky_doSyncListMemberData'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

use trialslyris1
GO

DROP PROC dbo.ky_doSyncListMemberData
GO

DROP TABLE ky_syncListMemberData;
GO

use lyrisCustom
GO

CREATE TABLE [dbo].[ky_listServicesEligible](
	[memberid_] [int] NOT NULL,
	[Address1_] [varchar](100) NULL,
	[Address2_] [varchar](100) NULL,
	[Address3_] [varchar](100) NULL,
	[areaofpractice1_] [varchar](250) NULL,
	[areaofpractice2_] [varchar](250) NULL,
	[areaofpractice3_] [varchar](250) NULL,
	[BarDate_] [varchar](10) NULL,
	[City_] [varchar](35) NULL,
	[Company_] [varchar](200) NULL,
	[CongressionalDistrict_] [varchar](20) NULL,
	[ContactPosition_] [varchar](100) NULL,
	[County_] [varchar](50) NULL,
	[District_] [varchar](20) NULL,
	[Fax_] [varchar](20) NULL,
	[Firstname_] [varchar](50) NULL,
	[Gender_] [varchar](10) NULL,
	[HD] [varchar](5) NULL,
	[JoinDate_] [varchar](10) NULL,
	[LastName_] [varchar](50) NULL,
	[Legislative_] [varchar](50) NULL,
	[MemberLevel_] [varchar](250) NULL,
	[MemberStatus_] [varchar](19) NULL,
	[MiddleName_] [varchar](25) NULL,
	[nickname_] [varchar](250) NULL,
	[numeric1_] [int] NULL,
	[numeric2_] [int] NOT NULL,
	[numeric3_] [int] NULL,
	[PostalCode_] [varchar](10) NULL,
	[prefix_] [varchar](10) NULL,
	[ProfSuffix_] [varchar](20) NULL,
	[renewLink_] [varchar](250) NULL,
	[SD] [varchar](5) NULL,
	[StateProvince_] [varchar](4) NULL,
	[Suffix_] [varchar](20) NULL,
	[Text1_] [varchar](250) NULL,
	[Text3_] [varchar](250) NULL,
	[Text4_] [nvarchar](250) NULL,
	[Text5_] [varchar](250) NULL,
	[Website_] [varchar](250) NULL,
	[WorkPhone_] [varchar](20) NULL,
 CONSTRAINT [PK_ky_listServicesEligible] PRIMARY KEY CLUSTERED 
(
	[memberid_] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

use lyrisCustom
GO

CREATE PROC dbo.ky_listServices
AS

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists;

-- get all KY lists
select [name] as listname
into #tmpKYLists
from trialsLyris1.dbo.lists_format 
where orgcode = 'KY'
and [name] <> 'eclips_ky';

-- get membernumbers on those lists from lyris
delete from MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligible;

insert into MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligible (memberid_, MCMemberNumber, MCMemberID)
select m.memberid_, m.externalMemberID, null
from trialsLyris1.dbo.members_ as m 
inner join #tmpKYLists as tmp on tmp.listname = m.list_
where m.externalMemberID is not null;

-- populate data
EXEC MEMBERCENTRAL.customApps.dbo.ky_listServicesEligible;

-- get data into local table
truncate table dbo.ky_listServicesEligible;

insert into dbo.ky_listServicesEligible (memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_)
select memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_
from MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligibleFinal;


-- update lyris member data
update m
set m.Address1_ = tmp.Address1_,
	m.Address2_ = tmp.Address2_,
	m.Address3_ = tmp.Address3_,
	m.areaofpractice1_ = tmp.areaofpractice1_,
	m.areaofpractice2_ = tmp.areaofpractice2_,
	m.areaofpractice3_ = tmp.areaofpractice3_,
	m.BarDate_ = tmp.BarDate_,
	m.City_ = tmp.City_,
	m.Company_ = tmp.Company_,
	m.CongressionalDistrict_ = tmp.CongressionalDistrict_,
	m.Contactposition_ = tmp.Contactposition_,
	m.County_ = tmp.County_,
	m.District_ = tmp.district_,
	m.Fax_ = tmp.Fax_,
	m.Firstname_ = tmp.Firstname_,
	m.Gender_ = tmp.Gender_,
	m.HD = tmp.HD,
	m.JoinDate_ = tmp.JoinDate_,
	m.LastName_ = tmp.LastName_,
	m.Legislative_ = tmp.Legislative_,
	m.MemberLevel_ = tmp.MemberLevel_,
	m.MemberStatus_ = tmp.MemberStatus_,
	m.MiddleName_ = tmp.MiddleName_,
	m.nickname_ = tmp.nickname_,
	m.numeric1_ = tmp.numeric1_,
	m.numeric2_ = tmp.numeric2_,
	m.numeric3_ = tmp.numeric3_,
	m.PostalCode_ = tmp.PostalCode_,
	m.prefix_ = tmp.prefix_,
	m.ProfSuffix_ = tmp.ProfSuffix_,
	m.RenewLink_ = tmp.RenewLink_,
	m.SD = tmp.SD,
	m.StateProvince_ = tmp.StateProvince_,
	m.Suffix_ = tmp.Suffix_,
	m.Text1_ = tmp.Text1_,
	m.Text3_ = tmp.Text3_,
	m.Text4_ = tmp.Text4_,
	m.Text5_ = tmp.Text5_,
	m.Website_ = tmp.Website_,
	m.WorkPhone_ = tmp.WorkPhone_
from trialslyris1.dbo.members_ as m
inner join dbo.ky_listServicesEligible as tmp on tmp.memberid_ = m.memberid_;


-- cleanup
truncate table dbo.ky_listServicesEligible;

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists;

GO

use trialslyris1
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

use trialslyris1
GO

DROP PROC dbo.[swl_updateMarketingList-Natle]
GO

use lyrisCustom
GO

CREATE PROC dbo.natle_seminarWebLive

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
create TABLE #swl_eligibleForNatleMarketingList (platform varchar(20), orgcode varchar(5), memberID int, 
	membernumber varchar(100), fullname varchar(100), email varchar(100), usernameLC_ varchar(100), 
	domain_ varchar(250));

exec membercentral.customApps.dbo.natle_seminarWebLiveEligible;

if not exists (select * from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table  membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.' ;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join trialslyris1.dbo.members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from trialslyris1.dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

use trialslyris1
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

DROP PROC dbo.[trialsmith_syncListUnsubs]
GO

use lyrisCustom
GO

CREATE PROCEDURE dbo.trialsmith_syncListUnsubs
@list1 varchar(100),
@list2 varchar(100)

AS

SET NOCOUNT ON;

update L1members
set membertype_ = 'unsub'
from trialslyris1.dbo.members_ L1members
inner join trialslyris1.dbo.members_ L2members on L1members.emailaddr_ = L2members.emailaddr_
	and L2members.membertype_ = 'unsub'
	and L1members.list_ = @list1
	and L2members.list_ = @list2;

update L2members
set membertype_ = 'unsub'
from trialslyris1.dbo.members_ L1members
inner join trialslyris1.dbo.members_ L2members on L1members.emailaddr_ = L2members.emailaddr_
	and L1members.membertype_ = 'unsub'
	and L1members.list_ = @list1
	and L2members.list_ = @list2;

GO

use trialslyris1
GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

set nocount on

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10),depomemberdataid int)
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int)
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int)
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100))


DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @currentListName varchar(100)

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END


set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'

exec TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers


if not exists (select emailaddr_ from TLASITES.transfer.dbo.trialsmithMarketingListPopulation)
BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE
BEGIN

    exec lyrisCustom.dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ = 'unsub'


    insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
    from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null


    --delete subscribed members with email addresses that are no longer in the pool

    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from members_ m
    left outer join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') and pool.poolid is null and m.membertype_ <> 'unsub'

    delete m
    from members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_


    -- update

    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from members_ m WITH(NOLOCK)
    inner join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
				or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub'


    update m set 
	    DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_,
		depomemberdataid = pool.depomemberdataid
    from members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool
	    on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ <> 'unsub'


    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join members_ m WITH(NOLOCK)
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers')

    -- insert new memberships
    insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid
    from #memberPool


	--update trialsmithUsage
	truncate table dbo.tsdata
	insert into dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, expList, numBadSrc, subType, expires)
	select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
	from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
	where depomemberdataid is not null

    exec lyrisCustom.dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

set nocount off

GO

use trialslyris1
GO

DROP PROC dbo.trialsmith_updateMarketingLists
GO

use lyrisCustom
GO

CREATE PROC dbo.trialsmith_updateMarketingLists
AS

set nocount on;

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool;
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers;
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete;
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs;
CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10),depomemberdataid int);
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int);
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int);
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100));

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100), @currentListName varchar(100), @tier varchar(20), 
	@errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10);

set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

exec membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

if not exists (select emailaddr_ from membercentral.transfer.dbo.trialsmithMarketingListPopulation) BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from trialslyris1.dbo.members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') 
	and m.membertype_ = 'unsub';

	insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
    from membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null;

    --delete subscribed members with email addresses that are no longer in the pool
    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from trialslyris1.dbo.members_ m
    left outer join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') 
	and pool.poolid is null 
	and m.membertype_ <> 'unsub';

    delete m
    from trialslyris1.dbo.members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_;

    -- update
    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from trialslyris1.dbo.members_ m WITH(NOLOCK)
    inner join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
				or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub';

    update m 
	set DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_,
		depomemberdataid = pool.depomemberdataid
    from trialslyris1.dbo.members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') 
	and m.membertype_ <> 'unsub';

    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join trialslyris1.dbo.members_ m WITH(NOLOCK) on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers');

    -- insert new memberships
    insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid
    from #memberPool;

	--update trialsmithUsage
	truncate table trialslyris1.dbo.tsdata
	insert into trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, expList, numBadSrc, subType, expires)
	select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
	from membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
	where depomemberdataid is not null;

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool;
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers;
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete;
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs;

GO

use lyrisCustom
GO

CREATE PROC dbo.ts_membercentraladmins

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'membercentraladmins'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(5), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.ts_membercentraladminsEligible;

if not exists (select * from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating MemberCentralAdmins List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.ts_membercentraladminsEligible ended with no rows in table membercentral.dataTransfer.dbo.ts_membercentraladminsEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired';

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers;
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

use trialslyris1
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ts_membercentraladmins';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

