use trialslyris1;
GO

ALTER TABLE dbo.members_ ADD MCEmailKey_usernameLC varchar(100);
GO
ALTER TABLE dbo.members_ ADD MCEmailKey_domain varchar(100);
GO

use lyrisCustom;
GO

ALTER PROC dbo.mc_addListMembership
@siteID int,
@listName varchar(60),
@emailAddr varchar(100),
@userName varchar(100),
@domain varchar(250),
@fullName varchar(100),
@memberType varchar(20),
@subType varchar(20),
@externalMemberID varchar(100),
@lockAddress bit,
@lockName bit,
@keepActive bit,
@receiverMemberID int,
@recordedByMemberID int,
@memberID_ int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @mcEmailKey varchar(75) = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + @userName + '@' + @domain),2)
	SET @memberID_ = NULL;

	
	INSERT INTO trialslyris1.dbo.members_ (list_,emailaddr_, fullname_, DateJoined_, membertype_, subtype_, usernameLc_, domain_,
		externalMemberID, MCOption_lockAddress, MCOption_lockName, MCOption_keepActive, MCEmailKey, MCEmailKey_usernameLC, MCEmailKey_domain)
	VALUES (@listName, @emailAddr, @fullName, getdate(), @memberType, @subType, @userName, @domain,
		@externalMemberID, @lockAddress, @lockName, @keepActive, @mcEmailKey, @userName, @domain);

	SELECT @memberID_ = SCOPE_IDENTITY();

	INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
		', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
		', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
		', "MAINMESSAGE":"List Membership Added", "LISTNAME":"'+ @listName +'"'+
		', "MESSAGES":[ "Email ['+ @emailAddr +'] has been added to the list." ]'+
		', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
		' } }';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.mc_saveMemberListsInfo
@siteID int,
@receiverMemberID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @mainMessage varchar(50);

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#mc_listMembersUpdate') IS NULL BEGIN
		RAISERROR('Unable to locate the temp table for processing.',16,1);
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	CREATE TABLE #tblExistingMember([MemberID_] [int],
		[SubType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[EmailAddr_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[FullName_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[ExpireDate_] [smalldatetime],
		[MemberType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[MCOption_lockAddress] [bit],
		[MCOption_lockName] [bit],
		[MCOption_keepActive] [bit],
		[receiveMCThreadIndex] [bit],
		[receiveMCThreadDigest] [bit]);
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX), listName varchar(60), email varchar(100));

	INSERT INTO #tblExistingMember([MemberID_], [SubType_], [EmailAddr_], [FullName_] , [ExpireDate_], [MemberType_], [MCOption_lockAddress], [MCOption_lockName],
		[MCOption_keepActive], [receiveMCThreadIndex], [receiveMCThreadDigest])
	SELECT lm.[MemberID_], lm.[SubType_], lm.[EmailAddr_], lm.[FullName_] , lm.[ExpireDate_], lm.[MemberType_], lm.[MCOption_lockAddress], lm.[MCOption_lockName],
		lm.[MCOption_keepActive], lm.[receiveMCThreadIndex], lm.[receiveMCThreadDigest]
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;
	
	UPDATE lm
	SET lm.SubType_ = tmp.subType,
		lm.EmailAddr_ = tmp.email,
		lm.FullName_ = tmp.fullname,
		lm.ExpireDate_ = CASE WHEN tmp.memberType <> 'expired' THEN NULL ELSE lm.ExpireDate_ END,
		lm.MemberType_ = tmp.memberType,
		lm.Domain_ = tmp.domain,
		lm.UserNameLC_ = tmp.username,
		lm.MCOption_lockAddress = tmp.MCOption_lockAddress,
		lm.MCOption_lockName = tmp.MCOption_lockName,
		lm.MCOption_keepActive = tmp.MCOption_keepActive,
		lm.receiveMCThreadIndex = tmp.receiveMCThreadIndex,
		lm.receiveMCThreadDigest = tmp.receiveMCThreadDigest,
		lm.MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',lm.list_ + '|' + tmp.username collate SQL_Latin1_General_CP1_CI_AS + '@' + tmp.domain collate SQL_Latin1_General_CP1_CI_AS),2),
		lm.MCEmailKey_usernameLC = tmp.username,
		lm.MCEmailKey_domain = tmp.domain
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Delivery Settings changed from [' + 
		CASE old.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END + '] to [' + 
		CASE m.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END
		+ '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.SubType_ <> m.SubType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'E-mail changed from [' + old.EmailAddr_ + '] to [' + m.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.EmailAddr_ <> m.EmailAddr_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Name changed from [' + ISNULL(NULLIF(old.FullName_,''),'blank') + '] to [' + ISNULL(NULLIF(m.FullName_,''),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.FullName_,'') <> ISNULL(m.FullName_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Expired Date changed from [' + ISNULL(CONVERT(VARCHAR(20),old.ExpireDate_,120),'blank') + '] to [' + ISNULL(CONVERT(VARCHAR(20),m.ExpireDate_,120),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.ExpireDate_,'') <> ISNULL(m.ExpireDate_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Member List Status changed from [' + old.MemberType_ + '] to [' + m.MemberType_ + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.MemberType_ <> m.MemberType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Address changed from [' + CASE WHEN old.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockAddress, 0) <> ISNULL(m.MCOption_lockAddress, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Name changed from [' + CASE WHEN old.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockName, 0) <> ISNULL(m.MCOption_lockName, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Keep Active changed from [' + CASE WHEN old.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_keepActive, 0) <> ISNULL(m.MCOption_keepActive, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Index of Topics setting changed from [' + CASE WHEN old.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadIndex, 0) <> ISNULL(m.receiveMCThreadIndex, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Summary of All Messages setting changed from [' + CASE WHEN old.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadDigest, 0) <> ISNULL(m.receiveMCThreadDigest, 0);

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Updated"' +
			', "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE listName = tmp.listName
				ORDER BY email, msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT listName
			FROM #tmpLogMessages
		) AS tmp;
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO


ALTER PROC dbo.natle_justiceServices

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'natle_justiceservices';

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
CREATE TABLE #tempListMembers (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, membernumber VARCHAR(100), 
	fullname VARCHAR(500), email VARCHAR(200), usernameLC_ VARCHAR(100), domain_ VARCHAR(150));

EXEC membercentral.customApps.dbo.natle_justiceServicesEligible;

IF NOT EXISTS (SELECT * FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = 'Error Updating NATLE Justice Services List';
	SET @errmsg = 'customApps.dbo.natle_justiceServicesEligible ended with no rows in table dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END ELSE BEGIN

	INSERT INTO #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	UPDATE #tempListMembers 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- mark email addresses that are NOT in temp table as expired
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ AS m  
	LEFT OUTER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND m.membertype_ in ('normal','held')
	AND m.association_ NOT IN ('CT');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ AS m 
	INNER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName
	AND m.membertype_ = 'expired'
	AND m.association_ NOT IN ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	UPDATE m 
	SET domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname,
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',m.list_ + '|' + tmp.usernameLC_ collate SQL_Latin1_General_CP1_CI_AS + '@' + tmp.domain_ collate SQL_Latin1_General_CP1_CI_AS),2),
		MCEmailKey_usernameLC = tmp.usernameLC_,
		MCEmailKey_domain = tmp.domain_
	FROM #tempListMembers AS tmp
	INNER JOIN trialslyris1.dbo.members_ AS m ON m.list_ = @listName
		AND m.association_ = tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.ExternalMemberID = tmp.memberNumber COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.emailaddr_ <> tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS)
		AND m.association_ NOT IN ('CT')
		AND m.membertype_ = 'normal'
		AND m.Domain_ = tmp.domain_  COLLATE SQL_Latin1_General_CP1_CI_AS
	LEFT OUTER JOIN trialslyris1.dbo.members_ AS prexistingEmail ON prexistingEmail.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND prexistingEmail.list_ = @listName
		AND prexistingEmail.memberID_ <> m.memberID_
	WHERE prexistingEmail.memberID_ IS NULL;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #tempListMembers ec
	WHERE email in (
		SELECT emailaddr_ COLLATE SQL_Latin1_General_CP1_CI_AS
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
	);

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,MCEmailKey,MCEmailKey_usernameLC,MCEmailKey_domain)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber AS ExternalMemberID, orgcode,
		MCEmailKey=convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + usernameLC_ + '@' + domain_),2),
		usernameLc_,domain_
	FROM #tempListMembers
	WHERE orgcode NOT IN ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

ALTER PROC dbo.natle_seminarWebLive

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'seminarweblive';

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
CREATE TABLE #swl_eligibleForNatleMarketingList (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, 
	membernumber VARCHAR(100), fullname VARCHAR(100), email VARCHAR(100), usernameLC_ VARCHAR(100), 
	domain_ VARCHAR(250));

EXEC membercentral.customApps.dbo.natle_seminarWebLiveEligible;

IF NOT EXISTS (SELECT * FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = 'Error Updating SeminarWebLive Marketing List for NATLE';
	SET @errmsg = 'customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END ELSE BEGIN

	INSERT INTO #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	UPDATE #swl_eligibleForNatleMarketingList 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- update fullname/association based on matching email address
	UPDATE m 
	SET association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	FROM #swl_eligibleForNatleMarketingList tmp
	INNER JOIN trialslyris1.dbo.members_ m ON m.list_ = @listName
		AND m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.association_ <> tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ m 
	LEFT OUTER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND isListAdm_ <> 'T'
	AND m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ m 
	INNER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.list_ = @listName
		AND m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #swl_eligibleForNatleMarketingList ec
	WHERE exists (
		SELECT usernameLc_, domain_
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
		AND usernameLc_ = ec.usernameLc_ COLLATE SQL_Latin1_General_CP1_CI_AS
		AND domain_ = ec.domain_ COLLATE SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	SELECT *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) AS rowNum
	INTO #swl_eligibleForNatleMarketingList2
	FROM #swl_eligibleForNatleMarketingList;

	DELETE FROM #swl_eligibleForNatleMarketingList2
	WHERE rowNum > 1;

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_,MCEmailKey,MCEmailKey_usernameLC,MCEmailKey_domain)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode,
		MCEmailKey=convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + usernameLC_ + '@' + domain_),2),
		usernameLc_,domain_
	FROM #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	CREATE TABLE #memberPool (poolid INT identity(1,1), DateJoined_ DATETIME, domain_ VARCHAR(250), emailaddr_ VARCHAR(100),
		fullname_ VARCHAR(100), list_ VARCHAR(60), usernameLc_ VARCHAR(100), ExternalMemberID VARCHAR(100),
		association_ VARCHAR(10), depomemberdataid INT);
	CREATE TABLE #updatedMembers (id INT identity(1,1), poolid INT, memberID_ INT);
	CREATE TABLE #membershipsToDelete (id INT identity(1,1), memberid_ INT);
	CREATE TABLE #unsubs (id INT identity(1,1), emailaddr_ VARCHAR(100));

	DECLARE @errorSubject VARCHAR(100), @errmsg varchar(200);
	
	EXEC membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

	IF NOT EXISTS (SELECT emailaddr_ FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation) BEGIN
		SET @errorSubject = 'Error Updating TrialSmith Marketing Lists';
		SET @errmsg = 'trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
	END ELSE BEGIN
		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

		INSERT INTO #unsubs (emailaddr_)
		SELECT emailaddr_
		FROM trialslyris1.dbo.members_ m 
		WHERE list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ = 'unsub';

		INSERT INTO #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
		SELECT lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
		FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation lp
		LEFT OUTER JOIN #unsubs u ON u.emailaddr_ = lp.emailaddr_ COLLATE Latin1_General_CI_AI
		WHERE u.emailaddr_ IS NULL;

		-- delete subscribed members with email addresses that are no longer in the pool
		INSERT INTO #membershipsToDelete (memberID_)
		SELECT m.memberID_
		FROM trialslyris1.dbo.members_ m
		LEFT OUTER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND pool.poolid IS NULL 
		AND m.membertype_ <> 'unsub';

		DELETE m
		FROM trialslyris1.dbo.members_ m
		INNER JOIN #membershipsToDelete md ON m.memberid_ = md.memberid_;

		-- update
		INSERT INTO #updatedMembers (poolid, memberid_)
		SELECT pool.poolid, m.memberID_
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
			AND (
					m.emailaddr_ <> pool.emailaddr_ COLLATE Latin1_General_CI_AI
					or m.fullname_ <> pool.fullname_ COLLATE Latin1_General_CI_AI
					or m.ExternalMemberID <> pool.ExternalMemberID COLLATE Latin1_General_CI_AI
					or m.association_ <> pool.association_ COLLATE Latin1_General_CI_AI
					or ISNULL(m.depomemberdataID,0) <> ISNULL(pool.depomemberdataid,0)
			)
			AND m.list_ in ('trialsmith','trialsmith_subscribers')
			AND m.membertype_ <> 'unsub';

		UPDATE m 
		SET DateJoined_ = pool.DateJoined_,
			fullname_= pool.fullname_,
			list_= pool.list_,
			ExternalMemberID = pool.ExternalMemberID,
			association_ = pool.association_,
			depomemberdataid = pool.depomemberdataid
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #updatedMembers updated ON m.memberid_ = updated.memberid_
		INNER JOIN #memberPool pool ON updated.poolid = pool.poolid
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ <> 'unsub';

		-- delete all preexisting memberships from pool, leaving only entries that need to be created
		DELETE pool
		FROM #memberPool pool
		INNER JOIN trialslyris1.dbo.members_ m WITH(NOLOCK) ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers');

		-- insert new memberships
		INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ ,
			ExternalMemberID, association_, depomemberdataid, mcemailkey,MCEmailKey_usernameLC,MCEmailKey_domain)
		SELECT DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ , ExternalMemberID, association_,
			depomemberdataid, mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernamelc_ + '@' + domain_),2),
			usernameLc_, domain_
		FROM #memberPool;

		-- update trialsmithUsage
		truncate table trialslyris1.dbo.tsdata
		INSERT INTO trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, 
			expList, numBadSrc, subType, expires)
		SELECT distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, 
			numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, 
			last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
		FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation
		WHERE depomemberdataid IS NOT NULL;

		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith', 'trialsmith_subscribers';
	END

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_membercentraladmins

AS

DECLARE @errorSubject VARCHAR(100), @errmsg VARCHAR(200), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'membercentraladmins'

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
CREATE TABLE #tempListMembers (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, membernumber VARCHAR(100), 
	fullname VARCHAR(500), email VARCHAR(200), usernameLC_ VARCHAR(100), domain_ VARCHAR(150));

EXEC membercentral.customApps.dbo.ts_membercentraladminsEligible;

IF NOT EXISTS (SELECT * FROM membercentral.dataTransfer.dbo.ts_membercentraladminsEligible) BEGIN
	SET @errorSubject = 'Error Updating MemberCentralAdmins List';
	SET @errmsg = 'customApps.dbo.ts_membercentraladminsEligible ended with no rows in table dataTransfer.dbo.ts_membercentraladminsEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END ELSE BEGIN

	INSERT INTO #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM membercentral.dataTransfer.dbo.ts_membercentraladminsEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	UPDATE #tempListMembers 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- mark email addresses that are NOT in temp table as expired
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ AS m  
	LEFT OUTER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ AS m 
	INNER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName
	AND m.membertype_ = 'expired';

	-- update email addresses/fullname based on matching membernumber and association
	UPDATE m 
	SET domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname,
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',m.list_ + '|' + tmp.usernameLC_ collate SQL_Latin1_General_CP1_CI_AS + '@' + tmp.domain_ collate SQL_Latin1_General_CP1_CI_AS),2),
		MCEmailKey_usernameLC = tmp.usernameLC_,
		MCEmailKey_domain = tmp.domain_
	FROM #tempListMembers AS tmp
	INNER JOIN trialslyris1.dbo.members_ AS m ON m.list_ = @listName
		AND m.association_ = tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.ExternalMemberID = tmp.memberNumber COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.emailaddr_ <> tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS)
		AND m.membertype_ = 'normal'
	LEFT OUTER JOIN trialslyris1.dbo.members_ AS prexistingEmail ON prexistingEmail.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND prexistingEmail.list_ = @listName
		AND prexistingEmail.memberID_ <> m.memberID_
	WHERE prexistingEmail.memberID_ IS NULL;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #tempListMembers ec
	WHERE email in (
		SELECT emailaddr_ COLLATE SQL_Latin1_General_CP1_CI_AS
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
	);

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,mcemailkey,MCEmailKey_usernameLC,MCEmailKey_domain)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber AS ExternalMemberID, orgcode,
		mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + usernamelc_ + '@' + domain_),2),
		usernameLc_, domain_
	FROM #tempListMembers;
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

use trialslyris1
GO

ALTER PROC dbo.job_enforceListSettings
@listName varchar(60) = NULL

AS

DECLARE @headerLineEndings char(2) = char(13) + char(10)

-- turn off join by email for all lists
update dbo.lists_
set NoEmailSub_ = 'T'
where NoEmailSub_ <> 'T'
and name_ = isnull(@listName,name_);

-- force all lists to only allow admins to add members
update dbo.lists_ 
set security_ = 'private'
where security_ = 'open' 
and name_ not in ('eclips_js','brandigy')
and name_ = isnull(@listName,name_);

-- set all lists to invisible in Discussion Forum Interface
update dbo.lists_ 
set MriVisibility_ = 'I'
where MriVisibility_ <> 'I'
and name_ = isnull(@listName,name_);


-- make sure that all discussion lists with HTML conversion turned on have Pgmbefore filled in
update l set 
	pgmbefore_ = 'C:\Progra~1\groovy\groovy-2.3.0\bin\groovy.bat c:\groovyscripts\DiscussionListProcessor.groovy -m $MESSAGEID -e production -d c:\groovyscripts\'
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
and cast(l.pgmbefore_ as varchar(500)) not like  '%DiscussionListProcessor.groovy%'
and lf.disableHTML=0
and l.AdminSend_ = 'F'
and l.name_ = isnull(@listName,l.name_);

-- update maxmesssize to 50000 for lists using offloadattachments with groovy script
update l 
set l.maxmessSiz_ = 50000
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where lf.offloadAttachments = 1 
and lf.disableHTML=0
and cast(l.pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
and l.maxmessSiz_ <> 50000
and l.name_ = isnull(@listName,l.name_);

-- update maxmesssize to 1500 for discussion lists not using offloadattachments
update l 
set l.maxmessSiz_ = 1500
from dbo.lists_ l
inner join (
	select name_
	from dbo.lists_
	where maxmessSiz_ <> 1500 and adminsend_ = 'F'
		except
	select name_
	from dbo.lists_ l2
	inner join dbo.lists_format lf on lf.name = l2.name_
	where lf.offloadAttachments = 1 
	and disableHTML=0
	and cast(pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
	) as temp on temp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- turn on advanced scripting for all lists using HTMLConversion
update l 
set l.MergeCapabilities_ = 2
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
and l.MergeCapabilities_ <> 2
and cast(l.pgmbefore_ as varchar(500))<> ''
and lf.disableHTML=0
and l.name_ = isnull(@listName,l.name_);

-- move digest, mimedigest, index subtypes -> mail for all TS, SW, MC hidden marketing lists
declare @hiddenMarketingLists TABLE (list_ varchar(200) primary key);
insert into @hiddenMarketingLists (list_)
select l.name_
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where AdminSend_= 'T' 
and lf.hidden = 1 
and lf.orgcode in ('TS','MC','SW')
and l.name_ = isnull(@listName,l.name_)
order by lf.orgcode;

IF EXISTS (
	select 1 
	from @hiddenMarketingLists l
	inner join dbo.members_ m on m.list_ = l.list_ collate Latin1_General_CI_AI
		and m.SubType_ in ('digest','mimedigest','index')
) BEGIN
	update m 
	set m.subType_ = 'mail'
	from @hiddenMarketingLists l
	inner join dbo.members_ m on m.list_ = l.list_ collate Latin1_General_CI_AI
		and m.SubType_ in ('digest','mimedigest','index');
END
-- mark all messages sent to segments as digested
declare @twohourago datetime = dateadd(hour,-2,getdate());
update dbo.messages_ 
set Digested_ = 'T'
where SubsetID_ > 0 
and Digested_ = 'F'
and CreatStamp_ > @twohourago;

-- make sure there are no NULL MC Digest Columns
IF EXISTS(select * from dbo.members_ where receiveMCThreadIndex is null)
    update dbo.members_ 
	set receiveMCThreadIndex = 0
    where receiveMCThreadIndex is null;

IF EXISTS(select * from dbo.members_ where receiveMCThreadDigest is null)
    update dbo.members_ 
	set receiveMCThreadDigest = 0
    where receiveMCThreadDigest is null;

-- make sure that all rows have correct MCEmailKey_
IF EXISTS(select 1 from dbo.members_ where UserNameLC_ <> isnull(MCEmailKey_usernameLC,'') or domain_ <> isnull(MCEmailKey_domain,''))
    update dbo.members_ set 
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
		MCEmailKey_usernameLC = usernameLC_,
		MCEmailKey_domain = domain_
    where UserNameLC_ <> isnull(MCEmailKey_usernameLC,'') or domain_ <> isnull(MCEmailKey_domain,'');


-- keep MCSetting_ListType updated
DECLARE @eclipsLists TABLE (listname varchar(100) PRIMARY KEY);

insert into @eclipsLists
select distinct lyrislistname
from membercentral.trialsmith.dbo.eclipsPublications
where lyrislistname <> '';

UPDATE l
SET l.MCSetting_ListType = tmp.MCSetting_ListType
FROM dbo.lists_ as l
INNER JOIN (
	select name_, MCSetting_ListType = case 
		when el.listname is not null then 'eclips' 
		when swm.list is not null then 'swmarketing' 
		when l.AdminSend_ = 'T' then 'marketing' 
		else 'discussion' end
	from dbo.lists_ l
	left outer join @eclipsLists el on el.listname = l.name_ collate Latin1_General_CI_AI
	left outer join sw_marketing swm on swm.list = l.name_ collate Latin1_General_CI_AI
) as tmp on tmp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- create sendgrid subusers if needed
EXEC dbo.createSendgridSubusersAndDomains;

-- set SubUserDomainID on Lists
DECLARE @activeStatusID int, @environmentID int = 5, @platformMKTSubUserDomainID int, @platformLISTPrimarySubUserDomainID int, @platformMKTSubUserID int, @platformLISTSubUserID int;

SELECT @activeStatusID = subuserStatusID 
FROM membercentral.platformMail.dbo.sendgrid_subuserStatuses
WHERE [status] = 'Active';

SELECT @platformMKTSubUserDomainID = sud.subuserDomainID, @platformMKTSubUserID=dsu.subuserID
FROM membercentral.platformMail.dbo.sendgrid_defaultsubusers AS dsu
INNER JOIN membercentral.platformMail.dbo.email_mailstreams AS ms ON ms.mailStreamID = dsu.mailstreamID
	AND dsu.environmentID = @environmentID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformMail.dbo.sendgrid_subusers AS su ON su.subuserID = dsu.subuserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformMail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
WHERE sud.statusID = @activeStatusID;

SELECT @platformLISTPrimarySubUserDomainID = sud.subuserDomainID, @platformLISTSubUserID=dsu.subuserID
FROM membercentral.platformMail.dbo.sendgrid_defaultsubusers AS dsu
INNER JOIN membercentral.platformMail.dbo.email_mailstreams AS ms on ms.mailStreamID = dsu.mailstreamID
	AND dsu.environmentID = @environmentID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformMail.dbo.sendgrid_subusers AS su ON su.subuserID = dsu.subuserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformMail.dbo.sendgrid_subuserDomains sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
WHERE sud.statusID = @activeStatusID;

-- marketing lists
DECLARE @tmpMarketingLists TABLE (listName varchar(60), MCSetting_subuserDomainID int);

INSERT INTO @tmpMarketingLists (listName)
SELECT name_
FROM dbo.lists_
WHERE MCSetting_ListType = 'marketing'
AND name_ = isnull(@listName,name_);

-- the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = lf.overrideSubuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active subuserdomain on the subuser associated with the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = active_sud.subuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS active_sud ON active_sud.subuserID = su.subuserID
	AND active_sud.subuserDomainID = su.activeSubuserDomainID
	AND active_sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the site-specific marketing subuser when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.siteID = s.siteID
	and su.environmentID=@environmentID
    and su.subuserID <> @platformMKTSubUserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
	AND sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the platformwide default marketing subuser when the subuser and domain are both active status
UPDATE @tmpMarketingLists
SET MCSetting_subuserDomainID = @platformMKTSubUserDomainID
WHERE MCSetting_subuserDomainID IS NULL;

-- discussion lists
DECLARE @tmpDiscussionLists TABLE (listName varchar(60), MCSetting_subuserDomainID int);

INSERT INTO @tmpDiscussionLists (listName)
SELECT name_
FROM dbo.lists_
WHERE MCSetting_ListType = 'discussion'
AND name_ = isnull(@listName,name_);

-- the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = lf.overrideSubuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active subuserdomain on the subuser associated with the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = active_sud.subuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS active_sud ON active_sud.subuserID = su.subuserID
	AND active_sud.subuserDomainID = su.activeSubuserDomainID
	AND active_sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the site-specific listserver subuser when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.siteID = s.siteID
	and su.environmentID=@environmentID
    and su.subuserID <> @platformLISTSubUserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
	AND sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the domain that matches the lyris site sending domain on the site-specific listserver subuser when the subuser and domain are both active status
UPDATE tmp
SET tmp.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS s ON s.name_ = t.sitename_
INNER JOIN dbo.lists_format AS lf ON lf.name = l.Name_
INNER JOIN membercentral.membercentral.dbo.sites AS mc_s ON mc_s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.sendingHostname = s.domainName_ COLLATE Latin1_General_CI_AI 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.siteID = mc_s.siteID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
WHERE tmp.MCSetting_subuserDomainID IS NULL;

-- the domain that matches the lyris site sending domain on the platformwide default listserver subuser when the subuser and domain are both active status
UPDATE tmp
SET tmp.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS s ON s.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.sendingHostname = s.domainName_ COLLATE Latin1_General_CI_AI 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_defaultsubusers AS su ON su.subuserID = sud.subuserID
	AND su.environmentID = @environmentID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = su.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
WHERE tmp.MCSetting_subuserDomainID IS NULL;

-- the primary domain on the platformwide default listserver subuser when the subuser and domain are both active status
UPDATE @tmpDiscussionLists
SET MCSetting_subuserDomainID = @platformLISTPrimarySubUserDomainID
WHERE MCSetting_subuserDomainID IS NULL;

-- delete matching MCSetting_subuserDomainID rows
DELETE tmp
FROM @tmpMarketingLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
WHERE tmp.MCSetting_subuserDomainID = l.MCSetting_subuserDomainID;

DELETE tmp
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
WHERE tmp.MCSetting_subuserDomainID = l.MCSetting_subuserDomainID;

IF EXISTS (SELECT 1 FROM @tmpMarketingLists) OR EXISTS (SELECT 1 FROM @tmpDiscussionLists) BEGIN
	-- update MCSetting_subuserDomainID
	UPDATE l
	SET l.MCSetting_subuserDomainID = tmp.MCSetting_subuserDomainID,
		l.MCSetting_configLastChanged  = GETDATE()
	FROM dbo.lists_ AS l
	INNER JOIN (
		SELECT listName, MCSetting_subuserDomainID FROM @tmpMarketingLists
			UNION
		SELECT listName, MCSetting_subuserDomainID FROM @tmpDiscussionLists
	) tmp ON tmp.listName = l.name_;

	DECLARE @errorSubject varchar(100) = 'Enforce Lyris Settings Procedure updated SubUserDomainID on Lists',
		@errorTitle varchar(100) = 'Enforce Lyris Settings Procedure updated SubUserDomainID on Lists',
		@errmsg nvarchar(2048) = 'Enforce Lyris Settings Procedure updated SubUserDomainID on following Lists.<br/><br/>',
		@updatedListNames varchar(max);

	SELECT @updatedListNames = COALESCE(@updatedListNames + ', ', '') + CAST(listName AS varchar(60))
	FROM  (
		SELECT listName FROM @tmpMarketingLists
			UNION
		SELECT listName FROM @tmpDiscussionLists
	) tmp;

	SET @errmsg = @errmsg + @updatedListNames;

	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errmsg, @forDev=1;
END

/*

no changes at the current time to ECLips and SW Marketing Lists

*/

-- update discussion lists fields
UPDATE l
SET l.NoListHdr_ = 'F',
	l.AddHeadersAndFooters_ = 'N',
	l.SMTPFrom_ = '"%%merge member.fullname%% (%%list.name%% listserver)" <listsender-%%list.name%%@'+sud.sendingHostname+'>',
	l.Replyto_ = CASE WHEN l.Replyto_ = 'author' THEN l.Replyto_ COLLATE Latin1_General_CI_AI
					ELSE '"%%merge list.descshort%%" <%%merge list.name%%@'+sud.sendingHostname+'>' END,
	l.HdrRemove_ = 'X-List-Host'+ @headerLineEndings +'X-URL'+ @headerLineEndings +'List-Owner'+ @headerLineEndings +'List-Subscribe' + @headerLineEndings +'ARC-Authentication-Results' + @headerLineEndings + 'ARC-Message-Signature' + @headerLineEndings + 'ARC-Seal' + @headerLineEndings + 'X-SES-DKIM-SIGNATURE' + @headerLineEndings + 'X-SES-RECEIPT' + @headerLineEndings + 'Authentication-Results' + @headerLineEndings + 'Received-SPF' + @headerLineEndings + 'X-SES-Virus-Verdict' + @headerLineEndings + 'X-SES-Spam-Verdict'
FROM dbo.lists_ AS l
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);


UPDATE l
SET l.SMTPHdrs_ = 'Precedence: list'+ @headerLineEndings +
					'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
					'X-MC-SENDGRIDSUBUSER: '+su.username COLLATE database_default + @headerLineEndings +
					'X-List-unsubscribe: <mailto:%% string map {' + ls.domainName_ COLLATE database_default + ' '+sud.sendingHostname COLLATE database_default +'} "[merge email.unsub]" %%>'+ @headerLineEndings +
					'X-SMTPAPI: {"filters": {"clicktrack": {"settings": {"enable": 0,"enable_text": false}}}, "ip_pool": "'+ipp.poolName COLLATE database_default +'", "category": ["Discussion Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"'+ipp.poolName COLLATE database_default +'"}}'

FROM dbo.lists_ AS l
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS ls ON ls.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);

-- update marketing lists fields

-- don't touch marketing lists that have used segments in the 2 years
declare @twoyearsago datetime = dateadd(YEAR,-2,getdate())
DECLARE @marketingListsThatUseSegments TABLE (listname varchar(100) PRIMARY KEY);
insert into @marketingListsThatUseSegments
select list_
from messages_ m
inner join lists_ l
    on l.name_ = m.list_
	and l.AdminSend_ = 'T'
	and m.SubsetID_ is not null 
	and m.SubsetID_ > 0 
and m.CreatStamp_ > @twoyearsago
group by list_


UPDATE l
SET l.NoListHdr_ = 'F',
	l.AddHeadersAndFooters_ = 'N',
	l.SMTPFrom_ = '"%%merge member.fullname%% (%%list.name%% listserver)" <listsender-%%list.name%%@'+sud.sendingHostname+'>',
	l.Replyto_ = CASE 
		WHEN l.Replyto_ IS NOT NULL AND lyrisarchive.dbo.fn_RegExReplace(ReplyTo_,'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)','') = '' THEN l.Replyto_
		WHEN l.Replyto_ in ('nochange','author') THEN l.Replyto_
		ELSE 'author' 
	END,
	l.HdrRemove_ = 'X-List-Host'+ @headerLineEndings +'X-URL'+ @headerLineEndings +'List-Owner'+ @headerLineEndings +'List-Subscribe' + @headerLineEndings + 'ARC-Authentication-Results' + @headerLineEndings + 'ARC-Message-Signature' + @headerLineEndings + 'ARC-Seal' + @headerLineEndings + 'X-SES-DKIM-SIGNATURE' + @headerLineEndings + 'X-SES-RECEIPT' + @headerLineEndings + 'Authentication-Results' + @headerLineEndings + 'Received-SPF' + @headerLineEndings + 'X-SES-Virus-Verdict' + @headerLineEndings + 'X-SES-Spam-Verdict'
FROM dbo.lists_ AS l
left outer join @marketingListsThatUseSegments mls on mls.listname = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType = 'marketing' and mls.listname is null
AND l.name_ = isnull(@listName,l.name_);

UPDATE l
SET l.SMTPHdrs_ = 'Precedence: list'+ @headerLineEndings +
					'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
					'X-MC-SENDGRIDSUBUSER: '+su.username + @headerLineEndings +
					'X-SMTPAPI: {"ip_pool": "'+ipp.poolName COLLATE database_default +'", "category": ["Marketing Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"'+ipp.poolName COLLATE database_default +'"}}'
FROM dbo.lists_ AS l
left outer join @marketingListsThatUseSegments mls on mls.listname = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
WHERE l.MCSetting_ListType = 'marketing' and mls.listname is null
AND l.name_ = isnull(@listName,l.name_);

RETURN 0;

GO

ALTER PROC dbo.mc_updateListEmailAddress
@orgcode varchar(10), 
@memberNumber varchar(50), 
@oldEmailAddress varchar(255), 
@newEmailAddress varchar(255)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @newEmailAddrDomain varchar(100), @newEmailAddrUsername varchar(100);
	SET @newEmailAddrDomain = LOWER(RIGHT(@newEmailAddress, LEN(@newEmailAddress) - CHARINDEX('@', @newEmailAddress)));
	SET @newEmailAddrUsername = LOWER(LEFT(@newEmailAddress, CHARINDEX('@', @newEmailAddress) - 1));
	
	UPDATE m
	SET emailaddr_ = @newEmailAddress,
		domain_ = @newEmailAddrDomain,
		usernameLC_ = @newEmailAddrUsername,
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',m.list_ + '|' + @newEmailAddrUsername + '@' + @newEmailAddrDomain),2),
		MCEmailKey_usernameLC = @newEmailAddrUsername,
		MCEmailKey_domain = @newEmailAddrDomain
	FROM dbo.members_ as m
	INNER JOIN dbo.lists_format as lf on m.list_ = lf.name
		AND	lf.orgcode = @orgcode
		AND m.externalMemberID = @memberNumber
		AND isnull(m.MCOption_lockAddress,0) = 0
		AND m.emailaddr_ = @oldEmailAddress
	LEFT OUTER JOIN dbo.members_ as existingMembers on existingMembers.emailaddr_ = @newEmailAddress
		AND existingMembers.list_ = m.list_
		AND m.memberID_ <> existingMembers.memberID_ 
	WHERE existingMembers.emailaddr_ IS NULL;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.mc_updateListEmailAddressByListName
@orgcode varchar(10),
@memberNumber varchar(50),
@listName varchar(255),
@oldEmailAddress varchar(255),
@newEmailAddress varchar(255)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @newEmailAddrDomain varchar(100), @newEmailAddrUsername varchar(100);
	SET @newEmailAddrDomain = LOWER(RIGHT(@newEmailAddress, LEN(@newEmailAddress) - CHARINDEX('@', @newEmailAddress)));
	SET @newEmailAddrUsername = LOWER(LEFT(@newEmailAddress, CHARINDEX('@', @newEmailAddress) - 1));
	
	UPDATE m
	SET emailaddr_ = @newEmailAddress,
		domain_ = @newEmailAddrDomain,
		usernameLC_ = @newEmailAddrUsername,
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',m.list_ + '|' + @newEmailAddrUsername + '@' + @newEmailAddrDomain),2),
		MCEmailKey_usernameLC = @newEmailAddrUsername,
		MCEmailKey_domain = @newEmailAddrDomain
	FROM dbo.members_ as m
	INNER JOIN dbo.lists_format as lf on m.list_ = lf.[name]
		AND	lf.orgcode = @orgcode
		AND lf.[name] = @listName
		AND m.externalMemberID = @memberNumber
		AND m.emailaddr_ = @oldEmailAddress
	LEFT OUTER JOIN dbo.members_ as existingMembers on existingMembers.emailaddr_ = @newEmailAddress
		AND existingMembers.list_ = m.list_
		AND m.memberID_ <> existingMembers.memberID_
	WHERE existingMembers.emailaddr_ IS NULL;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.mc_updateListMembershipFromQueue
@itemID int

AS

BEGIN TRY

	DECLARE @siteID int, @orgID int, @orgCode varchar(10), @siteCode varchar(10), @listName varchar(100),
		@queueTypeID int, @readyToProcessStatusID int, @processingStatusID int,	@progressLog VARCHAR(max),
		@errorLog VARCHAR(max), @emailSubject VARCHAR(500), @emailTitle VARCHAR(300), @escalateError BIT,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno INT, @proc sysname, @lineno INT, 
		@defaultMembertype VARCHAR(100), @defaultSubType VARCHAR(100), @defaultMCOption_keepActive BIT, 
		@defaultMCOption_lockAddress BIT, @defaultMCOption_lockName BIT, @isAutoManageActive BIT, @message VARCHAR(500),
		@lastrowcount INT, @thisListOneWayList BIT, @expireDateCutoff DATETIME, @runByMemberID INT;

	SET @escalateError = 0;
	SET @errorLog = '';
	SET @defaultMembertype = 'normal';
	SET @defaultSubType = 'mail';
	SET @defaultMCOption_keepActive = 0;
	SET @defaultMCOption_lockAddress = 0;
	SET @defaultMCOption_lockName = 0;
	SET @expireDateCutoff = DATEADD(year,-1,GETDATE());

	SELECT @queueTypeID = queueTypeID
	FROM membercentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'lyrisListSync';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM membercentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'readyToProcess';

	SELECT @processingStatusID = queueStatusID
	FROM membercentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'processingItem';

	SELECT @siteCode = siteCode, @listName = listName
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @listName IS NULL
		GOTO on_end;

	UPDATE membercentral.platformQueue.dbo.queue_lyrisListSync
	SET statusID = @processingStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	EXEC membercentral.membercentral.dbo.lists_getListMembersForLyris @siteCode=@siteCode, @listName=@listName;

	SELECT @siteID = siteID, @orgID = orgID, @orgCode = orgCode, @isAutoManageActive = isAutoManageActive
	FROM membercentral.datatransfer.dbo.ListsForLyris
	WHERE siteCode = @siteCode
	AND list_ = @listName;

	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tmpLogMessages (autoid INT IDENTITY(1,1), siteID INT, memberID INT, listName VARCHAR(100), msg VARCHAR(500));

	DELETE FROM dbo.MC_ListMembersForLyris WHERE sitecode = @sitecode and list_ = @listName;
	
	-- process only if list still exists in Lyris
	IF EXISTS (SELECT 1 FROM dbo.lists_ WHERE name_ = @listName) BEGIN
		
		INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
			functionName, isAutoManageActive, domain_, usernameLC_)
		SELECT siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
			functionName, @isAutoManageActive, RIGHT(emailaddr_,LEN(emailaddr_)-CHARINDEX('@',emailaddr_)), 
			LEFT(emailaddr_,CHARINDEX('@',emailaddr_)-1)
		FROM membercentral.datatransfer.dbo.ListMembersForLyris
		WHERE siteCode = @siteCode
		AND list_ = @listName;

		-- null blank emails
		UPDATE dbo.MC_ListMembersForLyris
		SET emailaddr_ = NULL
		WHERE ltrim(rtrim(ISNULL(emailaddr_,''))) = ''
		AND sitecode = @sitecode
		AND list_ = @listName;

		IF EXISTS (SELECT adminSend_ FROM dbo.lists_ WHERE name_ = @listName AND adminSend_ = 'T')
			SET @thisListOneWayList = 1;
		ELSE
			SET @thisListOneWayList = 0;

		SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Start Processing List changes - AutoManage: ' + cast(@isAutoManageActive AS VARCHAR(5));
		SET @progressLog = @progressLog + '<br/>' + @message;

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding names to update';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET updateStatus = 'UpdateName'
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.fullname_ <> m.fullname_
				AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Name changed FROM ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				-- update the full names that have been marked
				UPDATE m 
				SET m.fullname_ = lm.fullname_
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated names - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding records to update (Pass 1)';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail' ELSE 'UpdateEmail' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
			LEFT OUTER JOIN dbo.members_ existingAddresses ON lm.list_ = existingAddresses.list_
				AND (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ AND existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			WHERE existingAddresses.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				IF @isAutoManageActive = 1 BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + isnull(lm.usernamelc_,m.usernameLc_) collate SQL_Latin1_General_CP1_CI_AS + '@' + isnull(lm.domain_,m.domain_) collate SQL_Latin1_General_CP1_CI_AS),2),
						MCEmailKey_usernameLC = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey_domain = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding records to update (Pass 2)';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET lm.updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail1' ELSE 'UpdateEmail1' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND updateStatus IS NULL 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ IS NOT NULL
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN dbo.members_ existingAddresses ON lm2.list_ = existingAddresses.list_
					AND (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ AND existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				WHERE lm2.list_ = @listName
				AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				IF @isAutoManageActive = 1
				BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + isnull(lm.usernamelc_,m.usernameLc_) collate SQL_Latin1_General_CP1_CI_AS + '@' + isnull(lm.domain_,m.domain_) collate SQL_Latin1_General_CP1_CI_AS),2),
						MCEmailKey_usernameLC = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey_domain = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m 
						on lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);
					
					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding memberships to expire';
			SET @progressLog = @progressLog + '<br/>' + @message;

			INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			SELECT @siteID, @siteCode, @orgID, @orgcode, m.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				NULL AS functionName, 'expired' AS updateStatus, @isAutoManageActive
			FROM dbo.members_ m
			LEFT OUTER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID COLLATE Latin1_General_CI_AI
				AND m.list_ = lm.list_
			WHERE m.list_ = @listName
			AND m.membertype_ in ('confirm','held','normal')
			AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
			AND lm.autoID IS NULL AND (m.MCOption_keepActive IS NULL or m.MCOption_keepActive=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				UPDATE m 
				SET m.membertype_ = 'expired',
					m.ExpireDate_ = GETDATE()
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'expired';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Expired members - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding reactivations';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm 
			SET lm.updateStatus = 'reactivate'
			FROM dbo.members_ m
			INNER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
				AND m.list_ = lm.list_
				AND m.list_ = @listName
				AND m.membertype_ = 'expired'
				AND lm.functionName in ('managePopulation','manageStatus');

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				UPDATE m 
				SET m.membertype_ = 'normal',
					m.ExpireDate_ = NULL
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Reactivated memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding new memberships';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET lm.updateStatus = 'added'
			FROM 
				(
					SELECT min(autoID) AS autoID FROM MC_ListMembersForLyris WHERE list_ = @listName AND updateStatus IS NULL AND functionName in ('managePopulation') GROUP BY emailaddr_
				) deduped
				INNER JOIN MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					AND NULLif(lm.emailaddr_,'') IS NOT NULL
				LEFT OUTER JOIN dbo.members_ m
					on lm.list_ = m.list_
					AND (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = RIGHT(lm.emailaddr_,LEN(lm.emailaddr_)-CHARINDEX('@',lm.emailaddr_)) AND m.usernamelc_ = LEFT(lm.emailaddr_,CHARINDEX('@',lm.emailaddr_)-1))
					)
			WHERE m.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				FROM MC_ListMembersForLyris
				WHERE list_ = @listName
				AND updateStatus = 'added';

				INSERT INTO dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_, mcemailkey,MCEmailKey_usernameLC,MCEmailKey_domain)
				SELECT GETDATE() AS DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype AS membertype_, @defaultSubType AS subype_,
					mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + lm.usernamelc_ collate SQL_Latin1_General_CP1_CI_AS + '@' + lm.domain_ collate SQL_Latin1_General_CP1_CI_AS),2),
					lm.usernamelc_, lm.domain_
				FROM MC_ListMembersForLyris lm
				WHERE lm.list_ = @listName
				AND lm.updateStatus = 'added';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Added new memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding old expired memberships to delete';
				SET @progressLog = @progressLog + '<br/>' + @message;

				INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				SELECT @siteID, @siteCode, @orgID, @orgcode, m.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					NULL AS functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' AS updateStatus, @isAutoManageActive
				FROM dbo.members_ m  
				WHERE m.list_ = @listName
				AND m.membertype_ = 'expired'
				AND m.ExpireDate_ < @expireDateCutoff
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''

				SET @lastrowcount = @@rowcount;

				IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
					DELETE m
					FROM MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID
						AND lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Expired members deleted - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH
	END

	-- remove queue item after processing
	DELETE
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID AS VARCHAR(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID AS VARCHAR(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID AS VARCHAR(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END
	
END TRY
BEGIN CATCH
	-- remove queue item
	DELETE
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID;

	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	SET @escalateError = 1;
END CATCH

IF LEN(rtrim(ltrim(@errorLog))) > 0 BEGIN
	SET @errorLog = @errorLog + '<br/><br/>' + ISNULL(@progressLog,'');
	SET @emailSubject =  convert(varchar(19), GETDATE(), 121) + ' - MC ListSync Process: Errors Generated';
	SET @emailTitle =  'MC ListSync Process: Errors Generated';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailTitle, @messageContent=@errorLog, @forDev=1;
END

IF @escalateError = 1
	RAISERROR (@errmsg, @severity, @state, @errno);

IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

on_end:
RETURN 0;
GO

ALTER PROCEDURE [dbo].[up_UpdateListMembership] 
	@listname varchar(200),
	@emailaddress varchar(500),
	@newemailaddress varchar(500),
	@fullname varchar(200),
	@membertype varchar(200),
	@subtype varchar(200),
	@externalMemberID varchar(200)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @usernameLC varchar(200)
	DECLARE @domain varchar(200)
	DECLARE @successFlag bit
	DECLARE @failureReason varchar(500)
	DECLARE @MCEmailKey varchar(75)

	DECLARE @ATposition smallint

	select list_, emailaddr_, fullname_, membertype_, subtype_ ,externalMemberID
	from members_ 
	where list_ = @listname and emailaddr_ = @emailaddress

	if (len(@newemailaddress) > 0 and lower(@newemailaddress) <> lower(@emailaddress))
		begin
			select @ATposition = charindex('@',@newemailaddress,0)
			select @usernameLC = left(@newemailaddress,@ATposition-1)
			select @domain = right(@newemailaddress,len(@newemailaddress)-@ATposition)

			select @MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listname + '|' + @usernameLC + '@' + @domain),2)


			if not exists (select memberid_ from members_ where list_ = @listname and emailaddr_ = @newemailaddress)
				begin
					update members_ 
					set
						domain_ = @domain,
						emailaddr_ = @newemailaddress,
						fullname_= @fullname,
						membertype_= @membertype,
						subtype_= @subtype,
						usernameLc_ = @usernameLC,
						externalMemberID = @externalMemberID,
						MCEmailKey = @MCEmailKey,
						MCEmailKey_usernameLC = @usernameLC,
						MCEmailKey_domain = @domain
					where
						list_= @listname
						and emailaddr_ = @emailaddress

					set @successFlag = 1
				end
			else
				begin
					set @successFlag = 0
					set @failureReason = 'New email address is already a member of list'
				end


			select list_, emailaddr_, fullname_, membertype_, subtype_,externalMemberID
			from members_ 
			where list_ = @listname and emailaddr_ = @newemailaddress


		end
	else
		begin
			update members_ 
			set
				fullname_= @fullname,
				membertype_= @membertype,
				subtype_= @subtype,
				externalMemberID = @externalMemberID
			where
				list_= @listname
				and emailaddr_ = @emailaddress

			set @successFlag = 1

			select list_, emailaddr_, fullname_, membertype_, subtype_ ,externalMemberID
			from members_ 
			where list_ = @listname and emailaddr_ = @emailaddress
		end

	select @successFlag as successFlag, @failureReason as message



END
GO

use trialslyris1;
GO
-- break update into groups based on list name

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between '0' and 'a'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'a' and 'e'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'e' and 'j'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'j' and 'm'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'm' and 'n'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'n' and 'o'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'o' and 'r'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 'r' and 's'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 's' and 't'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ between 't' and 'u'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where list_ > 'u'
and (MCEmailKey is null or MCEmailKey = '')
GO

update members_ set 
	MCEmailKey_usernameLC = usernamelc_,
	MCEmailKey_domain = domain_
where MCEmailKey_usernameLC is null
GO
