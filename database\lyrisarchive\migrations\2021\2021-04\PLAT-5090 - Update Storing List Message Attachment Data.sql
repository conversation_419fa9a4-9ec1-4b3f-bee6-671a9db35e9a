use lyrisarchive
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[messageAttachmentSearchText]') AND type in (N'U'))
DROP TABLE [dbo].[messageAttachmentSearchText]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[messageAttachments]') AND type in (N'U'))
DROP TABLE [dbo].[messageAttachments]
GO


CREATE TABLE dbo.messageAttachments (
	messageID int NOT NULL,
	savedFilename varchar(255) NOT NULL,
	listID int NOT NULL,
	attachedMessagePath varchar(50) NULL,
	attachmentIndex int NOT NULL,
	originalFilename varchar(255) NOT NULL,
	extension varchar(50) NOT NULL,
	bytes int NOT NULL,
 CONSTRAINT [PK_messageAttachments] PRIMARY KEY CLUSTERED 
(
	messageID ASC,
	savedFilename ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE dbo.messageAttachments ADD CONSTRAINT
	FK_messageAttachments_messages_ FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.messages_
	(
	messageID_
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

ALTER TABLE dbo.messageAttachments ADD CONSTRAINT
	FK_messageAttachments_messageLists FOREIGN KEY
	(
	listID
	) REFERENCES dbo.messageLists
	(
	listID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

CREATE TABLE dbo.messageAttachmentSearchText (
	attachmentTextID int IDENTITY(1,1) NOT NULL,
	messageID int NOT NULL,
	savedFilename varchar(255) NOT NULL,
	searchText varchar(max) NULL,
 CONSTRAINT [PK_messageAttachmentSearchText] PRIMARY KEY CLUSTERED 
(
	attachmentTextID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.messageAttachmentSearchText ADD CONSTRAINT
	FK_messageAttachmentSearchText_messages_ FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.messages_
	(
	messageID_
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

CREATE NONCLUSTERED INDEX [IX__messageAttachmentSearchText__messageID_savedFilename] ON [dbo].[messageAttachmentSearchText]
(
	[messageID] ASC,
	[savedFilename] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)

GO

