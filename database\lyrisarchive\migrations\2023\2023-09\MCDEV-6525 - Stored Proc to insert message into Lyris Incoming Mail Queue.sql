use lyrisCustom;
GO

CREATE PROC dbo.lists_insertIncomingMail
@dateHeader varchar(100), 
@fromHeader varchar(238),
@fromEmail varchar(200), 
@toHeader varchar(200),
@toEmail varchar(200),
@subject varchar(1000),
@header varchar(max),
@body varchar(max),
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	

	insert into trialslyris1.dbo.inmail_ (
		Body_, ByEmail_, Created_, HdrAll_, HdrDate_, HdrFrom_, HdrFromSpc_, 
		HdrSubject_,HdrTo_, ModerApp_, Priority_, Status_, SubsetID_, Title_, 
		To_, Transact_, Type_, MaxRecips_,TrackOpens_, DetectHTML_
	)

	select 
		Body_ = @Body,
		ByEmail_ = 'T',
		Created_ = getdate(),
		hdrall_ = @header,
		HdrDate_ = @dateHeader,
		HdrFrom_ = @fromHeader ,
		HdrFromSpc_=@fromEmail,
		HdrSubject_ = @subject,
		HdrTo_ = @toHeader,
		ModerApp_ = 'F',
		Priority_=1, 
		Status_ = 'new',
		SubsetID_=0, 
		Title_ = cast(getdate() as varchar(100)), 
		To_ = @toEmail, 
		Transact_ = FORMAT(GETDATE(), 'ddd, dd MMM yyyy HH:mm:ss') + ' - Email message received.' + char(10) ,
		Type_ = 'unknown',
		MaxRecips_=0, TrackOpens_='F', DetectHTML_='F'


	set @messageID = SCOPE_IDENTITY()

	RETURN 0;

END TRY
BEGIN CATCH
	set @messageID = 0;
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
