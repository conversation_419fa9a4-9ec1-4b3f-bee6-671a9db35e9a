use lyrisCustom
GO

CREATE TABLE dbo.mkt_membersearch (memberID int NOT NULL)  ON [PRIMARY]
GO
CREATE TABLE dbo.mkt_lywebuser (memberID int NOT NULL)  ON [PRIMARY]
GO


-- run these in membercentral
insert into lyris.lyrisCustom.dbo.mkt_membersearch (memberID)
select distinct mnp.memberID
from search.dbo.tblSearchHistory as sh
inner join search.dbo.tblSearchBuckets as sb on sb.bucketID = sh.bucketIDorigin
	and sb.bucketTypeID = 3
inner join membercentral.dbo.ams_networkProfiles as np on np.profileID = sh.profileID
inner join membercentral.dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
where sh.dateEntered > '6/1/2021'
GO

select distinct ss.memberID
into dataTransfer.dbo.mkt_lywebuser
from dbo.statsSessions as ss
inner join dbo.statsAppHits as sah on sah.siteID = ss.siteID and sah.sessionid = ss.sessionid
	and sah.appSectionID = 31
where ss.ignore = 0
and ss.dateentered > '6/1/2021'
and ss.memberID is not null;
GO

insert into lyris.lyrisCustom.dbo.mkt_lywebuser (memberID)
select memberID
from dataTransfer.dbo.mkt_lywebuser;
GO


use lyrisArchive
GO

CREATE TABLE lyrisCustom.dbo.mkt_uniqueLists (listID int PRIMARY KEY, list_ varchar(100), siteID int, orgid int, sitecode varchar(10), 
	msgPastYear int, isMostActiveList bit);

-- get all discussion lists for TLAs
insert into lyrisCustom.dbo.mkt_uniqueLists (listID, list_, siteID, orgID, sitecode)
select ml.listID, l.name_, s.siteID, s.orgID, lf.orgcode
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name
inner join membercentral.membercentral.dbo.sites as s on s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI
inner join membercentral.membercentral.dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1 and ns.networkID = 3
inner join dbo.messageLists ml on ml.list = l.name_
where l.MCSetting_ListType = 'discussion'
and l.adminSend_ = 'F';
GO

-- how many messages in the past year
update l 
set l.msgPastYear = temp.numMessages
from lyrisCustom.dbo.mkt_uniqueLists as l
inner join (
	select m.listID, count(*) as numMessages
	from dbo.messages_ m
	inner join lyrisCustom.dbo.mkt_uniqueLists as ul on m.listID = ul.listID
	where m.creatStamp_ between '6/1/2021' and '7/1/2022'
	group by m.listID
) temp on temp.listid = l.listID;
GO

-- remove those with no/low message counts in the past year
delete lyrisCustom.dbo.mkt_uniqueLists where msgPastYear is null;
delete lyrisCustom.dbo.mkt_uniqueLists where msgPastYear <= 24;
GO

-- determine most active list by org
update l 
set l.isMostActiveList = case when l.listID = tmp.MainList then 1 else 0 end
from lyrisCustom.dbo.mkt_uniqueLists as l
inner join (
	select distinct siteCode, FIRST_VALUE(listID) OVER (PARTITION BY siteCode ORDER BY msgPastYear DESC ROWS UNBOUNDED PRECEDING) AS MainList
	from lyrisCustom.dbo.mkt_uniqueLists
) as tmp on tmp.siteCode = l.siteCode;
GO

-- who is normal on the main list
select l.listID, l.orgID, m.MemberID_, m.ExternalMemberID
into lyrisCustom.dbo.mkt_NormalOnMain
from trialslyris1.dbo.members_ as m
inner join lyrisCustom.dbo.mkt_uniqueLists as l on l.list_ = m.List_ COLLATE Latin1_General_CI_AI and l.isMostActiveList = 1
where m.membertype_ in ('normal','held')
and nullif(ExternalMemberID,'') is not null;
GO

-- who is normal on a non-main list
select l.listID, l.orgID, m.MemberID_, m.ExternalMemberID
into lyrisCustom.dbo.mkt_NormalOnNonMain
from trialslyris1.dbo.members_ as m
inner join lyrisCustom.dbo.mkt_uniqueLists as l on l.list_ = m.List_ COLLATE Latin1_General_CI_AI and l.isMostActiveList = 0
where m.membertype_ in ('normal','held')
and nullif(ExternalMemberID,'') is not null;
GO

-- who is expired on the main list
select l.listID, l.orgID, m.MemberID_, m.ExternalMemberID
into lyrisCustom.dbo.mkt_ExpOnMain
from trialslyris1.dbo.members_ as m
inner join lyrisCustom.dbo.mkt_uniqueLists as l on l.list_ = m.List_ COLLATE Latin1_General_CI_AI and l.isMostActiveList = 1
where m.membertype_ in ('expired','unsub')
and nullif(ExternalMemberID,'') is not null;
GO

-- all MC members in TSExportOK
select m.memberID, m.membernumber, ul.siteID
into lyrisCustom.dbo.mkt_MCMemberPool
from membercentral.membercentral.dbo.ams_members as m
inner join lyrisCustom.dbo.mkt_uniqueLists as ul on ul.orgID = m.orgID
inner join membercentral.membercentral.dbo.cache_members_groups as mg on mg.memberID = m.memberID
inner join membercentral.membercentral.dbo.ams_groups as g on g.orgID = m.orgID and g.groupID = mg.groupID and g.groupCode = 'TSExportOK'
where m.status = 'A'
group by m.memberID, m.membernumber, ul.siteID;
GO

-- which members are not on any of the lists
select distinct tmpm.memberID, tmpm.siteID
into lyrisCustom.dbo.mkt_MCPoolNotOnAnyList
from lyrisCustom.dbo.mkt_MCMemberPool as tmpm
where not exists (
	select m.memberID_ 
	from trialslyris1.dbo.members_ as m
	inner join lyrisCustom.dbo.mkt_uniqueLists as l on l.list_ = m.List_ COLLATE Latin1_General_CI_AI
	where m.ExternalMemberID = tmpm.membernumber COLLATE Latin1_General_CI_AI and l.siteID = tmpm.siteID
);
GO

-- put all members together
select listID, orgID, MemberID_, ExternalMemberID, cast(null as int) as MCMemberID
into lyrisCustom.dbo.mkt_LYMembers
from lyrisCustom.dbo.mkt_NormalOnMain
	union
select listID, orgID, MemberID_, ExternalMemberID, null
from lyrisCustom.dbo.mkt_NormalOnNonMain
	union
select listID, orgID, MemberID_, ExternalMemberID, null
from lyrisCustom.dbo.mkt_ExpOnMain;
GO

UPDATE ly
set ly.MCMemberID = m.activeMemberID
from lyrisCustom.dbo.mkt_LYMembers as ly
inner join membercentral.membercentral.dbo.ams_members as m on m.orgID = ly.orgID and m.membernumber = ly.ExternalMemberID COLLATE Latin1_General_CI_AI and m.status = 'A';
GO

delete from lyrisCustom.dbo.mkt_LYMembers
where MCMemberID is null;
GO

USE [lyrisCustom]
GO
CREATE NONCLUSTERED INDEX [IX_LYMembers_listID__orgID__memberID__Includes2] ON [dbo].[mkt_LYMembers]
(
	[listID] ASC,
	[orgID] ASC,
	[MemberID_] ASC
)
INCLUDE([ExternalMemberID],[MCMemberID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX_NormalOnMain__memberID] ON [dbo].[mkt_NormalOnMain]
(
	[MemberID_] ASC
)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX_NormalOnNonMain__memberID] ON [dbo].[mkt_NormalOnNonMain]
(
	[MemberID_] ASC
)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX_ExpOnMain__memberID] ON [dbo].[mkt_ExpOnMain]
(
	[MemberID_] ASC
)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX_UniqueLists_listID__isMostActiveList__list_] ON [dbo].[mkt_UniqueLists]
(
	[listID] ASC,
	[isMostActiveList] ASC,
	[list_] ASC
)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

select l.sitecode, l.listID, tmp.MemberID_, tmp.MCMemberID, m.lastname, m.firstname, lm.EmailAddr_, 
	NormalOnMostActiveList = CASE WHEN tmpMain.memberID_ is not null then 1 else 0 end,
	NormalOnNonMostActiveList = CASE WHEN tmpNonMain.memberID_ is not null then 1 else 0 end,
	UnSubOnMostActiveList = CASE WHEN tmpExp.memberID_ is not null then 1 else 0 end,
	0 as numberMessagesAll, 0 as numberMessagesMain, lm.MemberType_, lm.subtype_, lm.receiveMCThreadDigest,
	0 as deliveryMethod, 0 as memberRow
into lyrisCustom.dbo.mkt_StagingData
from lyrisCustom.dbo.mkt_LYMembers as tmp
inner join lyrisCustom.dbo.mkt_uniqueLists as l on l.listID = tmp.listID
inner join lyrisCustom.dbo.mkt_uniqueLists as l2 on l2.sitecode = l.siteCode and l2.isMostActiveList = 1
inner join trialslyris1.dbo.members_ as lm on lm.MemberID_ = tmp.MemberID_
LEFT OUTER JOIN membercentral.membercentral.dbo.ams_members as m on m.memberID = tmp.MCMemberID
LEFT OUTER JOIN lyrisCustom.dbo.mkt_NormalOnMain as tmpMain on tmpMain.memberID_ = tmp.MemberID_
LEFT OUTER JOIN lyrisCustom.dbo.mkt_NormalOnNonMain as tmpNonMain on tmpNonMain.memberID_ = tmp.MemberID_
LEFT OUTER JOIN lyrisCustom.dbo.mkt_ExpOnMain as tmpExp on tmpExp.memberID_ = tmp.MemberID_;
GO

CREATE NONCLUSTERED INDEX [IX_mkt_StagingData_MemberID] ON [dbo].[mkt_StagingData]
(
	[MemberID_] ASC
)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

select mem.EmailAddr_, l.listID, count(m.MessageID_) as msgCount
INTO lyrisCustom.dbo.mkt_LYMessageCount
from lyrisCustom.dbo.mkt_StagingData as tmp
inner join trialslyris1.dbo.members_ mem on mem.MemberID_ = tmp.MemberID_
inner join lyrisCustom.dbo.mkt_uniqueLists l on tmp.listID = l.listID
inner join lyrisArchive.dbo.messages_ m on m.listID = l.listID 
	and m.creatStamp_ between '6/1/2021' and '7/1/2022'
	and mem.EmailAddr_ = m.HdrFromSpc_
group by mem.EmailAddr_, l.listID;
GO

/*
update sd
set sd.numberMessagesAll = tmp2.msgCount
from lyrisCustom.dbo.mkt_StagingData as sd
inner join (
	select memberID_, sum(msgCount) as msgCount
	from lyrisCustom.dbo.mkt_LYMessageCount
	group by memberID_
) as tmp2 on tmp2.memberID_ = sd.MemberID_;
GO

update sd
set sd.numberMessagesMain = tmp2.msgCount
from lyrisCustom.dbo.mkt_StagingData as sd
inner join (
	select mc.memberID_, sum(mc.msgCount) as msgCount
	from lyrisCustom.dbo.mkt_LYMessageCount as mc
	inner join lyrisCustom.dbo.mkt_uniqueLists l on l.listID = mc.listID and l.isMostActiveList = 1
	group by mc.memberID_
) as tmp2 on tmp2.memberID_ = sd.MemberID_;
GO
*/

update sd2
set sd2.deliveryMethod = tmp.delivery
from lyrisCustom.dbo.mkt_StagingData as sd2
inner join (
	select sd.memberid_, delivery = case 
		when sd.SubType_ = 'mail' and sd.receiveMCThreadDigest = 1 then 5
		when sd.SubType_ = 'mail' then 4
		when sd.SubType_ in ('index','digest','mimedigest') or sd.receiveMCThreadDigest = 1 then 3
		when sd.SubType_ = 'nomail' then 1
		else 0
		end
	from lyrisCustom.dbo.mkt_StagingData as sd
	inner join lyrisCustom.dbo.mkt_uniqueLists l on l.listID = sd.listID and l.isMostActiveList = 1
	where sd.MemberType_ = 'normal'
) as tmp on tmp.memberID_ = sd2.memberID_;
GO

-- pick the record we will use as the main member record
UPDATE sd2
set sd2.memberRow = tmp.memberRow
from lyrisCustom.dbo.mkt_StagingData as sd2
inner join (
	select sd.memberID_, ROW_NUMBER() OVER (PARTITION BY l.sitecode, sd.MCMemberID ORDER BY l.isMostActiveList desc, memberID_ asc) as MemberRow
	from lyrisCustom.dbo.mkt_StagingData as sd
	inner join lyrisCustom.dbo.mkt_uniqueLists l on l.listID = sd.listID
) as tmp on tmp.memberID_ = sd2.memberID_;
GO

select sitecode, MCMemberID, max(NormalOnMostActiveList) as NormalOnMostActiveList,
	max(NormalOnNonMostActiveList) as NormalOnNonMostActiveList,
	max(UnSubOnMostActiveList) as UnSubOnMostActiveList
into lyrisCustom.dbo.mkt_StagingDataByMCMember
from lyrisCustom.dbo.mkt_StagingData
group by sitecode, MCMemberID
GO

select distinct sd.siteCode, sd.MCMemberID, sd.EmailAddr_, lc.listID, lc.msgCount
into lyrisCustom.dbo.mkt_LYMessageCountByMCMember
from lyrisCustom.dbo.mkt_StagingData as sd
inner join lyrisCustom.dbo.mkt_LYMessageCount as lc on lc.EmailAddr_= sd.EmailAddr_;
GO

select sitecode, SiteMostActiveList, MCMemberID, lastname, firstname, EmailAddr_, 
	NormalOnMostActiveList = case when NormalOnMostActiveList = 1 then 'Yes' else 'No' end,
	NormalOnNonMostActiveList = case when NormalOnNonMostActiveList = 1 then 'Yes' else 'No' end,
	UnSubOnMostActiveList = case when UnSubOnMostActiveList = 1 then 'Yes' else 'No' end,
	NumMessagesAllListsLast12Months, NumMessagesActiveListLast12Months,
	ContributorStatusAllLists = case 
		when NumMessagesAllListsLast12Months >= 20 then 'Active Contributor' 
		when NumMessagesAllListsLast12Months >= 10 then 'Medium Contributor'
		when NumMessagesAllListsLast12Months >= 1 then 'Low Contributor'
		else 'Never Contributes'
		end,
	ContributorStatusActiveList = case
		when NumMessagesActiveListLast12Months >= 20 then 'Active Contributor' 
		when NumMessagesActiveListLast12Months >= 10 then 'Medium Contributor'
		when NumMessagesActiveListLast12Months >= 1 then 'Low Contributor'
		else 'Never Contributes'
		end,
	DeliveryMethodActiveList, WebUser, ListSearchUser
into lyrisCustom.dbo.mkt_FinalData
from (
	select l.sitecode, la.list_ as SiteMostActiveList, sd.MCMemberID, sd.lastname, sd.firstname, sd.EmailAddr_,
		sdm.NormalOnMostActiveList, sdm.NormalOnNonMostActiveList, sdm.UnSubOnMostActiveList,
		isnull((select sum(msgCount) from lyrisCustom.dbo.mkt_LYMessageCountByMCMember where sitecode = l.sitecode and MCMemberID = sd.MCMemberID),0) as NumMessagesAllListsLast12Months,
		isnull((select sum(msgCount) from lyrisCustom.dbo.mkt_LYMessageCountByMCMember where sitecode = l.sitecode and MCMemberID = sd.MCMemberID and listID = la.listID),0) as NumMessagesActiveListLast12Months,
		DeliveryMethodActiveList = case
			when DeliveryMethod = 5 then 'All Messages and Digest'
			when DeliveryMethod = 4 then 'All Messages'
			when DeliveryMethod = 3 then 'Digest'
			else 'No Mail'
			end,
		WebUser = case when wu.memberID is not null then 'Yes' else 'No' end,
		ListSearchUser = case when ms.memberID is not null then 'Yes' else 'No' end
	from lyrisCustom.dbo.mkt_StagingData as sd
	inner join lyrisCustom.dbo.mkt_uniqueLists l on l.listID = sd.listID
	inner join lyrisCustom.dbo.mkt_uniqueLists la on la.siteID = l.siteID and la.isMostActiveList = 1
	inner join lyrisCustom.dbo.mkt_StagingDataByMCMember as sdm on sdm.sitecode = l.sitecode and sdm.MCMemberID = sd.MCMemberID
	left outer join lyris.lyrisCustom.dbo.mkt_membersearch as ms on ms.memberID = sd.MCMemberID
	left outer join lyris.lyrisCustom.dbo.mkt_lywebuser as wu on wu.memberID = sd.MCMemberID
	where sd.memberRow = 1
) as finalData
GO

delete fd
from lyrisCustom.dbo.mkt_FinalData as fd
where MCMemberID not in (select memberID from lyrisCustom.dbo.mkt_MCMemberPool)
GO

INSERT INTO lyrisCustom.dbo.mkt_FinalData
select s.sitecode, l.list_, m.memberID, m.lastname, m.firstname, me.email, 'No', 'No', 'No', 0, 0, 'Never Contributes', 'Never Contributes', 'No Mail', 'No', 'No'
from membercentral.membercentral.dbo.ams_members as m
inner join lyrisCustom.dbo.mkt_MCPoolNotOnAnyList as nal on nal.memberID = m.memberID
inner join membercentral.membercentral.dbo.sites as s on s.siteID = nal.siteID
inner join lyrisCustom.dbo.mkt_uniqueLists l on l.siteID = s.siteID and l.isMostActiveList = 1
inner join membercentral.membercentral.dbo.ams_memberEmails as me on me.memberID = m.memberID and me.email <> ''
inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1;
GO

-- anyone from group not included yet (maybe only member of one way lists)
INSERT INTO lyrisCustom.dbo.mkt_FinalData
select s.sitecode, l.list_, m.memberID, m.lastname, m.firstname, me.email, 'No', 'No', 'No', 0, 0, 'Never Contributes', 'Never Contributes', 'No Mail', 'No', 'No'
from membercentral.membercentral.dbo.ams_members as m
inner join lyrisCustom.dbo.mkt_MCMemberPool as nal on nal.memberID = m.memberID
inner join membercentral.membercentral.dbo.sites as s on s.siteID = nal.siteID
inner join lyrisCustom.dbo.mkt_uniqueLists l on l.siteID = s.siteID and l.isMostActiveList = 1
inner join membercentral.membercentral.dbo.ams_memberEmails as me on me.memberID = m.memberID and me.email <> ''
inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
where m.memberID not in (select MCMemberID from lyrisCustom.dbo.mkt_FinalData)
GO

select sitecode, SiteMostActiveList, MCMemberID, lastname, firstname, EmailAddr_, NormalOnMostActiveList,
	NormalOnNonMostActiveList, UnSubOnMostActiveList, NumMessagesAllListsLast12Months, NumMessagesActiveListLast12Months,
	ContributorStatusAllLists, ContributorStatusActiveList, DeliveryMethodActiveList, WebUser, ListSearchUser
from lyrisCustom.dbo.mkt_FinalData
where DeliveryMethodActiveList = 'All Messages and Digest'
order by sitecode, lastname, firstname;

