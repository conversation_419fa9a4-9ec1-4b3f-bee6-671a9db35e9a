use trialsmith
GO

ALTER PROC dbo.trialsmith_getMarketingListMembers
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblEmail') IS NOT NULL
		DROP TABLE #tblEmail;
	IF OBJECT_ID('tempdb..#allowedGroupPrints') IS NOT NULL
		DROP TABLE #allowedGroupPrints;
	IF OBJECT_ID('tempdb..#sitePlatforms') IS NOT NULL
		DROP TABLE #sitePlatforms;
	IF OBJECT_ID('tempdb..#uniqueEmails_MC') IS NOT NULL
		DROP TABLE #uniqueEmails_MC;
	IF OBJECT_ID('tempdb..#uniqueEmails_TLASITES') IS NOT NULL
		DROP TABLE #uniqueEmails_TLASITES;

	CREATE TABLE #tblEmail (email varchar(300) PRIMARY KEY);
	CREATE TABLE #sitePlatforms (orgcode varchar(10), isLiveOnNewPlatform bit, isPortalLaunched char(1),siteID int, orgID int, siteResourceID int);
	CREATE TABLE #allowedGroupPrints (groupPrintID int PRIMARY KEY);
	CREATE TABLE #uniqueEmails_MC (memberID int PRIMARY KEY, email varchar(300), depomemberdataid int);
	CREATE TABLE #uniqueEmails_TLASITES (orgmemberdataID int PRIMARY KEY, email varchar(300), depomemberdataid int);

	declare @functionID int;

	select @functionID=functionID
	from membercentral.dbo.cms_siteResourceFunctions
	where functionname = 'TrialSmithAllowed';

	-- -- clear transfer table
	truncate table transfer.dbo.trialsmithMarketingListPopulation;

	-- paid TS emails
	INSERT INTO #tblEmail (email)
	select distinct rtrim(ltrim(email))
	from dbo.depomemberdata 
	where membertype in (2,3,4,5,6,8) 
	and renewaldate > dateadd(year,-1,getdate()) 
	and mailcode = 'A'
	and email like '%@%'
		union 
	select distinct rtrim(ltrim(a.email))
	from dbo.depomemberdata m
	inner join dbo.addedEmailsID a on m.depomemberdataid = a.depomemberdataid
	and m.membertype in (2,3,4,5,6,8) 
	and m.renewaldate > dateadd(year,-1,getdate())
	and mailcode = 'A'
	and a.email like '%@%'
		union
	--add MC assoc addresses that are linked to paid trialsmith accounts
	select distinct rtrim(ltrim(me.email))
	from dbo.depomemberdata m
	inner join membercentral.dbo.ams_networkProfiles np
		on np.depomemberdataid = m.depomemberdataid
		and np.[status]='A'
		and m.membertype in (2,3,4,5,6,8) 
		and m.renewaldate > dateadd(year,-1,getdate()) 
		and m.mailcode = 'A'
	inner join membercentral.dbo.ams_memberNetworkProfiles mnp
		on mnp.profileID = np.profileID
		and mnp.[status] = 'A'
	inner join membercentral.dbo.ams_memberEmails as me 
		on me.memberid = mnp.memberid
		and me.email <> ''
		union 
	--add TLASITES assoc addresses that are linked to paid trialsmith accounts
	select distinct rtrim(ltrim(o.email))
	from dbo.depomemberdata m
	inner join dbo.orgmemberdata o 
		on m.depomemberdataid = o.depomemberdataid
		and m.membertype in (2,3,4,5,6,8) 
		and m.renewaldate > dateadd(year,-1,getdate())
		and mailcode = 'A'
		and o.email like '%@%'
	--add email addresses where all associated accounts are TSAllowed = 0 to the email exclusion list
		union
	select rtrim(ltrim(email))
	from dbo.depomemberdata 
	where mailcode = 'A'
	and email like '%@%'
	group by email
	having min(cast(tsallowed as int)) = 0
	and max(cast(tsallowed as int)) = 0;

	insert into #sitePlatforms (orgcode, isLiveOnNewPlatform, isPortalLaunched, siteID, orgID, siteResourceID)
	select state, isLiveOnNewPlatform, isPortalLaunched, s.siteID, o.orgID, s.siteResourceID
	from trialsmith.dbo.depotla tla
	left outer join membercentral.dbo.organizations o
		inner join membercentral.dbo.sites s on s.orgID = o.orgID
	on o.orgcode = tla.[state]
	where includeInTSEmailMarketing = 1;

	-- new platform
	insert into #allowedGroupPrints (groupPrintID)
	select distinct gprp.groupPrintID
	from #sitePlatforms as tla
	inner join membercentral.dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteResourceID = tla.siteResourceID
		and srfrp.functionID = @functionID
	inner join membercentral.dbo.cache_perms_groupPrintsRightPrints gprp on gprp.rightPrintID = srfrp.rightPrintID;

	insert into #uniqueEmails_MC (email, memberID, depomemberdataid)
	select rtrim(ltrim(me.email)), min(m.memberid) as memberid, min(np.depomemberdataid)
	from #allowedGroupPrints gp
	inner join membercentral.dbo.ams_members as m on gp.groupPrintID = m.groupPrintID
		and m.memberid = m.activeMemberID
		and m.status = 'A'
	inner join membercentral.dbo.ams_memberEmails as me on me.memberid = m.memberid
		and me.email <> ''
	inner join membercentral.dbo.ams_memberEmailTags as metag on metag.memberID = me.memberID 
		and metag.emailTypeID = me.emailTypeID
	inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.emailTagTypeID = metag.emailTagTypeID 
		and metagt.emailTagType = 'Primary'
	left outer join membercentral.dbo.ams_memberNetworkProfiles mnp
		inner join membercentral.dbo.ams_networkProfiles np on np.profileID = mnp.profileID
			and np.[status]='A'
		inner join trialsmith.dbo.depomemberdata dm on dm.depomemberdataID = np.depomemberdataid
			and dm.mailcode = 'A'
			and dm.TSAllowed=1
		on mnp.memberID = m.memberID
		and mnp.[status] = 'A'
	left outer join #tblEmail as paidTSEmails on me.email = paidTSEmails.email
	where paidTSEmails.email is null
	group by me.email;

	delete from #uniqueEmails_MC
	where len(email)-charindex('@',email) <= 0;

	BEGIN TRAN;
		-- trialsmith list
		insert into transfer.dbo.trialsmithMarketingListPopulation ([DateJoined_],[domain_],[emailaddr_],[fullname_],[list_],[usernameLc_],[externalMemberID],[association_],depomemberdataid)
		select m.earliestDateCreated, domain = right(distinctEmails.email,len(distinctEmails.email)-charindex('@',distinctEmails.email)),
			distinctEmails.email, m.firstname + ' ' + m.lastname as fullname_, 'trialsmith', 
			usernameLc_ = left(distinctEmails.email,charindex('@',distinctEmails.email)-1), m.memberNumber as ExternalMemberID,
			o.orgcode, distinctEmails.depomemberdataid
		from membercentral.dbo.ams_members as m
		inner join membercentral.dbo.organizations as o on o.orgID = m.orgID
		inner join #uniqueEmails_MC as distinctEmails on distinctEmails.memberid = m.memberid;

		-- trialsmith_subscribers list
		insert into transfer.dbo.trialsmithMarketingListPopulation ([DateJoined_],[domain_],[emailaddr_],[fullname_],[list_],[usernameLc_],[externalMemberID],[association_],depomemberdataid)
		select m.memberEnrollDate,
			domain = right(paidTsAccounts.email,len(paidTsAccounts.email)-charindex('@',paidTsAccounts.email)),
			paidTsAccounts.email,
			m.firstname + ' ' + m.lastname as fullname_,
			'trialsmith_subscribers',
			usernameLc_ = left(paidTsAccounts.email,charindex('@',paidTsAccounts.email)-1),
			'TSDEPOID_' + CAST(m.depomemberdataID as varchar(10)) as ExternalMemberID,
			m.tlaMemberState,
			m.depomemberdataid
		from dbo.depomemberdata m 
		inner join (
			select email, min(depomemberdataID) as depomemberdataID 
			from (
				select rtrim(ltrim(email)) as email, depomemberdataID
				from dbo.depomemberdata m
				where rtrim(ltrim(email)) like '%@%' and len(email)-charindex('@',email) > 0
					and membertype in (2,3,4,5,6,8) 
					and renewaldate > dateadd(year,-1,getdate()) 
					and mailcode = 'A'
					and tsallowed = 1
					and tlamemberstate <> 'cp'
				union
				select rtrim(ltrim(me.email)) as email, m.depomemberdataID 
				from dbo.depomemberdata m
				inner join membercentral.dbo.ams_networkProfiles np on np.depomemberdataid = m.depomemberdataid
					and np.[status]='A'
					and m.membertype in (2,3,4,5,6,8) 
					and m.renewaldate > dateadd(year,-1,getdate()) 
					and m.mailcode = 'A'
					and m.tsallowed = 1
					and m.tlamemberstate <> 'cp'
				inner join membercentral.dbo.ams_memberNetworkProfiles mnp on mnp.profileID = np.profileID
					and mnp.[status] = 'A'
				inner join membercentral.dbo.ams_memberEmails as me on me.memberid = mnp.memberid
					and me.email <> ''
				inner join membercentral.dbo.ams_memberEmailTags as metag on metag.memberID = me.memberID 
					and metag.emailTypeID = me.emailTypeID
				inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.emailTagTypeID = metag.emailTagTypeID 
					and metagt.emailTagType = 'Primary'
			) tmp
			group by email
		) as paidTsAccounts on m.depomemberdataid = paidTsAccounts.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- 	TrialSmith Expiration date and current/last plan name
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.SubscriberType_ = t.membertype,
			tmlp.dateTrialsmithExpires = m.RenewalDate
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join trialsmith.dbo.depomemberdata m on m.depomemberdataid = tmlp.depomemberdataid
		inner join trialsmith.dbo.membertype t on t.membertypeID = m.membertype;

		--------------------------------------------------------------------------------------
		-- Last Date User Had NET Deposition Purchase ? Not Enough purchase credits
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.dateLastPurchasedDepo = temp.dateLastDepoPurchased
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select depomemberdataid, max(datepurchased) as dateLastDepoPurchased
			from (
				select depomemberdataid, transgroup, max(datepurchased) as datepurchased
				from trialsmith.dbo.depotransactions t
				where accountcode = '3000'
				group by depomemberdataid, transgroup
				having max(reversable) = 'Y' and sum(amountbilled+salesTaxAmount) > 0
			) netTransGroups
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- last date contributed depos
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.dateLastContributedDepo = temp.dateLastContributedDepo
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select depomemberdataid, max(dateentered) as dateLastContributedDepo
			from trialsmith.dbo.depodocuments
			where depomemberdataid is not null and documentTypeID = 1
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- last date searched depos
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.dateLastSearchedDepo = temp.dateLastSearchedDepo
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select depomemberdataid, max(dateentered) as dateLastSearchedDepo
			from search.dbo.tblSearchHistory sh
			inner join search.dbo.tblSearchBuckets b (nolock) on b.bucketId = sh.bucketIDorigin
			inner join search.dbo.tblSearchBucketTypes bt (nolock) on b.bucketTypeID = bt.bucketTypeID
				and bt.bucketType = 'depositions'
			where depomemberdataid is not null
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- depos contributed last twelve months
		--------------------------------------------------------------------------------------
		declare @1yearago datetime = dateadd(year,-1,getdate());

		update tmlp 
		set tmlp.numDeposContributedInLastYear = temp.numDeposContributedInLastYear
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select depomemberdataid, count(*) as numDeposContributedInLastYear
			from trialsmith.dbo.depodocuments
			where depomemberdataid is not null and documentTypeID = 1
			and dateEntered > @1yearago
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- last date searched listservers
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.dateLastSearchedLists = temp.dateLastSearchedLists
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select depomemberdataid, max(dateentered) as dateLastSearchedLists
			from search.dbo.tblSearchHistory sh
			inner join search.dbo.tblSearchBuckets b (nolock) on b.bucketId = sh.bucketIDorigin
			inner join search.dbo.tblSearchBucketTypes bt (nolock) on b.bucketTypeID = bt.bucketTypeID
				and bt.bucketType = 'listservers'
			where depomemberdataid is not null
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- 	Purchase Credits Available (integer -- round down)
		--------------------------------------------------------------------------------------
		update tmlp 
		set tmlp.numPurchaseCreditsAvailable = temp.numPurchaseCreditsAvailable
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			-- report firm balance for all members of firm plan
			SELECT firmMember.depoMemberDataID, cast(isnull(SUM(pc.PurchaseCreditAmount),0) as integer) AS numPurchaseCreditsAvailable
			FROM dbo.tlaFirmPlanLink AS firmMember 
			INNER JOIN dbo.tlaFirmPlanLink AS entireFirm ON firmMember.firmPlanID = entireFirm.firmPlanID 
			inner JOIN dbo.PurchaseCredits AS pc ON entireFirm.depoMemberDataID = pc.depoMemberDataID
			GROUP BY firmMember.depoMemberDataID
			union
			-- report individual account balance for all non-firm plan account
			select depomemberdataid, cast(isnull(sum(purchaseCreditAmount),0) as integer) as numPurchaseCreditsAvailable
			from trialsmith.dbo.PurchaseCredits
			where depomemberdataid not in (select depomemberdataid from dbo.tlaFirmPlanLink)
			group by depomemberdataid
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;

		--------------------------------------------------------------------------------------
		-- depo searches with no matches
		--------------------------------------------------------------------------------------
		declare @3monthsago datetime = dateadd(month,-3,getdate());
		declare @searches table (searchID int Primary KEY, bucketIDorigin int, depomemberdataid int, firstname varchar(100), lastname varchar(100), recency int);

		insert into @searches (searchID, bucketIDorigin, depomemberdataid)
		select searchID, bucketIDorigin, depomemberdataid
		from search.dbo.tblSearchHistory sh
		inner join search.dbo.tblSearchBuckets b (nolock) on b.bucketId = sh.bucketIDorigin
		inner join search.dbo.tblSearchBucketTypes bt (nolock) on b.bucketTypeID = bt.bucketTypeID
			and bt.bucketType = 'depositions'
		where dateEntered > @3monthsago and depomemberdataid is not null
		order by searchID;

		delete rs
		from @searches rs
		inner join search.dbo.tblSearchBucketCounts bc (nolock) on bc.searchID = rs.searchID
			and bc.bucketID = rs.bucketIDorigin
			and bc.itemcount > 0;

		update rs 
		set rs.firstname = isnull(C.cond.value('s_fname','varchar(100)'),''), 
			rs.lastname = isnull(C.cond.value('s_lname','varchar(100)'),'')
		from @searches rs
		inner join search.dbo.tblSearchHistory sh on rs.searchID = sh.searchID
		CROSS APPLY sh.searchXML.nodes('//search') as C(cond);

		-- only accept searches with both firstname and lastname
		delete from @searches where firstname = '' or lastname = '';

		-- make sure first letter of firstname, lastname is capitalized
		update @searches 
		set firstname = upper(left(firstname,1)) + right(firstname,len(firstname)-1),
			lastname = upper(left(lastname,1)) + right(lastname,len(lastname)-1);

		--remove duplicates, keeping most recent
		delete rs2
		from @searches rs2
		inner join (
			select depomemberdataid, firstname, lastname, max(searchID) as maxSearchID
			from @searches rs
			group by depomemberdataid, firstname, lastname
		) keepers
			on keepers.depomemberdataid=rs2.depomemberdataid
			and keepers.firstname=rs2.firstname
			and keepers.lastname=rs2.lastname
			and rs2.searchID <> maxSearchID;

		update rs2
		set rs2.recency = tmp.recency
		from @searches rs2
		inner join (
			select searchID, recency = row_number() over(partition by depomemberdataid order by searchID desc)
			from @searches rs
		) tmp on tmp.searchID = rs2.searchID;

		update tmlp 
		set tmlp.last10FailedDepoSearchesPast3Months = temp.last10FailedDepoSearchesPast3Months,
			tmlp.numFailedDepoSearchPast3Months = temp.numFailedDepoSearchPast3Months
		from transfer.dbo.trialsmithMarketingListPopulation tmlp
		inner join (
			select rs.depomemberdataid, numFailedDepoSearchPast3Months=count(*), last10FailedDepoSearchesPast3Months = replace(recentNames.namelist,'|',', ')
			from @searches rs
			inner join (
				select recent.depomemberdataid, namelist = seminarweb.dbo.pipelist(ltrim(recent.firstname + ' ' + recent.lastname))
				from @searches recent
				where recent.recency <=10
				group by recent.depomemberdataid
			) as recentNames on recentNames.depomemberdataid = rs.depomemberdataid
			group by rs.depomemberdataid, recentNames.namelist
		) as temp on temp.depomemberdataid = tmlp.depomemberdataid;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tblEmail') IS NOT NULL
		DROP TABLE #tblEmail
	IF OBJECT_ID('tempdb..#allowedGroupPrints') IS NOT NULL
		DROP TABLE #allowedGroupPrints
	IF OBJECT_ID('tempdb..#sitePlatforms') IS NOT NULL
		DROP TABLE #sitePlatforms
	IF OBJECT_ID('tempdb..#uniqueEmails_MC') IS NOT NULL
		DROP TABLE #uniqueEmails_MC
	IF OBJECT_ID('tempdb..#uniqueEmails_TLASITES') IS NOT NULL
		DROP TABLE #uniqueEmails_TLASITES

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

EXEC lyris.lyrisCustom.dbo.trialsmith_updateMarketingLists;
GO
