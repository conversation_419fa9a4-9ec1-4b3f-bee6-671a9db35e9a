use trialslyris1;
GO

ALTER TABLE trialslyris1.dbo.members_ ADD MCEmailKey varchar(75) NULL;
GO

update trialslyris1.dbo.members_ 
set mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernamelc_ + '@' + domain_),2)
where list_ in ('trialsmith','trialsmith_subscribers');
GO

use lyrisCustom;
GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	CREATE TABLE #memberPool (poolid int identity(1,1), DateJoined_ datetime, domain_ varchar(250), emailaddr_ varchar(100),
		fullname_ varchar(100), list_ varchar(60), usernameLc_ varchar(100), ExternalMemberID varchar(100),
		association_ varchar(10), depomemberdataid int);
	CREATE TABLE #updatedMembers (id int identity(1,1), poolid int, memberID_ int);
	CREATE TABLE #membershipsToDelete (id int identity(1,1), memberid_ int);
	CREATE TABLE #unsubs (id int identity(1,1), emailaddr_ varchar(100));

	DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100), @currentListName varchar(100), @tier varchar(20), 
		@errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10);
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	SET @crlf = char(13) + char(10);
	SET @tier = 'PRODUCTION';
	SET @smtpserver = '***********';
	IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
		SET @tier = 'DEVELOPMENT';
		SET @smtpserver = 'mail.trialsmith.com';
	END
	IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
		SET @tier = 'BETA';
		SET @smtpserver = 'mail.trialsmith.com';
	END

	exec membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

	if not exists (select emailaddr_ from membercentral.transfer.dbo.trialsmithMarketingListPopulation) BEGIN
		SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists';
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
		SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;

		EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
	END ELSE BEGIN
		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

		insert into #unsubs (emailaddr_)
		select emailaddr_
		from trialslyris1.dbo.members_ m 
		where list_ in ('trialsmith','trialsmith_subscribers') 
		and m.membertype_ = 'unsub';

		insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
		select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
		from membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
		left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
		where u.emailaddr_ is null;

		-- delete subscribed members with email addresses that are no longer in the pool
		insert into #membershipsToDelete (memberID_)
		select m.memberID_
		from trialslyris1.dbo.members_ m
		left outer join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
			and m.list_ = pool.list_ collate Latin1_General_CI_AI
		where m.list_ in ('trialsmith','trialsmith_subscribers') 
		and pool.poolid is null 
		and m.membertype_ <> 'unsub';

		delete m
		from trialslyris1.dbo.members_ m
		inner join #membershipsToDelete md on m.memberid_ = md.memberid_;

		-- update
		insert into #updatedMembers (poolid, memberid_)
		select pool.poolid, m.memberID_
		from trialslyris1.dbo.members_ m WITH(NOLOCK)
		inner join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
			and m.list_ = pool.list_ collate Latin1_General_CI_AI
			and (
					m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
					or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
					or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
					or m.association_ <> pool.association_ collate Latin1_General_CI_AI
					or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
			)
			and m.list_ in ('trialsmith','trialsmith_subscribers')
			and m.membertype_ <> 'unsub';

		update m 
		set DateJoined_ = pool.DateJoined_,
			fullname_= pool.fullname_,
			list_= pool.list_,
			ExternalMemberID = pool.ExternalMemberID,
			association_ = pool.association_,
			depomemberdataid = pool.depomemberdataid
		from trialslyris1.dbo.members_ m WITH(NOLOCK)
		inner join #updatedMembers updated on m.memberid_ = updated.memberid_
		inner join #memberPool pool on updated.poolid = pool.poolid
		where m.list_ in ('trialsmith','trialsmith_subscribers') 
		and m.membertype_ <> 'unsub';

		-- delete all preexisting memberships from pool, leaving only entries that need to be created
		delete pool
		from #memberPool pool
		inner join trialslyris1.dbo.members_ m WITH(NOLOCK) on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
			and m.list_ = pool.list_ collate Latin1_General_CI_AI
		where m.list_ in ('trialsmith','trialsmith_subscribers');

		-- insert new memberships
		insert into trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ ,
			ExternalMemberID, association_, depomemberdataid, mcemailkey)
		select DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ , ExternalMemberID, association_,
			depomemberdataid, mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernamelc_ + '@' + domain_),2)
		from #memberPool;

		-- update trialsmithUsage
		truncate table trialslyris1.dbo.tsdata
		insert into trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, 
			expList, numBadSrc, subType, expires)
		select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, 
			numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, 
			last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
		from membercentral.transfer.dbo.trialsmithMarketingListPopulation
		where depomemberdataid is not null;

		exec dbo.trialsmith_syncListUnsubs 'trialsmith', 'trialsmith_subscribers';
	END

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
