use trialslyris1
GO

CREATE PROC mc_changeExternalMemberID
@orgcode varchar(10),
@oldMemberNumber varchar(50),
@newMemberNumber varchar(50)

AS

declare @tblMemberID table (memberID_ int NOT NULL);

UPDATE m
SET m.externalMemberID = @newMemberNumber
OUTPUT inserted.memberid_ INTO @tblMemberID
FROM dbo.members_ as m
INNER JOIN dbo.lists_format as lf on m.list_ = lf.name
WHERE lf.orgcode = @orgcode
AND m.externalMemberID = @oldMemberNumber;

SELECT memberID_ 
FROM @tblMemberID;

RETURN 0
GO
