update m set externalMemberID = '4' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '374' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '585' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '637' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '637' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '637' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '660' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '680' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '697' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '806' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '899' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1063' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1100' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1108' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1116' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = 'cmachunterpa@<EMAIL>'
GO
update m set externalMemberID = '1132' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1134' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1134' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1193' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1264' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1304' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1319' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1380' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1387' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1387' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1493' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1575' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1692' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1846' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1862' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '1971' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2025' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2166' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2216' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2312' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2461' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2519' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2602' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2689' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2785' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2789' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2802' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '2925' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3006' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3070' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3105' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3161' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3189' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3192' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3194' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3277' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3280' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3382' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3435' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3443' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3443' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3567' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3771' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3787' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3808' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '3915' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4176' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4453' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4496' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4513' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4557' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4570' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4570' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4571' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4574' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4756' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '4897' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5006' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5133' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5200' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5210' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5239' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5458' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5458' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5476' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5499' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5512' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5768' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5810' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '5912' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6030' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6030' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6045' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6073' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6084' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6112' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6277' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6305' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6433' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6512' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6592' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6718' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6722' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6745' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6763' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6763' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6940' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '6993' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7082' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7094' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7171' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7402' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7434' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7441' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7536' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7558' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7570' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7586' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7596' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7658' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7683' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7736' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7742' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7794' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7825' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7928' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7932' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7941' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '7990' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '8003' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9029' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9074' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9153' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9198' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9198' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9198' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9280' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9370' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9379' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9430' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9493' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9508' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9553' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9590' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9602' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9607' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9612' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9648' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9662' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9662' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9720' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9748' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9773' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9803' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9907' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9917' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9932' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9954' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9957' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9995' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '9995' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10002' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10023' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10030' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10036' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10072' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10075' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10076' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10082' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10096' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10175' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10236' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10299' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10337' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10371' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10372' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10416' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10434' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10463' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10566' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10598' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10654' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10654' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10654' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10655' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10700' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10703' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10717' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10718' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10720' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10726' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10758' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10767' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10800' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10800' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10823' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10876' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '10966' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '11015' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '11035' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '11044' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '11079' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '11098' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>.'
GO
update m set externalMemberID = '12070' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12080' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12129' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12228' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12228' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12243' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12265' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12283' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12285' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12333' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12371' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12404' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12463' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12518' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12554' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12587' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12615' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12653' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12720' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12851' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12874' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12879' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12889' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12910' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '12953' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13017' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13051' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13078' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13090' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13151' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13269' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13327' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13418' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13455' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13614' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13726' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13774' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13864' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13871' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13887' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13913' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13913' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13924' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13952' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '13971' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14034' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14054' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14079' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14120' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14141' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14156' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14163' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14166' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14185' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14233' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14278' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14315' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14320' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14334' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14347' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14352' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14358' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14359' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14402' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14404' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14436' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14460' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14496' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14502' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14536' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '14603' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '15246' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '15781' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '16691' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '16992' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17083' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17093' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17095' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17095' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17119' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17150' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17150' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17172' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17200' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17201' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17253' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17294' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17358' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17366' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17376' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17387' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17428' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17448' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17449' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17463' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17470' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17501' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17531' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17532' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17535' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17579' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17600' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17615' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17669' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17710' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17720' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '17745' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18042' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18059' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18130' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18190' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18365' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18791' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18803' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18873' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18877' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18879' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '18890' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19059' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19073' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19078' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19292' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19301' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19335' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19441' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19477' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19485' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19496' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19503' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19512' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19519' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19544' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19574' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19590' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19593' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19605' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19606' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19682' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19708' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19716' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19716' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19731' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19731' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19731' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19750' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19752' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19755' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19765' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19787' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19789' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19903' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19906' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19907' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19935' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19950' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19960' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '19970' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20079' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20129' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20204' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20211' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20217' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20227' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20229' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20243' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20249' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20283' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20312' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20318' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20319' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20320' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20356' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20374' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20374' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20374' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20390' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20397' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20399' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20415' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20433' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20446' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20449' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20458' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20521' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20678' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20724' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20724' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '20957' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21126' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21126' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21126' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>.'
GO
update m set externalMemberID = '21131' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21391' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21391' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21395' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21691' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21763' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21780' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21810' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21833' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21849' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21901' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21928' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21936' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21939' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '21939' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22034' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22116' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22186' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22201' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22293' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22450' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22454' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22611' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22630' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22630' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22638' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22688' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22729' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22729' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22807' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22820' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22857' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22873' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '22874' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23258' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23265' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23275' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23308' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23339' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23405' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23435' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23449' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23453' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23492' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23512' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23524' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23528' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23571' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23575' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23620' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23631' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23663' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>(gabetalton)'
GO
update m set externalMemberID = '23663' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>(gabetalton)'
GO
update m set externalMemberID = '23663' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '23755' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24001' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24012' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24038' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24042' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24063' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24086' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24175' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24228' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24249' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24250' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24267' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24303' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24303' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24318' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24345' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24490' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24493' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24521' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24645' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24647' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24660' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24692' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24788' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24812' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24838' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24846' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24933' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24955' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24955' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24955' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '24956' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25064' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25070' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25082' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25105' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25114' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25114' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25132' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25268' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25279' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25309' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25337' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25341' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25364' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25368' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25394' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25544' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25545' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25658' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25664' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25698' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25727' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25733' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25745' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25750' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25751' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25759' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25797' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25814' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25831' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25884' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '25910' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26006' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26032' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26049' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26052' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26124' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26131' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26163' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26178' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26182' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26221' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26269' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26309' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26355' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26369' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26381' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26404' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26431' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26455' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26575' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26586' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26587' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26635' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26651' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26688' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26701' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26730' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26756' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26769' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26815' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26830' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26852' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26873' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26873' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26888' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26892' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26903' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26903' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26939' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26998' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '26998' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27023' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27061' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27091' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27091' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27107' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27125' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27131' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27175' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27183' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27189' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27189' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27197' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27248' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27278' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27283' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27283' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27386' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27446' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27456' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27459' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27488' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27491' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27492' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27492' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27529' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27549' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27587' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27635' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27647' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27655' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27700' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27728' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27754' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27754' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27766' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27790' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27793' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27869' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27924' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27925' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27926' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27932' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27933' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27943' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '27999' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28017' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28017' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28035' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28051' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28052' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28066' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28100' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28109' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28110' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28121' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28128' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28157' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28161' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28172' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28176' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28179' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28181' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28215' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28242' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28259' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28278' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28278' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28376' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28424' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28485' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28513' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28529' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28533' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28546' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28551' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28569' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28633' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28661' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28720' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28771' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28774' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28800' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28804' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28819' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28823' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28828' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28828' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28834' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28866' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28886' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28943' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '28979' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29062' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29074' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29085' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29093' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29094' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29117' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29130' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29150' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29151' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29155' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29157' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29160' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29167' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29168' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29173' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29181' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29183' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29211' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29273' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29302' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29303' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29328' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29331' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29390' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29391' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29408' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = 'mkmoore#@hotmail.com'
GO
update m set externalMemberID = '29446' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29446' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29489' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29602' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29701' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29750' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29795' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29804' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '29933' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30018' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30025' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30144' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30159' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30327' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '30412' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '130008' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
update m set externalMemberID = '133844' from lists_format lf inner join members_ m on m.list_ = lf.name and orgcode = 'nc' and emailaddr_ = '<EMAIL>'
GO
