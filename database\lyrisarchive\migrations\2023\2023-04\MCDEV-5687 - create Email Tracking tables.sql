USE EmailTracking;
GO

-- sendingApplications
CREATE TABLE sendingApplications (
    sendingApplicationID INT IDENTITY(1,1) PRIMARY KEY,
    applicationType VARCHAR(20),
    applicationName VARCHAR(100)
);
GO

-- MessageStats
CREATE TABLE messageStats (
    sendingApplicationID INT FOREIGN KEY REFERENCES sendingApplications(sendingApplicationID),
    sendingApplicationMessageID INT,
    siteID INT,
    ipPoolID INT,
    sendgrid_subuserID INT,
    sendgrid_subuserDomainID INT,
    periodCode INT,
    uniqueOpens INT DEFAULT(0),
    uniqueClicks INT DEFAULT(0),
    totalOpens INT DEFAULT(0),
    totalClicks INT DEFAULT(0),
    totalSpamReports INT DEFAULT(0),
    PRIMARY KEY (sendingApplicationID, sendingApplicationMessageID)
);
GO

-- RecipientDomains
CREATE TABLE recipientDomains (
    domainID INT IDENTITY(1,1) PRIMARY KEY,
    domain VARCHAR(250)
);
GO

-- Recipients
CREATE TABLE recipients (
    sg_message_id VARCHAR(100) <PERSON>IMAR<PERSON>,
    memberNumber VARCHAR(50),
    sendingApplicationID INT,
    sendingApplicationMessageID INT,
    siteID INT,
    username VARCHAR(100),
    domainID INT,
    dateFirstAttempted SMALLDATETIME,
    dateLastAttempted SMALLDATETIME,
    DeliveryStatusID TINYINT,
    deliveryAttempts TINYINT,
    ErrormessageID INT,
    firstOpened SMALLDATETIME,
    firstClicked SMALLDATETIME,
    firstReportedSpam SMALLDATETIME
);
GO

-- RecipientOpens
CREATE TABLE recipientOpens (
    sg_event_id VARCHAR(100) PRIMARY KEY,
    sg_message_id VARCHAR(100),
    memberNumber VARCHAR(50),
    sendingApplicationID INT,
    sendingApplicationMessageID INT,
    siteID INT,
    username VARCHAR(100),
    domainID INT,
    periodCode INT,
    dateReceived SMALLDATETIME,
    UseragentID INT,
    ipaddress VARCHAR(50),
    isMachineOpen BIT,
    hasBeenAggregated BIT
);
GO

-- RecipientClicks
CREATE TABLE recipientClicks (
    sg_event_id VARCHAR(100) PRIMARY KEY,
    sg_message_id VARCHAR(100),
    memberNumber VARCHAR(50),
    sendingApplicationID INT,
    sendingApplicationMessageID INT,
    siteID INT,
    username VARCHAR(100),
    domainID INT,
    periodCode INT,
    dateReceived SMALLDATETIME,
    UseragentID INT,
    ipaddress VARCHAR(50),
    clickDomain VARCHAR(500),
    clickUrl VARCHAR(1000),
    hasBeenAggregated BIT
);
GO

-- recipientDeliveryMessages
CREATE TABLE recipientDeliveryMessages (
    sg_event_id VARCHAR(100) PRIMARY KEY,
    sg_message_id VARCHAR(100),
    memberNumber VARCHAR(50),
    sendingApplicationID INT,
    sendingApplicationMessageID INT,
    siteID INT,
    username VARCHAR(100),
    domainID int,
    periodCode INT,
    dateReceived SMALLDATETIME,
    messageStatusID INT,
    smtpStatusCode VARCHAR(25),
    MessageTemplateSHA1 VARCHAR(40),
    hasBeenAggregated BIT
);
GO

-- deliveryMessageTemplates
CREATE TABLE deliveryMessageTemplates (
    messageTemplateSHA1 VARCHAR(40) PRIMARY KEY,
    dateCreated DATETIME,
    statusMessageTemplate VARCHAR(1000)
);
GO

-- userAgents
CREATE TABLE userAgents (
    userAgentID INT IDENTITY(1,1) PRIMARY KEY,
    useragentSHA1 VARCHAR(40),
    dateCreated DATETIME,
    useragentString VARCHAR(500),
    device_os VARCHAR(100),
    browser_name VARCHAR(100),
    browser_version VARCHAR(100),
    is_spam BIT,
    is_bot BIT,
    is_weird BIT,
    is_restricted BIT,
    is_abusive BIT,
    deviceType VARCHAR(50)
);
GO

-- deliveryStatuses
CREATE TABLE deliveryStatuses (
    deliveryStatusID INT IDENTITY(1,1),
    statusCode VARCHAR(50),
    explanation VARCHAR(255)
);
GO

-- Insert data into deliveryStatuses
INSERT INTO deliveryStatuses (statusCode, explanation)
VALUES
('processed', 'Message has been received and is ready to be delivered'),
('dropped', 'Message has been received, but sendgrid won''t attempt'),
('deferred', 'Receiving server temporarily rejected the message.'),
('delivered', 'Message has been successfully delivered to the receiving server.'),
('bounced-invalid', 'permanent rejection due to typo or email address no longer existing. Email added to Suppression List'),
('bounced-mailboxUnavailable', 'permanent rejection due to problem with an individual recipient''s mailbox. Email added to Suppression List'),
('bounced-technical', 'permanent rejection due to technical problem with the sender or with the mailbox provider. Email added to Suppression List'),
('bounced-unclassified', 'permanent rejection due to some unclassified reason. Email added to Suppression List'),
('blocked-invalid', 'temporary rejection due to typo or email address no longer exists'),
('blocked-technical', 'temporary rejection due to technical problem with the sender or with the mailbox provider'),
('blocked-content', 'temporary rejection due to recipient server content filters'),
('blocked-reputation', 'temporary rejection due to reputation issues with your sending domain or sending IP'),
('blocked-frequencyVolume', 'temporary rejection due to the number of messages you are trying to send'),
('blocked-mailboxUnavailable', 'temporary rejection due to problem with recipient''s mailbox'),
('blocked-unclassified', 'temporary rejection for some unclassified reason');

GO

-- MessageStats index
CREATE INDEX idx_MessageStats_siteID_sendingAppID_sendingAppMessageID
ON MessageStats (siteID, sendingApplicationID, sendingApplicationMessageID);

-- Recipients indexes
CREATE INDEX idx_Recipients_siteID_sendingAppID_sendingAppMessageID
ON Recipients (siteID, sendingApplicationID, sendingApplicationMessageID);

CREATE INDEX idx_Recipients_siteID_domainID_username
ON Recipients (siteID, domainID, username)
INCLUDE (DeliveryStatusID, firstOpened, firstClicked);

-- RecipientOpens index
CREATE INDEX idx_RecipientOpens_siteID_domainID_username
ON RecipientOpens (siteID, domainID, username)
INCLUDE (dateReceived);

-- RecipientClicks index
CREATE INDEX idx_RecipientClicks_siteID_domainID_username
ON RecipientClicks (siteID, domainID, username)
INCLUDE (dateReceived, clickUrl);

-- recipientDeliveryMessages index
CREATE INDEX idx_recipientDeliveryMessages_siteID_domainID_username
ON recipientDeliveryMessages (siteID, domainID, username)
INCLUDE (dateReceived, messageStatusID, MessageTemplateSHA1);

GO