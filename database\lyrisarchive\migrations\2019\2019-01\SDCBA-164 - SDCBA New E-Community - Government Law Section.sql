USE memberCentral
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sitecode varchar(10), @fieldsetName_search varchar (200), @fieldsetName_results varchar (200), 
		@fieldsetName_details varchar (200), @GLAccountCode varchar(30), @communitiesSectionCode varchar(100), @deletePreExistingPages bit;
	DECLARE @commList TABLE (autoID int IDENTITY(1,1), pageName varchar(100), pageTitle varchar(200));

	/* ************************************ */
	/* THIS SECTION SHOULD COME FROM THE PM */
	/* ************************************ */
	SET @sitecode = 'SDCBA';
	SET @fieldsetName_search = 'Directory Search Form';
	SET @fieldsetName_results = 'Member Directory Results';
	SET @fieldsetName_details = 'Member Directory Results';
	SET @GLAccountCode = '7000000';
	SET @communitiesSectionCode = 'SectionPages';

	SET @deletePreExistingPages = 1

	INSERT INTO @commlist (pageName, pageTitle) VALUES ('SGOVERComm', 'Government Law');

	/* ************************************************ */
	/* THIS SECTION SHOULD BE STANDARD FOR ALL PURPOSES */
	/* ************************************************ */
	DECLARE	@siteid int, @orgID int, @defaultGLAccountID int, @searchFieldSetID int, @resultsFieldSetID int,
		@detailsFieldSetID int, @TopSectionID int, @languageID int, @zoneID int, @zoneIDB int, @pageModeID int, 
		@subpageTemplateID int, @pgParentResourceTypeID int, @pgResourceTypeID int, @commSiteResourceTypeID int, 
		@MemberManagerResourceTypeID int, @ListViewerResourceTypeID int, @EventsResourceTypeID int, 
		@FileShareResourceTypeID int, @AnnouncementsResourceTypeID int, @VideoGalleryResourceTypeID int, 
		@PhotoGalleryResourceTypeID int, @recentPhotosWidgetTypeID int, @recentVideosWidgetTypeID int, 
		@appearInDirectoryID varchar(255), @participateFunctionID int, @viewFunctionID int,@viewFunctionIDList varchar(255), @fsAddDocumentsFunctionID int,
		@fsEditOwnMetadataFunctionID int, @fsReuploadOwnFunctionID int, @fsDeleteOwnFunctionID int, 
		@fsAddSubFolderFunctionID int, @editOwnFunctionID int, @editOwnFunctionIDList varchar(255), @AddPhotosFunctionID int,  @AddPhotosFunctionIDList varchar(255),
		@deleteOwnFunctionID int, @deleteOwnFunctionIDList varchar(255),
		@AddVideosFunctionID int, @AddVideosFunctionIDList varchar(255), @memberFieldsetFunctionID varchar(255), @thispageName varchar(100), @siteResourceID int, 
		@thispageTitle varchar(200), @thisapplicationInstanceID int, @thissiteResourceID int, @thispageID int,
		@thisRootSectionID int, @commHomePageID int, @subAppPageName varchar(100), @subAppPageTitle varchar(100),
		@subAppApplicationInstanceID int, @subAppResourceID int, @subAppPageID int, @searchUseID int, @pageID int, 
		@resultsUseID int, @resultsUseSiteResourceID int, @trashID int, @detailsUseID int, @applicationInstanceID int, 
		@detailsUseSiteResourceID int, @sourceCalendarID int, @destinationCalendarID int, @MCsystemMemberID int,
		@viewFunction int, @applicationWidgetInstanceID int, @applicationWidgetInstanceSiteResourceID int;

	SELECT @siteID = dbo.fn_getSiteIDFromSiteCode(@siteCode);
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SET @deletePreExistingPages = isnull(@deletePreExistingPages,0)

	-- page names cannot have spaces
	IF EXISTS (select pagename from @commList where pagename like '% %')
	BEGIN
		select pagename 
		from @commList 
		where pagename like '% %'
		order by pagename;

		RAISERROR('Page names cannot have spaces.',16,1);
	END

	-- page names cannot be duplicated
	IF EXISTS (select pageName from @commlist group by pageName having count(*) > 1) 
	BEGIN
		select pageName 
		from @commlist 
		group by pageName 
		having count(*) > 1
		order by pageName;

		RAISERROR('Page names must be unique.',16,1);
	END
	

	IF @deletePreExistingPages = 0
	BEGIN
		-- page names cannot already be on site
		IF EXISTS (
			select cl.pagename
			from @commlist as cl
			inner join dbo.cms_pages as p on cl.pagename = p.pagename
			inner join dbo.cms_siteResources sr on sr.siteResourceID = p.siteResourceID
			inner join dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc in ('Active','Inactive')
			where p.siteID = @siteID) 
		BEGIN
			select cl.pagename
			from @commlist as cl
			inner join dbo.cms_pages as p on cl.pagename = p.pagename
			inner join dbo.cms_siteResources sr on sr.siteResourceID = p.siteResourceID
			inner join dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc in ('Active','Inactive')
			where p.siteID = @siteID
			ORDER BY cl.pagename;

			RAISERROR('Page names must not already exist on the site. SET @deletePreExistingPages=1 to override and autodelete preexisting pages',16,1);
		END
	END ELSE BEGIN

		declare 
			@siteResourceIDToDelete int,
			@totalCountToDelete int,
			@numberDeleted int,
			@deletionProgessMessage varchar(100)

		declare @siteResourcesToDelete TABLE (siteResourceID int PRIMARY KEY)
		-- write your query to populate the @siteResourcesToDelete table

		insert into @siteResourcesToDelete (siteResourceID)
		select p.siteResourceID
		from @commlist c
		inner join cms_pages p
			on c.pagename = p.pagename
		inner join sites s
			on s.siteID = p.siteID
			and s.sitecode = @sitecode
		inner join dbo.cms_siteResources sr
			on sr.siteResourceID = p.siteResourceID
		inner join dbo.cms_siteResourceStatuses srs 
			on srs.siteResourceStatusID = sr.siteResourceStatusID 
			and srs.siteResourceStatusDesc in ('Active','Inactive')

		-- do not change below this line
		set @numberDeleted = 0
		select @totalCountToDelete = count(*) from @siteResourcesToDelete

		select @siteResourceIDToDelete = min(siteResourceID) from @siteResourcesToDelete
		while @siteResourceIDToDelete is not null
		begin
			exec dbo.cms_deleteSiteResourceAndChildren @siteResourceID=@siteResourceIDToDelete
			set @numberDeleted = @numberDeleted + 1
			set @deletionProgessMessage = 'Deleted ' + convert(varchar(10),@numberDeleted) + ' of ' + convert(varchar(10),@totalCountToDelete) + ' Preexisting Communities'
			RAISERROR(@deletionProgessMessage,0,1)

			select @siteResourceIDToDelete = min(siteResourceID) from @siteResourcesToDelete where siteResourceID > @siteResourceIDToDelete
		end
	END

	-- invalid GL
	select @defaultGLAccountID = GLAccountID FROM dbo.tr_glAccounts WHERE orgID = @orgID AND accountCode = @GLAccountCode AND [status] = 'A';
	IF @defaultGLAccountID is null
		RAISERROR('GL Account Code is not valid.',16,1);

	-- invalid fieldsets
	SELECT @searchFieldSetID = fieldSetID FROM dbo.ams_memberFieldSets WHERE siteID = @siteID AND fieldsetName = @fieldsetName_search;
	IF @searchFieldSetID is null
		RAISERROR('Search Field Set is not valid.',16,1);

	SELECT @resultsFieldSetID = fieldSetID FROM dbo.ams_memberFieldSets WHERE siteID = @siteID AND fieldsetName = @fieldsetName_results;
	IF @resultsFieldSetID is null
		RAISERROR('Results Field Set is not valid.',16,1);

	SELECT @detailsFieldSetID = fieldSetID FROM dbo.ams_memberFieldSets WHERE siteID = @siteID AND fieldsetName = @fieldsetName_details;
	IF @detailsFieldSetID is null
		RAISERROR('Details Field Set is not valid.',16,1);

	-- invalid section code
	SELECT @TopSectionID = sectionID from dbo.cms_pageSections where siteid = @siteID and sectionCode = @communitiesSectionCode;
	IF @TopSectionID is null
		RAISERROR('Community Section Code is not valid.',16,1);

	-- constants
	SELECT @languageID = dbo.fn_getLanguageID('en');
	SELECT @zoneID = dbo.fn_getZoneID('Main');
	SELECT @zoneIDB = dbo.fn_getZoneID('B');
	SELECT @pageModeID = dbo.fn_getModeId('Full');
	SELECT @subpageTemplateID = dbo.fn_getTemplateID(null,'communityThreeColumn');
	SELECT @pgParentResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage');
	SELECT @pgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage');
	SELECT @commSiteResourceTypeID = dbo.fn_getResourceTypeID('Community');
	SELECT @MemberManagerResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Members';
	SELECT @ListViewerResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'ListViewer';
	SELECT @EventsResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Events';
	SELECT @FileShareResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'FileShare2';
	SELECT @AnnouncementsResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Announcements';
	SELECT @VideoGalleryResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'VideoGallery';
	SELECT @PhotoGalleryResourceTypeID = srt.resourceTypeID from dbo.cms_applicationTypes at inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'PhotoGallery';
	SELECT @recentPhotosWidgetTypeID = applicationWidgetTypeID from dbo.cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentPhotos';
	SELECT @recentVideosWidgetTypeID = applicationWidgetTypeID from dbo.cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentVideos';
	SELECT @appearInDirectoryID = cast(dbo.fn_getResourceFunctionID('AppearInDirectory', @MemberManagerResourceTypeID) as varchar(255));
	SELECT @participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @commSiteResourceTypeID);
	SELECT @viewFunctionID = dbo.fn_getResourceFunctionID('View', @commSiteResourceTypeID);
	SELECT @viewFunctionIDList = cast(@viewFunctionID as varchar(255));
	SELECT @fsAddDocumentsFunctionID = dbo.fn_getResourceFunctionID('fsAddDocuments', @FileShareResourceTypeID);
	SELECT @fsEditOwnMetadataFunctionID = dbo.fn_getResourceFunctionID('fsEditOwnMetadata', @FileShareResourceTypeID);
	SELECT @fsReuploadOwnFunctionID = dbo.fn_getResourceFunctionID('fsReuploadOwn', @FileShareResourceTypeID);
	SELECT @fsDeleteOwnFunctionID = dbo.fn_getResourceFunctionID('fsDeleteOwn', @FileShareResourceTypeID);
	SELECT @fsAddSubFolderFunctionID = dbo.fn_getResourceFunctionID('fsAddSubFolder', @FileShareResourceTypeID);
	SELECT @editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', @PhotoGalleryResourceTypeID);
	SELECT @editOwnFunctionIDList = cast(@editOwnFunctionID as varchar(255));
	SELECT @AddPhotosFunctionID = dbo.fn_getResourceFunctionID('AddPhotosByDefault', @PhotoGalleryResourceTypeID);
	SELECT @AddPhotosFunctionIDList = cast(@AddPhotosFunctionID as varchar(255));
	SELECT @deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', @PhotoGalleryResourceTypeID);
	SELECT @deleteOwnFunctionIDList = cast(@deleteOwnFunctionID as varchar(255));
	SELECT @AddVideosFunctionID = dbo.fn_getResourceFunctionID('AddVideos', @VideoGalleryResourceTypeID);
	SELECT @AddVideosFunctionIDList = cast(@AddVideosFunctionID as varchar(255));
	SELECT @memberFieldsetFunctionID = cast(srf.functionID as varchar(255))
		from dbo.ams_memberFieldusage mfu
		inner join dbo.cms_siteResources sr on sr.siteResourceID = mfu.useSiteResourceID
		inner join dbo.cms_siteResourceTypes srt on sr.resourceTypeID = srt.resourceTypeID
		inner join dbo.cms_siteResourceTypeFunctions srtf on srtf.resourceTypeID = srt.resourceTypeID
		inner join dbo.cms_siteResourceFunctions srf on srf.functionID = srtf.functionID;
	SELECT @MCsystemMemberID = memberID from dbo.ams_members where orgID = 1 and memberNumber = 'SYSTEM' and status = 'A';

	
	-- loop over each in the list
	SELECT @thisPageName = min(pageName) from @commList;
	WHILE @thisPageName is not null BEGIN
		BEGIN TRAN;

		print char(13) + char(10) + 'Attempting ' + @thisPageName;

		select @thisApplicationInstanceID = null, @thisSiteResourceID = null, @thisPageID = null,
			@subAppApplicationInstanceID = null, @subAppResourceID = null, @subAppPageID = null;
		SELECT @thisPageTitle = pageTitle from @commlist where pageName = @thisPageName;

		exec dbo.cms_createApplicationInstanceCommunity @siteid=@siteID, @languageID=@languageID, @sectionID=@TopSectionID, 
			@isVisible=1, @pageName=@thispageName, @pageTitle=@thispageTitle, @pagedesc=@thispageTitle, @zoneID=@zoneID,
			@pageTemplateID=null, @subpageTemplateID=@subpageTemplateID, @pageModeID=@pageModeID, 
			@pgResourceTypeID=@pgParentResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
			@applicationInstanceName=@thispageTitle, @applicationInstanceDesc=@thispageTitle, 
			@applicationInstanceID=@thisapplicationInstanceID OUTPUT, @siteResourceID=@thissiteResourceID OUTPUT,
			@pageID=@thispageID OUTPUT;

		SELECT @thisRootSectionID = rootSectionID from dbo.comm_communities where applicationInstanceID = @thisapplicationInstanceID;

		SELECT @commHomePageID = p.pageID
		from dbo.comm_communities comm
		inner join dbo.cms_pages p on p.pageName = comm.defaultCommunityPageName
			and comm.rootSectionID = p.sectionID
			and comm.rootSectionID = @thisRootSectionID
		inner join dbo.cms_siteResources sr on sr.siteResourceID = p.siteResourceID
		inner join dbo.cms_siteResourceStatuses srs on sr.siteResourceStatusID = srs.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active';

		/* ******** */
		/* CALENDAR */
		/* ******** */
		set @subAppPageName = @thispageName + 'Calendar';
		set @subAppPageTitle = 'Calendar';

		EXEC dbo.cms_createApplicationInstanceEvents @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@defaultGLAccountID=@defaultGLAccountID, @applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='',
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, @siteresourceID=@subAppResourceID OUTPUT,
			@pageID=@subAppPageID OUTPUT;

		-- Copy the default categories from the first calendar.
		SELECT @sourceCalendarID = null, @destinationCalendarID = null;

		select top 1 @sourceCalendarID = c.calendarID
		from dbo.ev_calendars c
		inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID
		where c.siteid = @siteid
		order by c.calendarID;

		select @destinationCalendarID=calendarID
		from dbo.ev_calendars c
		inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID
		where ai.applicationInstanceID = @subAppApplicationInstanceID;

		if (@sourceCalendarID is not null and @destinationCalendarID is not null)
			exec dbo.ev_copyMissingCategoriesToCalendar @sourceCalendarID=@sourceCalendarID, @destCalendarID=@destinationCalendarID;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@viewFunctionIDList, 
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;


		/* ********** */
		/* FILESHARE2 */
		/* ********** */
		set @subAppPageName = @thispageName + 'FileShare';
		set @subAppPageTitle = 'File Share';

		EXEC dbo.cms_createApplicationInstanceFileShare2 @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
			@isVisible=1, @pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pagedesc='', @zoneID=@zoneID, @pageTemplateID=NULL,
			@pageModeID=NULL, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', @applicationInstanceID=@applicationInstanceID OUTPUT,
			@siteResourceID=@subAppResourceID OUTPUT, @pageID=@pageID OUTPUT;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@viewFunctionIDList, 
			@roleID=null, @groupID=null, @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@fsAddDocumentsFunctionID,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@fsEditOwnMetadataFunctionID,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@fsReuploadOwnFunctionID,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@fsAddSubFolderFunctionID,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@fsDeleteOwnFunctionID,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;

		/* ***** */
		/* LISTS */
		/* ***** */
		set @subAppPageName = @thispageName + 'ListServer';
		set @subAppPageTitle = 'List Server';

		EXEC dbo.cms_createApplicationInstanceListViewer @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@isVisible=1, @pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', @applicationInstanceID=@subAppApplicationInstanceID OUTPUT,
			@siteresourceID=@subAppResourceID OUTPUT, @pageID=@subAppPageID OUTPUT;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, 
			@functionIDList=@viewFunctionIDList, @roleID=null, @groupID=null, @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;

		/* ************* */
		/* ANNOUNCEMENTS */
		/* ************* */
		set @subAppPageName = @thispageName + 'Announcements';
		set @subAppPageTitle = 'Announcements';

		EXEC dbo.cms_createApplicationInstanceAnnouncements @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@isVisible=1, @pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', @applicationInstanceID=@subAppApplicationInstanceID OUTPUT,
			@siteresourceID=@subAppResourceID OUTPUT, @pageID=@subAppPageID OUTPUT;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@viewFunctionIDList, 
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;

		/* ************** */
		/* MEMBER MANAGER */
		/* ************** */
		set @subAppPageName = @thispageName + 'CommunityDirectory';
		set @subAppPageTitle = 'Community Directory';

		EXEC dbo.cms_createApplicationInstanceMemberDirectory @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@isVisible=1, @pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', @applicationInstanceID=@subAppApplicationInstanceID OUTPUT,
			@siteresourceID=@subAppResourceID OUTPUT, @pageID=@subAppPageID OUTPUT;

		-- set the search fieldset for the member directory
		EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@searchFieldSetID, @area='search',
			@createSiteResourceID=0, @useID=@searchUseID OUTPUT;

		-- set the results fieldset for the member directory
		EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@resultsFieldSetID, @area='results',
			@createSiteResourceID=1, @useID=@resultsUseID OUTPUT;

		-- lookup usesiteResourceID from useID returned from previous step
		SELECT @resultsUseSiteResourceID = mfu.useSiteResourceID
			FROM dbo.ams_memberFieldusage mfu
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = mfu.useSiteResourceID
			WHERE mfu.useID = @resultsUseID;

		-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
		exec dbo.cms_createSiteResourceRight 
				@siteID=@siteid, 
				@siteResourceID=@resultsUseSiteResourceID, 
				@include=1, 
				@functionIDList=@memberFieldsetFunctionID, 
				@roleID=null, 
				@groupID=null,
				@inheritedRightsResourceID=@thisSiteResourceID, 
				@inheritedRightsFunctionID=@participateFunctioniD;

		-- set the details fieldset for the member directory
		EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@detailsFieldSetID, @area='details',
			@createSiteResourceID=1, @useID=@detailsUseID OUTPUT;

		-- lookup usesiteResourceID from useID returned from previous step
		SELECT @detailsUseSiteResourceID = mfu.useSiteResourceID
			FROM dbo.ams_memberFieldusage mfu
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = mfu.useSiteResourceID
			WHERE mfu.useID = @detailsUseID;

		-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@detailsUseSiteResourceID,
			@include=1, @functionIDList=@memberFieldsetFunctionID, @roleID=null, @groupID=null,
			@inheritedRightsResourceID=@thisSiteResourceID, @inheritedRightsFunctionID=@participateFunctioniD;

		-- set the default action per MF; Set's the default view on membermanager.
		update dbo.cms_applicationInstances
		set settingsXML = '<settings><setting name="defaultAction" value="SearchResults" /></settings>'
		where applicationInstanceID = @subAppApplicationInstanceID;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, 
			@functionIDList=@appearInDirectoryID, @roleID=null, @groupID=null, 
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctioniD;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, 
			@functionIDList=@viewFunctionIDList, @roleID=null, @groupID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@viewFunctionID;

		/* ************* */
		/* GUIDELINES	 */
		/* ************* */
        -- declare vars for Help or Guidelines
        DECLARE @AppSubPageTypeID int, @siteResourceStatusID int;
		DECLARE @commpgPageID int, @communityPageID int;
		DECLARE @userCreatedContentResourceTypeID int, @contentID int, @contentSiteResourceID int;

		select @AppSubPageTypeID = dbo.fn_getResourceTypeId('ApplicationSubPage');
		select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active');
		select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent');

        set @subAppPageName = @thispageName + 'Guidelines';
        EXEC dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID,
            @siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=1, @sectionid=@thisRootSectionID,
            @ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='Community Guidelines',
            @pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT;

        EXEC dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID,
            @resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
            @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Community Guidelines',
            @contentDesc='',
            @rawContent='Welcome to this community. Click around and see what''s here.',
            @contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT;

		/* ************* */
		/* HELP			 */
		/* ************* */
        set @subAppPageName = @thispageName + 'Help';
        EXEC dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID,
            @siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=1, @sectionID=@thisRootSectionID,
            @ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='Help & FAQ',
            @pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT;

        EXEC dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID,
            @resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
            @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Help & FAQ',
            @contentDesc='',
            @rawContent='Welcome to this community. Click around and see what''s here.',
            @contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT;

		/* ************* */
		/* PHOTO GALLERY */
		/* ************* */
		/*
		set @subAppPageName = @thispageName + 'Photos';
		set @subAppPageTitle = 'Photos';

		EXEC dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@isVisible=1,@pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@allowSubGalleries=1, @creatorMemberID=@MCsystemMemberID, @applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='',
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, @siteresourceID=@subAppResourceID OUTPUT,
			@pageID=@subAppPageID OUTPUT;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@viewFunctionIDList, 
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@editOwnFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@AddPhotosFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@deleteOwnFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;

		EXEC dbo.cms_createApplicationWidgetInstance @siteid=@siteID, @applicationInstanceID=@subAppApplicationInstanceID,
			@applicationWidgetTypeID=@recentPhotosWidgetTypeID, @applicationWidgetInstanceName='Recent Photos',
			@applicationWidgetInstanceDesc='Recent Photos', @applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
			@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT;
		exec dbo.cms_createPageZoneResource @pageID=@commHomePageID, @zoneID=@zoneIDB, 
			@siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT;
		*/

		/* ************* */
		/* VIDEO GALLERY */
		/* ************* */
		/*
		set @subAppPageName = @thispageName + 'Videos';
		set @subAppPageTitle = 'Videos';

		EXEC dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID,
			@isVisible=1,@pageName=@subAppPageName, @pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@thissiteresourceID, @allowReturnAfterLogin=1,
			@allowSubGalleries=1, @creatorMemberID=@MCsystemMemberID, @applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='',
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, @siteresourceID=@subAppResourceID OUTPUT,
			@pageID=@subAppPageID OUTPUT;

		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@viewFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thissiteResourceID, 
			@inheritedRightsFunctionID=@viewFunctionID;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@editOwnFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID, 
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@deleteOwnFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID,
			@inheritedRightsFunctionID=@participateFunctioniD;
		exec dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionIDList=@AddVideosFunctionIDList,
			@roleID=null, @groupID=null,  @inheritedRightsResourceID=@thisSiteResourceID,
			@inheritedRightsFunctionID=@participateFunctioniD;

		EXEC dbo.cms_createApplicationWidgetInstance @siteid=@siteID, @applicationInstanceID=@subAppApplicationInstanceID,
			@applicationWidgetTypeID=@recentVideosWidgetTypeID, @applicationWidgetInstanceName='Recent Videos',
			@applicationWidgetInstanceDesc='Recent Videos', @applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
			@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT;
		exec dbo.cms_createPageZoneResource @pageID=@commHomePageID, @zoneID=@zoneIDB, 
			@siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT;
		*/
		COMMIT TRAN;
		
		SELECT @thisPageName = min(pageName) from @commList where pageName > @thisPageName;
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH

GO

-- change navigation to horizontal


DECLARE @sitecode varchar(10)
DECLARE @commList TABLE (autoID int IDENTITY(1,1), pageName varchar(100), pageTitle varchar(200));

SET @sitecode = 'SDCBA';

declare @communitySectionID int, @zoneIDA int= dbo.fn_getZoneID('A'), @zoneIDMain int = dbo.fn_getZoneID('Main'), @siteID int = dbo.fn_getSiteIDFromSiteCode(@sitecode);

declare @settingsXML xml = '<settings horizontal="false" communityHDR="True" />'

INSERT INTO @commlist (pageName, pageTitle) VALUES ('SGOVERComm', 'Government Law');


--update awi with new settingsXML
--select awi.siteResourceID, awi.settingsXML, @settingsXML
update awi set 
	settingsXML = @settingsXML
from @commlist cl
inner join cms_pages p
	on cl.pageName = p.pageName
	and p.siteID = @siteID
inner join cms_siteResources sr
	on sr.siteResourceID = p.siteResourceID
	and sr.siteResourceStatusID = 1
inner join cms_siteResources commsr
	on commsr.parentSiteResourceID = p.siteResourceID
inner join cms_applicationInstances ai
	on commsr.siteResourceID = ai.siteResourceID
inner join cms_applicationTypes at
	on at.applicationTypeID = ai.applicationTypeID
	and at.applicationTypeName = 'Community'
inner join cms_applicationWidgetTypes awt
	on awt.applicationTypeID = at.applicationTypeID
	and awt.applicationWidgetTypeName='communityNavigation'
inner join cms_applicationWidgetInstances awi
	on awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	and awi.applicationInstanceID = ai.applicationInstanceID


--add entries to cms_pageSectionZonesResources
insert into [dbo].[cms_pageSectionsZonesResources] ([sectionID], [zoneID], [siteResourceID], [sortOrder])
select ps.sectionID, @zoneIDA as zoneID, awi.siteResourceID as siteResourceID, 1 as sortOrder
from @commlist cl
inner join cms_pages p
	on cl.pageName = p.pageName
	and p.siteID = @siteID
inner join cms_siteResources sr
	on sr.siteResourceID = p.siteResourceID
	and sr.siteResourceStatusID = 1
inner join cms_siteResources commsr
	on commsr.parentSiteResourceID = p.siteResourceID
inner join cms_applicationInstances ai
	on commsr.siteResourceID = ai.siteResourceID
inner join cms_applicationTypes at
	on at.applicationTypeID = ai.applicationTypeID
	and at.applicationTypeName = 'Community'
inner join cms_applicationWidgetTypes awt
	on awt.applicationTypeID = at.applicationTypeID
	and awt.applicationWidgetTypeName='communityNavigation'
inner join cms_applicationWidgetInstances awi
	on awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	and awi.applicationInstanceID = ai.applicationInstanceID
inner join cms_siteResources commSectionsr
	on commSectionsr.parentSiteResourceID = commsr.siteResourceID
inner join cms_pageSections ps
	on ps.siteResourceID = commSectionsr.siteResourceID



--delete entries from cms_pageZonesResources

--select pzr.*
delete pzr
from @commlist cl
inner join cms_pages p
	on cl.pageName = p.pageName
	and p.siteID = @siteID
inner join cms_siteResources sr
	on sr.siteResourceID = p.siteResourceID
	and sr.siteResourceStatusID = 1
inner join cms_siteResources commsr
	on commsr.parentSiteResourceID = p.siteResourceID
inner join cms_applicationInstances ai
	on commsr.siteResourceID = ai.siteResourceID
inner join cms_applicationTypes at
	on at.applicationTypeID = ai.applicationTypeID
	and at.applicationTypeName = 'Community'
inner join cms_applicationWidgetTypes awt
	on awt.applicationTypeID = at.applicationTypeID
	and awt.applicationWidgetTypeName='communityNavigation'
inner join cms_applicationWidgetInstances awi
	on awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	and awi.applicationInstanceID = ai.applicationInstanceID
inner join cms_pageZonesResources pzr
	on pzr.siteResourceID = awi.siteResourceID
