declare @messagesToDelete TABLE (messageID_ int PRIMARY KEY, deleteEMLCommand varchar(1000), deleteComponentsCommand varchar(1000))


insert into @messagesToDelete (messageID_, deleteEMLCommand, deleteComponentsCommand)

select messageID_,
deleteEMLCommand = 'aws s3 rm  s3://messages.membercentral.com/lyrisarchivemessagecomponents/' + list + '/' + modfolder +  '/' + cast(MessageID_ as varchar(10)) + '.eml',
deleteComponentsCommand = 'aws s3 rm  --recursive s3://messages.membercentral.com/lyrisarchivemessagecomponents/' + list + '/' + modfolder +  '/ --exclude "*" --include "' + cast(MessageID_ as varchar(10)) + '[._]*'

from (

select ml.list, m.MessageID_, m.ParentID_, m.HdrSubject_, m.CreatStamp_, m.attachmentflag,
    modfolder =  FORMAT(m.MessageID_ % 1000,'0000'),
    s3key = 'messages.membercentral.com/lyrisarchivemessages/' + ml.list + '/' + FORMAT(m.MessageID_ % 1000,'0000')  + '/' + cast(m.MessageID_ as varchar(10)) + '.eml',
    filename = ml.list + '-' +
        cast(m.ParentID_ as varchar(10)) + '-' + cast(m.MessageID_ as varchar(10)) + '-' +
        ltrim(rtrim(
        dbo.fn_RegExReplace(
            dbo.fn_RegExReplace(
                dbo.fn_RegExReplace(m.HdrSubject_,'\s+','-'),
                '[^\w\-]+', 
                ''
            ),
            '\-\-+',
            '-'
        ))) + '.eml'

from trialslyris1.dbo.lists_format lf
inner join dbo.messageLists ml
    on ml.list = lf.name
    and lf.orgcode = 'EC'
inner join messages_ m
    on m.listID = ml.listID
    and m.CreatStamp_ > '9/1/2020'
    and (

 m.HdrSubject_  like '%Update on DivComm Panel on BlackLives Matter%' or
m.HdrSubject_  like '%PLEASE READ: CELA Members'' Letter to CELA Community%' or
m.HdrSubject_  like '%On Professor Melina Abdullah -- A Few Thoughts%' or
m.HdrSubject_  like '%(re-post with images) PLEASE READ: CELA Members'' Letter to CELA Community%' or
m.HdrSubject_  like '%Urgent- On-TOPIC Post regarding Prof. Abdullah - needs response from Div Com%' or
m.HdrSubject_  like '%Annual Conference Speaker%' or
m.HdrSubject_  like '%Statement re Melina Abdullah, Ph.D%'

    )
) as raw
order by ParentID_, messageid_



select deleteEMLCommand
from @messagesToDelete

select deleteComponentsCommand
from @messagesToDelete


-- delete from lyris and lyrisarchive

delete ma
from @messagesToDelete md
inner join dbo.messagesToArchive ma 
    on md.messageID_ = ma.MessageID_;


delete m
from @messagesToDelete md
inner join lyrisarchive.dbo.messages_ m
    on md.messageID_ = m.MessageID_;


delete mst
from @messagesToDelete md
inner join lyrisarchive.dbo.messageSearchText mst
    on md.messageID_ = mst.MessageID_;


delete lm
from @messagesToDelete md
inner join trialslyris1.dbo.messages_ lm
    on md.messageID_ = lm.MessageID_;


