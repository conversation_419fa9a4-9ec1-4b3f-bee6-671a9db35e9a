USE [EmailTracking]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[incomingMessageInmailMappings]') AND type in (N'U'))
DROP TABLE [dbo].[incomingMessageInmailMappings]
GO

CREATE TABLE [dbo].[incomingMessageInmailMappings](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[incomingMessageID] [bigint] NOT NULL,
	[messageID_] [int] NULL,
	[toAddress] [varchar](320) NOT NULL,
 CONSTRAINT [PK_incomingMessageInmailMappings] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


ALTER TABLE dbo.incomingMessages ADD
	sentTimestamp bigint NULL
GO