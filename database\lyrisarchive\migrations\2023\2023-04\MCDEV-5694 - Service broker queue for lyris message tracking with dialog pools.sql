use EmailTracking
GO

-- this is based on https://learn.microsoft.com/en-us/archive/blogs/sql_service_broker/reusing-dialogs-with-a-dialog-pool

CREATE TABLE [sb_dialogPool] (
	FromService SYSNAME NOT NULL,
	ToService SYSNAME NOT NULL,
	OnContract SYSNAME NOT NULL,
	Handle UNIQUEIDENTIFIER NOT NULL,
	OwnerSPID INT NOT NULL,
	CreationTime DATETIME NOT NULL,
	SendCount BIGINT NOT NULL,
	UNIQUE (Handle)
);
GO

CREATE TABLE [dbo].[sb_ServiceBrokerErrorLogs](
	[LogID] [bigint] IDENTITY(1,1) NOT NULL,
	[LogDate] [datetime] NOT NULL,
	[ErrorMessage] [nvarchar](2048) NULL,
	[MessageBody] [varbinary](max) NULL,
 CONSTRAINT [PK_sb_ServiceBrokerErrorLogs] PRIMARY KEY CLUSTERED 
(
	[LogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[sb_ServiceBrokerErrorLogs] ADD  DEFAULT (getdate()) FOR [LogDate]
GO


CREATE PROC [dbo].[up_ErrorHandler]
AS

DECLARE @errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int;

SELECT	@errmsg = error_message(), @severity = error_severity(), @state = error_state(), 
		@errno = error_number(), @proc = error_procedure(), @lineno = error_line();
   
IF @errmsg NOT LIKE '***%'
	SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + ', Line ' + ltrim(str(@lineno)) + '. Errno ' + ltrim(str(@errno)) + ': ' + @errmsg;

RAISERROR('%s', @severity, @state, @errmsg); -- use this approach in case errmsg has a percent character

on_done:
RETURN 0;
GO




CREATE PROC dbo.sb_getDialogFromPool
@fromService SYSNAME,
@toService SYSNAME,
@onContract SYSNAME,
@dialogHandle UNIQUEIDENTIFIER OUTPUT,
@sendCount BIGINT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Reuse a free dialog in the pool or create a new one in case no free dialogs exist.
	DECLARE @tblDialog TABLE (FromService SYSNAME NOT NULL, ToService SYSNAME NOT NULL, OnContract SYSNAME NOT NULL, 
		Handle UNIQUEIDENTIFIER NOT NULL, OwnerSPID INT NOT NULL, CreationTime DATETIME NOT NULL, SendCount BIGINT NOT NULL);

	-- Try to claim an unused dialog in sb_dialogPool
	-- READPAST option avoids blocking on locked dialogs.
	BEGIN TRAN;
		DELETE FROM @tblDialog;

		UPDATE TOP(1) dbo.sb_dialogPool WITH(READPAST)
		SET OwnerSPID = @@SPID
			OUTPUT INSERTED.* INTO @tblDialog
		WHERE FromService = @fromService
		AND ToService = @toService
		AND OnContract = @OnContract
		AND OwnerSPID = -1;

		IF @@ROWCOUNT > 0 BEGIN
			SET @dialogHandle = (SELECT Handle FROM @tblDialog);
			SET @sendCount = (SELECT SendCount FROM @tblDialog);
		END
		ELSE BEGIN
			-- No free dialogs: need to create a new one
			BEGIN DIALOG CONVERSATION @dialogHandle
			FROM SERVICE @fromService
			TO SERVICE @toService
			ON CONTRACT @onContract
			WITH ENCRYPTION = OFF;

			INSERT INTO dbo.sb_dialogPool (FromService, ToService, OnContract, Handle, OwnerSPID, CreationTime, SendCount)
			VALUES (@fromService, @toService, @onContract, @dialogHandle, @@SPID, GETDATE(), 0);

			SET @sendCount = 0;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sb_freeDialogFromPool
@dialogHandle UNIQUEIDENTIFIER,
@sendCount BIGINT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Return the dialog to the pool.
	DECLARE @rowcount INT, @string VARCHAR(50);

	BEGIN TRAN;
		-- Release dialog by setting OwnerSPID to -1.
		UPDATE dbo.sb_dialogPool 
		SET OwnerSPID = -1, 
			SendCount = @sendCount 
		WHERE Handle = @dialogHandle;

		SELECT @rowcount = @@ROWCOUNT;

		IF @rowcount = 0 BEGIN
			SET @string = (SELECT CAST(@dialogHandle AS VARCHAR(50)));
			RAISERROR('sb_freeDialogFromPool: dialog %s not found in dialog pool', 16, 1, @string) WITH LOG;
		END
		IF @rowcount > 1 BEGIN
			SET @string = (SELECT CAST(@dialogHandle AS VARCHAR(50)));
			RAISERROR('sb_freeDialogFromPool: duplicate dialog %s found in dialog pool', 16, 1, @string) WITH LOG;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sb_deleteDialogFromPool
@dialogHandle UNIQUEIDENTIFIER

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Delete the dialog from the pool. This does not end the dialog.
	BEGIN TRAN;
		DELETE FROM dbo.sb_dialogPool
		WHERE Handle = @dialogHandle;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROCEDURE SendGridStatusTrackingInitiatorQueue_activated
AS

BEGIN

	DECLARE @handle UNIQUEIDENTIFIER;
	DECLARE @message_type SYSNAME;

	BEGIN TRANSACTION;
		WAITFOR (
			RECEIVE TOP(1) @handle = [conversation_handle], @message_type = [message_type_name]
			FROM SendGridStatusTrackingInitiatorQueue
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 1 BEGIN
			-- Expect target response to EndOfStream message.
			IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @handle;
			END
		END
	COMMIT TRAN;

END
GO

CREATE PROC dbo.SendGridStatusTrackingTargetQueue_Activated
AS

SET NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @receive_table TABLE (queuing_order BIGINT, [conversation_handle] UNIQUEIDENTIFIER, message_type_name SYSNAME, message_body VARBINARY(MAX));
DECLARE message_cursor CURSOR LOCAL FORWARD_ONLY READ_ONLY 
	FOR SELECT [conversation_handle], message_type_name, message_body 
	FROM @receive_table 
	ORDER BY queuing_order;
DECLARE @conversation_handle UNIQUEIDENTIFIER, @message_type SYSNAME, @message_body VARBINARY(max), @xmldata xml, 
	@error_number INT, @error_message VARCHAR(4000), @error_severity INT, @error_state INT, @error_procedure SYSNAME, 
	@error_line INT, @error_dialog VARCHAR(50);

BEGIN TRY
WHILE (1 = 1) BEGIN
	BEGIN TRANSACTION;

		-- Receive all available messages into the table.
		-- Wait 5 seconds for messages.
		WAITFOR (
			RECEIVE [queuing_order], [conversation_handle], [message_type_name], [message_body]
			FROM SendGridStatusTrackingTargetQueue
			INTO @receive_table
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 0 BEGIN
			COMMIT;
			BREAK;
		END ELSE BEGIN
			OPEN message_cursor;
			WHILE (1=1) BEGIN
				FETCH NEXT FROM message_cursor INTO @conversation_handle, @message_type, @message_body; 
				IF (@@FETCH_STATUS != 0) BREAK;

				BEGIN TRY
					IF @message_type = 'emailTracking/GeneralXMLRequest' BEGIN
						-- process the msg.
						SET @xmldata = cast(@message_body as xml);

					END
					IF @message_type = 'emailTracking/EndOfStream' BEGIN
						-- initiator is signaling end of message stream: end the dialog
						END CONVERSATION @conversation_handle;
					END
					IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
						WITH XMLNAMESPACES ('https://schemas.microsoft.com/SQL/ServiceBroker/Error' AS ssb)
						SELECT @error_number = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Code)[1]', 'INT'),
							@error_message = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Description)[1]', 'VARCHAR(4000)');
						SET @error_dialog = CAST(@conversation_handle AS VARCHAR(50));

						RAISERROR('Error in dialog %s: %s (%i)', 16, 1, @error_dialog, @error_message, @error_number);
						END CONVERSATION @conversation_handle;
					END
				END TRY
				BEGIN CATCH
					SET @error_message = ERROR_MESSAGE();
					IF XACT_STATE() = -1 BEGIN
						-- The transaction is doomed. Only rollback possible.
						-- This could disable the queue if done 5 times consecutively!
						ROLLBACK TRANSACTION;

						-- Record the error.
						BEGIN TRAN;
							INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
							VALUES(@error_message, @message_body);
						COMMIT TRAN;

						-- For this level of error, it is best to exit the proc and give the queue monitor control.
						-- Breaking to the outer catch will accomplish this.
						RAISERROR ('Message processing error', 16, 1);
					END
					ELSE IF XACT_STATE() = 1 BEGIN
						-- Record error and continue processing messages.
						-- Failing message could also be put aside for later processing here.
						-- Otherwise it will be discarded.
						INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
						VALUES(@error_message, @message_body);
					END
				END CATCH
			END

			CLOSE message_cursor;
			DELETE FROM @receive_table;
		END

	COMMIT TRAN;
END
END TRY
BEGIN CATCH
	-- Process the error and exit the proc to give the queue monitor control
	SET @error_message = ERROR_MESSAGE();

	IF XACT_STATE() = -1 BEGIN
		-- The transaction is doomed. Only rollback possible.
		-- This could disable the queue if done 5 times consecutively!
		ROLLBACK TRANSACTION;

		-- Record the error.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
	ELSE IF XACT_STATE() = 1 BEGIN
		-- Record error and commit transaction.
		-- Here you could also save anything else you want before exiting.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
END CATCH
GO

CREATE MESSAGE TYPE [emailTracking/GeneralXMLRequest] VALIDATION = WELL_FORMED_XML
GO
CREATE MESSAGE TYPE [emailTracking/EndOfStream]
GO

CREATE CONTRACT [emailTracking/GeneralXMLEOSContract] ([emailTracking/GeneralXMLRequest] SENT BY INITIATOR, [emailTracking/EndOfStream] SENT BY INITIATOR)
GO

CREATE QUEUE SendGridStatusTrackingInitiatorQueue WITH ACTIVATION (
	STATUS = ON,
	MAX_QUEUE_READERS = 1,
	PROCEDURE_NAME = dbo.SendGridStatusTrackingInitiatorQueue_activated,
	EXECUTE AS OWNER
);

CREATE QUEUE SendGridStatusTrackingTargetQueue WITH ACTIVATION (
	STATUS = ON,
	MAX_QUEUE_READERS = 1,
	PROCEDURE_NAME = dbo.SendGridStatusTrackingTargetQueue_Activated,
	EXECUTE AS OWNER
);

CREATE SERVICE SendGridStatusTrackingInitiatorService ON QUEUE SendGridStatusTrackingInitiatorQueue;
GO
CREATE SERVICE SendGridStatusTrackingTargetService ON QUEUE SendGridStatusTrackingTargetQueue ([emailTracking/GeneralXMLEOSContract]);
GO

CREATE PROC dbo.sb_dialogpool_sendMessage
@fromService SYSNAME,
@toService SYSNAME,
@onContract SYSNAME,
@messageType SYSNAME,
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	DECLARE @dialogHandle UNIQUEIDENTIFIER, @sendCount BIGINT, @counter INT = 1, @error INT;

	BEGIN TRAN;
		-- Will need a loop to retry in case the dialog is in a state that does not allow transmission
		WHILE (1=1) BEGIN
			-- Claim a dialog from the dialog pool.
			-- A new one will be created if none are available.
			EXEC dbo.sb_getDialogFromPool @fromService=@fromService, @toService=@toService, @onContract=@onContract, 
				@dialogHandle=@dialogHandle OUTPUT, @sendCount=@sendCount OUTPUT;

			BEGIN TRY
				-- Attempt to SEND on the dialog
				-- If the @messageBody is not null it must be sent explicitly
				IF (@messageBody IS NOT NULL)
					SEND ON CONVERSATION @dialogHandle MESSAGE TYPE @messageType (@messageBody);

				-- Messages with no body must *not* specify the body, cannot send a NULL value argument
				ELSE
					SEND ON CONVERSATION @dialogHandle MESSAGE TYPE @messageType;
			
				SELECT @error = @@ERROR;
			END TRY
			BEGIN CATCH
				SET @error = 1;
				SET @counter = 0;
			END CATCH

			IF @error = 0 BEGIN
				-- Successful send, increment count and exit the loop
				SET @sendCount = @sendCount + 1;
				BREAK;
			END

			SELECT @counter = @counter+1;
			IF @counter > 10 BEGIN
				-- We failed 10 times in a row, something must be broken
				RAISERROR('Failed to SEND on a conversation for more than 10 times. Error %i.', 16, 1, @error) WITH LOG;
				BREAK;
			END

			-- Delete the associated dialog from the table and try again
			EXEC dbo.sb_deleteDialogFromPool @dialogHandle=@dialogHandle;
			SELECT @dialogHandle = NULL;
		END

		-- "Criterion" for dialog pool removal is send count > 1000.
		-- Modify to suit application.
		-- When deleting also inform the target to end the dialog.
		IF @sendCount > 1000 BEGIN
			EXEC dbo.sb_deleteDialogFromPool @dialogHandle=@dialogHandle;

			SEND ON CONVERSATION @dialogHandle MESSAGE TYPE [emailTracking/EndOfStream];
		END
		ELSE BEGIN
			-- Free the dialog.
			EXEC dbo.sb_freeDialogFromPool @dialogHandle=@dialogHandle, @sendCount=@sendCount;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sendgridStatusTracking_sendMessage
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	EXEC dbo.sb_dialogpool_sendMessage N'SendGridStatusTrackingInitiatorService', N'SendGridStatusTrackingTargetService', 
		N'emailTracking/GeneralXMLEOSContract', N'emailTracking/GeneralXMLRequest', @messageBody;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO




-- Remove service broker pieces in lyrisCustom for email tracking

USE [lyrisCustom]
GO

DROP SERVICE [SendGridStatusTrackingInitiatorService]
GO

DROP SERVICE [SendGridStatusTrackingTargetService]
GO

DROP QUEUE [dbo].[SendGridStatusTrackingTargetQueue]
GO

DROP QUEUE [dbo].[SendGridStatusTrackingInitiatorQueue]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sb_dialogPool]') AND type in (N'U'))
DROP TABLE [dbo].[sb_dialogPool]
GO

DROP PROCEDURE [dbo].[sb_deleteDialogFromPool]
GO

DROP PROCEDURE [dbo].[sb_dialogpool_sendMessage]
GO

DROP PROCEDURE [dbo].[sb_freeDialogFromPool]
GO

DROP PROCEDURE [dbo].[sb_getDialogFromPool]
GO

DROP PROCEDURE [dbo].[sendgridStatusTracking_sendMessage]
GO

DROP PROCEDURE [dbo].[SendGridStatusTrackingInitiatorQueue_activated]
GO

DROP PROCEDURE [dbo].[SendGridStatusTrackingTargetQueue_Activated]
GO




