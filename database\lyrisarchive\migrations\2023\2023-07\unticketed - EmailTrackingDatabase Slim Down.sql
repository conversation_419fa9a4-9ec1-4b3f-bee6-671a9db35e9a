use emailTracking
GO

DROP INDEX [idx_Recipients_siteID_domainID_username] ON [dbo].[recipients]
GO
DROP INDEX [IX_recipients__siteID_username_domainID_dateAcknowledged__include3] ON [dbo].[recipients]
GO

CREATE NONCLUSTERED INDEX [IX_recipients__siteID_sendingApplicationID_domainID_username_dateAcknowledged__include5] ON [dbo].[recipients]
(
	[siteID] ASC,
    [sendingApplicationID] ASC,
	[username] ASC,
	[domainID] ASC)
INCLUDE([sendingApplicationMessageID],[DeliveryStatusID],[dateAcknowledged],[firstOpened],[firstClicked]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO


declare @cutoff datetime = cast(dateadd(day,-30,getdate()) as date), @minsendingApplicationMessageIDToKeep int=0
declare @doit int = 1, @batchsize int = 1000, @topID bigint=0, @topIDstring VARCHAR(25);

declare @recipientBatch TABLE ([sg_message_id] varchar(100) PRIMARY KEY)
declare @eventBatch TABLE ([sg_event_id] varchar(100) PRIMARY KEY)

while @doit > 0 begin

	delete from @recipientBatch

	insert into @recipientBatch (sg_message_id)
	select top (@batchsize) sg_message_id 
    from [dbo].[recipients]
    where [dateAcknowledged] < @cutoff

	raiserror ('Loop # %ds', 10,1, @doit) with nowait

	delete r -- WITH (UPDLOCK, READPAST)
	from @recipientBatch b
	inner join [dbo].[recipients] r
		on r.sg_message_id = b.sg_message_id

	IF @@ROWCOUNT = 0
		set @doit = -10;

	set @doit = @doit + 1;

	IF @doit%10 = 0 BEGIN
		raiserror ('Waiting on Loop # %d', 10,1, @doit) with nowait
		WAITFOR DELAY '00:00:00.500';

	END

	-- for testing purposes, set number of batches aftter which you want to exit the loop
	-- IF @doit > 50 set @doit = 0;
end
GO



declare @cutoff datetime = cast(dateadd(day,-30,getdate()) as date)
declare @doit int = 1, @batchsize int = 500, @topID bigint=0, @topIDstring VARCHAR(25);

declare @eventBatch TABLE ([sg_event_id] varchar(100) PRIMARY KEY)

while @doit > 0 begin

	delete from @eventBatch

	insert into @eventBatch (sg_event_id)
	select top (@batchsize) sg_event_id 
    from [dbo].[recipientOpens]
    where [dateReceived] < @cutoff

	raiserror ('Loop # %ds', 10,1, @doit) with nowait

	delete r -- WITH (UPDLOCK, READPAST)
	from @eventBatch b
	inner join [dbo].[recipientOpens] r
		on r.sg_event_id = b.sg_event_id

	IF @@ROWCOUNT = 0
		set @doit = -10;

	set @doit = @doit + 1;

	IF @doit%10 = 0 BEGIN
		raiserror ('Waiting on Loop # %d', 10,1, @doit) with nowait
		WAITFOR DELAY '00:00:00.500';

	END

	-- for testing purposes, set number of batches aftter which you want to exit the loop
	IF @doit > 50 set @doit = 0;
end
GO



declare @cutoff datetime = cast(dateadd(day,-30,getdate()) as date)
declare @doit int = 1, @batchsize int = 500, @topID bigint=0, @topIDstring VARCHAR(25);

declare @eventBatch TABLE ([sg_event_id] varchar(100) PRIMARY KEY)

while @doit > 0 begin

	delete from @eventBatch

	insert into @eventBatch (sg_event_id)
	select top (@batchsize) sg_event_id 
    from [dbo].[recipientDeliveryMessages]
    where [dateReceived] < @cutoff

	raiserror ('Loop # %ds', 10,1, @doit) with nowait

	delete r -- WITH (UPDLOCK, READPAST)
	from @eventBatch b
	inner join [dbo].[recipientDeliveryMessages] r
		on r.sg_event_id = b.sg_event_id

	IF @@ROWCOUNT = 0
		set @doit = -10;

	set @doit = @doit + 1;

	IF @doit%10 = 0 BEGIN
		raiserror ('Waiting on Loop # %d', 10,1, @doit) with nowait
		WAITFOR DELAY '00:00:00.500';

	END

	-- for testing purposes, set number of batches aftter which you want to exit the loop
	IF @doit > 50 set @doit = 0;
end
GO
