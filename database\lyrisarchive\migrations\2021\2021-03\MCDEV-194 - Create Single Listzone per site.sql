use lyrisarchive;
GO

declare @lists TABLE (list_ varchar(100), sitecode varchar(10), listID int PRIMARY KEY, zoneID int, numMessages int)
declare @mainzones TABLE (sitecode varchar(10), zoneID int)
declare @newListZones TABLE (zoneID int, [zoneName] varchar(255),[zoneTypeID] int,[siteCode] varchar(10) PRIMARY KEY,[uniqueCode] varchar(50))
declare @activePublishers TABLE (publisherID int PRIMARY KEY, sitecode varchar(10))


declare @listserverZoneTypeID int;
select @listserverZoneTypeID = zoneTypeID from membercentral.membercentral.dbo.ad_zoneTypes where zoneTypeName = 'Listserver';


insert into @activePublishers (publisherID, sitecode)
select p.publisherID, p.sitecode
from membercentral.membercentral.dbo.ad_publishers p
inner join membercentral.membercentral.dbo.ad_zones z
    on z.siteCode = p.sitecode
    and z.zonetypeID = @listserverZoneTypeID
inner join membercentral.membercentral.dbo.ad_adplacements ap
    on ap.zoneID = z.zoneID
    and ap.[status] = 'A'
group by p.publisherID,p.sitecode

insert into @lists (list_,sitecode, zoneID, listID)
select ml.list, z.sitecode, z.zoneID, ml.listID
from membercentral.membercentral.dbo.ad_zones z
inner join membercentral.membercentral.dbo.ad_adplacements ap
    on ap.zoneID = z.zoneID
    and ap.status = 'A'
inner join lyrisarchive.dbo.messagelists ml
    on ml.list = z.uniqueCode collate Latin1_General_CI_AI
inner join trialslyris1.dbo.lists_format lf
    on lf.name = ml.list
    and lf.orgcode = z.sitecode collate Latin1_General_CI_AI
where z.zonetypeID=2
group by ml.list, z.sitecode, z.zoneid, ml.listID
order by ml.list

update l set
    numMessages = temp.numMessages
from @lists l
inner join (
    select l.listID, count(*) as numMessages
    from @lists l
    inner join messages_ m
        on m.listID = l.listID
        and m.CreatStamp_ > '1/1/2020'
    group by l.listID
) as temp on temp.listID = l.listID

insert into @mainzones (sitecode, zoneID)
select distinct sitecode, 
     mainZoneID = FIRST_VALUE(zoneID) over (PARTITION BY sitecode ORDER BY numMessages desc)
from @lists

insert into @newListZones (zoneName,zoneTypeID,siteCode, uniqueCode)
select 'Discussion List Footers' as ZoneName, @listserverZoneTypeID as zoneTypeID, mz.sitecode, 'Discussion List Footers' as uniquecode
from @mainzones mz

-- add zones for sites with no activity
insert into @newListZones (zoneName,zoneTypeID,siteCode, uniqueCode)
select 'Discussion List Footers' as ZoneName, @listserverZoneTypeID as zoneTypeID, sitecode, 'Discussion List Footers' as uniquecode
from @activePublishers
except
select zoneName,zoneTypeID,siteCode, uniqueCode 
from @newListZones


-- insert new ad zones
insert into membercentral.membercentral.dbo.ad_zones (zoneName,	zoneTypeID,	siteCode, uniqueCode)
select zoneName collate DATABASE_DEFAULT,zoneTypeID,	siteCode collate DATABASE_DEFAULT, uniqueCode collate DATABASE_DEFAULT
from @newListZones
except 
select zoneName,zoneTypeID,	siteCode, uniqueCode
from membercentral.membercentral.dbo.ad_zones

-- update zoneID in @newListZones
update lz set
    zoneID = temp.zoneID
from @newListZones lz
inner join (
    select zoneID, siteCode
    from  membercentral.membercentral.dbo.ad_zones
    where zoneTypeID=@listserverZoneTypeID and uniqueCode = 'Discussion List Footers'
) as temp on temp.sitecode = lz.sitecode collate Latin1_General_CI_AI


delete ap
from @newListZones lz
inner join membercentral.membercentral.dbo.ad_adplacements ap
    on ap.zoneID = lz.zoneID

-- copy adplacements from mainzone to newzone

insert into membercentral.membercentral.dbo.ad_adplacements (adID, zoneID, [status])
select ap.adID, lz.zoneID, ap.[status]
from @mainzones mz
inner join membercentral.membercentral.dbo.ad_adplacements ap
    on ap.zoneID = mz.zoneID
inner join @newListZones lz
    on lz.sitecode = mz.sitecode

EXCEPT

select ap.adID, ap.zoneID, ap.[status]
from membercentral.membercentral.dbo.ad_adplacements ap


insert into membercentral.platformStatsMC.dbo.ad_adplacementrotation(adplacementID, lastShown)
select ap_new.adPlacementID, apr.lastShown
from @mainzones mz
inner join membercentral.membercentral.dbo.ad_adplacements ap
    on ap.zoneID = mz.zoneID
inner join @newListZones lz
    on lz.sitecode = mz.sitecode
inner join membercentral.membercentral.dbo.ad_adplacements ap_new
    on ap_new.zoneID = lz.zoneID
    and ap.adID = ap_new.adID
inner join membercentral.platformStatsMC.dbo.ad_adplacementrotation apr
    on apr.adplacementID = ap.adPlacementID
left outer join membercentral.platformStatsMC.dbo.ad_adplacementrotation apr2
    on apr2.adplacementID = ap_new.adPlacementID
where apr2.adplacementID is null

-- copy ad_advertiserZones from mainzone to newzone

insert into membercentral.membercentral.dbo.ad_advertiserZones (zoneID, advertiserID)
select lz.zoneID, az.advertiserID
from @mainzones mz
inner join membercentral.membercentral.dbo.ad_advertiserZones az
    on az.zoneID = mz.zoneID
inner join @newListZones lz
    on lz.sitecode = mz.sitecode

EXCEPT

select zoneID, advertiserID
from membercentral.membercentral.dbo.ad_advertiserZones


GO


