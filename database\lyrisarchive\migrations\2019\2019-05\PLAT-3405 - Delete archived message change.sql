use lyrisArchive
GO

ALTER PROC dbo.lyris_removeMessage
@messageid_ int,
@filelocation varchar(200)

AS

SET NOCOUNT ON

-- queue deletion in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3
CREATE TABLE #tmpS3 (objectKey varchar(200), size bigint)

DECLARE @sql varchar(max)
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')'
EXEC(@sql)

INSERT INTO membercentral.platformQueue.dbo.queue_S3Delete (s3bucketName, objectKey)
SELECT 'messages.membercentral.com', objectKey
FROM #tmpS3

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3

-- delete from lyris and lyrisarchive
delete from dbo.messagesToArchive
where messageid_ = @messageid_

delete from lyrisarchive.dbo.messages_ 
where messageid_ = @messageid_

delete from dbo.messageSearchText 
where messageid_ = @messageid_

delete from trialslyris1.dbo.messages_ 
where messageid_ = @messageid_

SET NOCOUNT OFF

RETURN 0
GO