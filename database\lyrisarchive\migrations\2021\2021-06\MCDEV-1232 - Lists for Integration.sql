use LyrisArchive
GO

select 
	lf.orgcode,
	l.name_,
	l.creatStamp_,
	l.descShort_,
	(select count(*) from trialslyris1.dbo.members_ where list_ = l.name_ and membertype_ in ('normal','held')) as numTotalMembers,
	(select count(*) from trialslyris1.dbo.members_ where list_ = l.name_ and membertype_ in ('normal','held') and nullif(ExternalMemberID,'') is null) as numNonAssociatedMembers,
	(select count(*) from trialslyris1.dbo.members_ where list_ = l.name_ and membertype_ in ('normal','held') and nullif(ExternalMemberID,'') is not null) as numAssociatedMembers,
	(
		select max(creatStamp_)
		from messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_
		group by ml.list
	) as mostRecentMessage,
	(
		select min(creatStamp_)
		from messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_
		group by ml.list
	) as oldestMessage,
	(
		select count(creatStamp_)
		from messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_
		group by ml.list
	) as numMessagesInArchive
from trialslyris1.dbo.lists_ l
left outer join trialslyris1.dbo.lists_format lf
	on l.name_ = lf.name
 left outer join membercentral.membercentral.dbo.lists_lists as ll2
	inner join membercentral.membercentral.dbo.cms_siteResources as sr on
		sr.siteResourceID = ll2.siteResourceID
	inner join membercentral.membercentral.dbo.sites as s on
		s.siteID = sr.siteID
	on s.siteCode = lf.orgcode collate SQL_Latin1_General_CP1_CI_AS
where
	s.siteCode is null	
order by lf.orgcode, l.name_