use lyrisCustom
go

ALTER PROC dbo.natle_justiceServices

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'natle_justiceservices';

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
CREATE TABLE #tempListMembers (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, membernumber VARCHAR(100), 
	fullname VARCHAR(500), email VARCHAR(200), usernameLC_ VARCHAR(100), domain_ VARCHAR(150));

EXEC membercentral.customApps.dbo.natle_justiceServicesEligible;

IF NOT EXISTS (SELECT * FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = 'Error Updating NATLE Justice Services List';
	SET @errmsg = 'customApps.dbo.natle_justiceServicesEligible ended with no rows in table dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END ELSE BEGIN

	INSERT INTO #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	UPDATE #tempListMembers 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- mark email addresses that are NOT in temp table as expired
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ AS m  
	LEFT OUTER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND m.membertype_ in ('normal','held')
	AND m.association_ NOT IN ('CT');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ AS m 
	INNER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName
	AND m.membertype_ = 'expired'
	AND m.association_ NOT IN ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	UPDATE m 
	SET domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	FROM #tempListMembers AS tmp
	INNER JOIN trialslyris1.dbo.members_ AS m ON m.list_ = @listName
		AND m.association_ = tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.ExternalMemberID = tmp.memberNumber COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.emailaddr_ <> tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS)
		AND m.association_ NOT IN ('CT')
		AND m.membertype_ = 'normal'
		AND m.Domain_ = tmp.domain_  COLLATE SQL_Latin1_General_CP1_CI_AS
	LEFT OUTER JOIN trialslyris1.dbo.members_ AS prexistingEmail ON prexistingEmail.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND prexistingEmail.list_ = @listName
		AND prexistingEmail.memberID_ <> m.memberID_
	WHERE prexistingEmail.memberID_ IS NULL;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #tempListMembers ec
	WHERE email in (
		SELECT emailaddr_ COLLATE SQL_Latin1_General_CP1_CI_AS
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
	);

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber AS ExternalMemberID, orgcode
	FROM #tempListMembers
	WHERE orgcode NOT IN ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO