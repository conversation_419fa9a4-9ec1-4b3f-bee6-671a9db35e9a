use trialslyris1;
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.102.1.86'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ts_membercentraladmins';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO