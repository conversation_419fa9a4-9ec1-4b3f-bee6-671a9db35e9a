
use EmailTracking;
GO


IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[incomingMessageInmailMappings]') AND type in (N'U'))
DROP TABLE [dbo].[incomingMessageInmailMappings]
GO

CREATE TABLE [dbo].[incomingMessageInmailMappings](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[sesID] [varchar](150) NOT NULL,
	[messageID_] [int] NOT NULL,
	[toAddress] [varchar](320) NOT NULL,
	[fromAddress] [varchar](320) NOT NULL,
 CONSTRAINT [PK_incomingMessageInmailMappings] PRIMARY KEY CLUSTERED 
(
	[messageID_] ASC,
	[toAddress] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [IX__incomingMessageInmailMappings__sesID_inc1] ON [dbo].[incomingMessageInmailMappings]
(
	[sesID] ASC
)
INCLUDE([id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

CREATE NONCLUSTERED INDEX [IX__incomingMessageInmailMappings__id] ON [dbo].[incomingMessageInmailMappings]
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX__incomingMessageInmailMappings__sedid_toEmail] ON [dbo].[incomingMessageInmailMappings]
(
	[sesID] ASC,
	[toAddress] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO
CREATE NONCLUSTERED INDEX [IX__incomingMessages__sesID] ON [dbo].[incomingMessages]
(
	[sesID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = ON, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO


CREATE PROCEDURE emailTracking_recordInmailMapping
	@sesid varchar(150),
	@messageID int,
	@toEmail varchar(320),
	@fromEmail varchar(320)
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	--  Insert data into emailtracking.dbo.incomingMessageInmailMappings if it doesn't exist already
	IF NOT EXISTS (
		select 1
		from dbo.incomingMessageInmailMappings
		where sesid = @sesid
		and toAddress = @toEmail
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

        INSERT INTO emailtracking.dbo.incomingMessageInmailMappings
            (sesID, messageID_, toAddress, fromAddress)
        select @sesid, @messageID, @toEmail, @fromEmail
        WHERE NOT EXISTS(
       		select 1
            from dbo.incomingMessageInmailMappings
            where sesid = @sesid
            and toAddress = @toEmail
        );

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	END
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO


CREATE PROCEDURE emailTracking_recordIncomingMessageDeliveredStatus
	@sesid varchar(255)
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	-- Update the emailtracking.dbo.incomingMessages table
	UPDATE emailtracking.dbo.incomingMessages
	SET
		dateCreated = GETDATE(),
		[status] = 'DELIVERED',
		[reason] = 'Message added to Lyris for delivery'
		WHERE sesid = @sesid;

	RETURN 0;    
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO



CREATE PROCEDURE emailTracking_recordIncomingMessageStatus
	@hdrMessageid varchar(1000),
	@sesID varchar(150),
	@stepFunctionName varchar(250),
	@dateCreated datetime,
	@subject varchar(500),
	@fromAddress varchar(320),
	@area1Disposition varchar(50),
	@mcDisposition varchar(50),
	@status varchar(50),
	@reason varchar(3000),
	@sentTimestamp bigint,
    @incomingMessageID int OUTPUT
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Check if the record already exists based on sesID
	IF EXISTS (SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID)
	BEGIN
		-- Check if the sentTimestamp is greater than the existing record
		IF EXISTS (SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID AND @sentTimestamp > sentTimestamp)
		BEGIN
			-- Update the existing record
			UPDATE emailtracking.dbo.incomingMessages
			SET
				hdrMessageid = @hdrMessageid,
				sesID = @sesID,
				stepFunctionName = @stepFunctionName,
				dateCreated = @dateCreated,
				[subject] = @subject,
				[fromAddress] = @fromAddress,
				[area1Disposition] = @area1Disposition,
				[mcDisposition] = @mcDisposition,
				[status] = @status,
				[reason] = @reason,
				[sentTimestamp] = @sentTimestamp
			WHERE sesID = @sesID;

            SELECT @incomingMessageID = incomingMessageID
            FROM emailtracking.dbo.incomingMessages
            WHERE sesid = @sesid;
		END
	END
	ELSE
	BEGIN

        IF NOT EXISTS (
            SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID
        ) BEGIN
            SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
            -- Insert a new record
            INSERT INTO emailtracking.dbo.incomingMessages (
                [hdrMessageid],
                [sesID],
                [stepFunctionName],
                [dateCreated],
                [subject],
                [fromAddress],
                [area1Disposition],
                [mcDisposition],
                [status],
                [reason],
                [sentTimestamp]
            )
            select @hdrMessageid,
                @sesID,
                @stepFunctionName,
                @dateCreated,
                @subject,
                @fromAddress,
                @area1Disposition,
                @mcDisposition,
                @status,
                @reason,
                @sentTimestamp
            WHERE NOT EXISTS(
                SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID
            );
            set @incomingMessageID = SCOPE_IDENTITY();
            SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
        END
	END;

	RETURN 0;    
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
