use lyrisarchive
GO

CREATE PROC dbo.lyris_removeArchive
@listname varchar(100)

AS

set @listname = lower(@listname)

-- list must be removed from lyris already
IF NOT EXISTS (select name_ from LYRIS.trialslyris1.dbo.lists_ where name_ = @listname)
BEGIN

	delete from LYRIS.trialslyris1.dbo.lists_format
	where name = @listname

	declare @SRID int
	select @SRID = siteResourceID 
		from membercentral.membercentral.dbo.lists_lists
		where listname = @listname
	IF @SRID is not null	
		EXEC membercentral.membercentral.dbo.cms_deleteSiteResourceAndChildren @SRID

	delete from membercentral.membercentral.dbo.lists_lists
	where listname = @listname

	declare @listID int
	select @listID = listid 
		from lyrisarchive.dbo.messageLists 
		where list = @listname
	IF @listID is not null
		delete from lyrisarchive.dbo.messages_
		where listid = @listID

	delete from lyrisarchive.dbo.messageLists 
	where list = @listname

END

RETURN 0
GO
