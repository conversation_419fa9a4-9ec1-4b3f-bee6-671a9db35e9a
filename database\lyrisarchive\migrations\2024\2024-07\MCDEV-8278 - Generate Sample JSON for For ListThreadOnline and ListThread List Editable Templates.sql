USE lyrisarchive
GO

Create PROC [dbo].[lists_getThreadMessageInfo]
@listname varchar(100),
@threadid int

AS

declare @listThreads TABLE (messageid_ int PRIMARY KEY, threadStartDate datetime, firstMessageInDateRange datetime, listMessageCount int, totalMessageCount int, totalParticipantCount int, subject varchar(1000), hdrFrom varchar(200), fromAddress varchar(200), fullname varchar(500), memberNumber varchar (100))
declare @listMessages TABLE (messageid_ int PRIMARY KEY, threadMessageID int, messagedate datetime, hdrFrom varchar(200), fromaddress varchar(200), hasAttachments bit, memberNumber varchar (100),fullname varchar(500))


insert into @listThreads (messageid_, threadStartDate, firstMessageInDateRange, listMessageCount, subject, hdrFrom, fromAddress)
SELECT max(m.parentid_) as parentid_, parentMessage.CreatStamp_, min(m.CreatStamp_), count(m.messageid_), parentMessage.hdrSubject_,parentMessage.HdrFrom_, parentMessage.HdrFromSpc_
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
inner join dbo.messages_ parentmessage on m.parentID_ = parentMessage.messageID_
where ml.list = @listname 
and m.parentid_ is not null
and m.ParentID_ = @threadid
and m.isVisible=1
and parentmessage.isVisible=1
group by m.parentid_, parentMessage.hdrSubject_, parentMessage.CreatStamp_, parentMessage.HdrFrom_, parentMessage.HdrFromSpc_

update t2 SET
    totalMessageCount = temp.totalMessageCount,
    totalParticipantCount = temp.totalParticipantCount
from @listThreads t2
inner join (
    select t.messageid_, count(*) as totalMessageCount, count(distinct m.hdrfromspc_) as totalParticipantCount
    from @listThreads t
    inner join messages_ m 
        on m.ParentID_ = t.messageid_
	   and m.ParentID_ = @threadid
        and m.isVisible=1
    group by t.messageid_
) as temp on temp.messageid_ = t2.messageid_


update @listThreads SET
    totalMessageCount = 0
where totalMessageCount is null

insert into @listMessages  (messageid_, threadMessageID,messagedate, HdrFrom,  fromaddress, hasAttachments)
select m.MessageID_, t.messageid_, m.CreatStamp_, m.HdrFrom_,m.HdrFromSpc_, isnull(m.attachmentflag,0)
from @listThreads t
inner join messages_ m
    on t.messageid_ = m.ParentID_
	and m.ParentID_ = @threadid
    and m.isVisible=1


update t set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @listThreads t
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = t.fromaddress
    and m.List_ = @listname

update m2 set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @listMessages m2
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = m2.fromaddress
    and m.List_ = @listname


select t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.listMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,
    t.totalParticipantCount, listParticipantCount = count(distinct dm.fromaddress),
    messagesWithAttachments = sum( case when dm.hasAttachments = 1 then cast(1 as int) else cast(0 as int) end),
    numReplies = sum( case when dm.fromaddress = t.fromAddress then cast(0 as int) else cast(1 as int) end),
    participants = lyrisCustom.dbo.PipeList(distinct isnull(dm.fullname, dm.fromAddress))
from @listThreads t
inner join @listMessages dm
    on dm.threadMessageID = t.messageid_
group by t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.listMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,t.totalParticipantCount


select messageid_, threadMessageID , messagedate, hdrFrom, fromaddress, hasAttachments, memberNumber,fullname
from @listMessages
order by  messageID_

GO