USE lyrisarchive;
GO

CREATE FUNCTION dbo.fn_cleanInvalidXMLChars (@string varchar(max))
RETURNS varchar(max)
AS
BEGIN
	RETURN dbo.fn_RegExReplace(@string,'[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]','')
END
GO

USE lyrisCustom;
GO

ALTER PROC dbo.mc_saveMemberListsInfo
@siteID int,
@receiverMemberID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @mainMessage varchar(50);

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#mc_listMembersUpdate') IS NULL BEGIN
		RAISERROR('Unable to locate the temp table for processing.',16,1);
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	CREATE TABLE #tblExistingMember([MemberID_] [int],
		[SubType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[EmailAddr_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[FullName_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[ExpireDate_] [smalldatetime],
		[MemberType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[MCOption_lockAddress] [bit],
		[MCOption_lockName] [bit],
		[MCOption_keepActive] [bit],
		[receiveMCThreadIndex] [bit],
		[receiveMCThreadDigest] [bit]);
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX), listName varchar(60), email varchar(100));

	INSERT INTO #tblExistingMember([MemberID_], [SubType_], [EmailAddr_], [FullName_] , [ExpireDate_], [MemberType_], [MCOption_lockAddress], [MCOption_lockName],
		[MCOption_keepActive], [receiveMCThreadIndex], [receiveMCThreadDigest])
	SELECT lm.[MemberID_], lm.[SubType_], lm.[EmailAddr_], lm.[FullName_] , lm.[ExpireDate_], lm.[MemberType_], lm.[MCOption_lockAddress], lm.[MCOption_lockName],
		lm.[MCOption_keepActive], lm.[receiveMCThreadIndex], lm.[receiveMCThreadDigest]
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;
	
	UPDATE lm
	SET lm.SubType_ = tmp.subType,
		lm.EmailAddr_ = tmp.email,
		lm.FullName_ = tmp.fullname,
		lm.ExpireDate_ = CASE WHEN tmp.memberType <> 'expired' THEN NULL ELSE lm.ExpireDate_ END,
		lm.MemberType_ = tmp.memberType,
		lm.Domain_ = tmp.domain,
		lm.UserNameLC_ = tmp.username,
		lm.MCOption_lockAddress = tmp.MCOption_lockAddress,
		lm.MCOption_lockName = tmp.MCOption_lockName,
		lm.MCOption_keepActive = tmp.MCOption_keepActive,
		lm.receiveMCThreadIndex = tmp.receiveMCThreadIndex,
		lm.receiveMCThreadDigest = tmp.receiveMCThreadDigest
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Delivery Settings changed from [' + 
		CASE old.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END + '] to [' + 
		CASE m.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END
		+ '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.SubType_ <> m.SubType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'E-mail changed from [' + old.EmailAddr_ + '] to [' + m.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.EmailAddr_ <> m.EmailAddr_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Name changed from [' + ISNULL(NULLIF(old.FullName_,''),'blank') + '] to [' + ISNULL(NULLIF(m.FullName_,''),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.FullName_,'') <> ISNULL(m.FullName_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Expired Date changed from [' + ISNULL(CONVERT(VARCHAR(20),old.ExpireDate_,120),'blank') + '] to [' + ISNULL(CONVERT(VARCHAR(20),m.ExpireDate_,120),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.ExpireDate_,'') <> ISNULL(m.ExpireDate_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Member List Status changed from [' + old.MemberType_ + '] to [' + m.MemberType_ + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.MemberType_ <> m.MemberType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Address changed from [' + CASE WHEN old.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockAddress, 0) <> ISNULL(m.MCOption_lockAddress, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Name changed from [' + CASE WHEN old.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockName, 0) <> ISNULL(m.MCOption_lockName, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Keep Active changed from [' + CASE WHEN old.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_keepActive, 0) <> ISNULL(m.MCOption_keepActive, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Index of Topics setting changed from [' + CASE WHEN old.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadIndex, 0) <> ISNULL(m.receiveMCThreadIndex, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Summary of All Messages setting changed from [' + CASE WHEN old.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadDigest, 0) <> ISNULL(m.receiveMCThreadDigest, 0);

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Updated"' +
			', "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE listName = tmp.listName
				ORDER BY email, msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT listName
			FROM #tmpLogMessages
		) AS tmp;
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

USE trialslyris1;
GO

ALTER PROC dbo.mc_updateListMemberships
@debugMode bit = 0

AS

BEGIN TRY

	declare @progressLog varchar(max), @errorLog varchar(max), @CRLF varchar(2), @emailSubject varchar(500), @escalateError bit,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int, 
		@defaultMembertype varchar(100), @defaultSubType varchar(100), @defaultMCOption_keepActive bit, 
		@defaultMCOption_lockAddress bit, @defaultMCOption_lockName bit, @thisListName varchar(100), @thisListAutoID int,
		@thisListAutoManageActive bit, @message varchar(500), @lastrowcount int, @thisListOneWayList bit, @expireDateCutoff datetime,
		@runByMemberID int;

	set @crlf = char(13) + char(10);
	set @escalateError = 0;
	set @errorLog = '';
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	set @defaultMCOption_keepActive = 0;
	set @defaultMCOption_lockAddress = 0;
	set @defaultMCOption_lockName = 0;
	set @expireDateCutoff = dateadd(year,-1,getdate());

	exec MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #ListsForLyrisSync (autoid int IDENTITY(1,1), siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
		list_ varchar(100), isAutoManageActive bit);
	CREATE TABLE #tmpLogMessages (autoid int IDENTITY(1,1), siteID int, memberID int, listName varchar(100), msg varchar(500));

	truncate table dbo.MC_ListMembersForLyris;

	insert into #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	from membercentral.datatransfer.dbo.ListsForLyris
	order by orgcode, list_;

	-- delete lists that no longer exist in Lyris
	delete s
	from #ListsForLyrisSync s
	left outer join lists_ l
		on s.list_ = l.name_ collate Latin1_General_CI_AI
    where l.ListID_ is null


	insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	select lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.MCMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, right(lmfl.emailaddr_,len(lmfl.emailaddr_)-charindex('@',lmfl.emailaddr_)), 
		left(lmfl.emailaddr_,charindex('@',lmfl.emailaddr_)-1)
	from membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	inner join #ListsForLyrisSync lfl on lfl.list_ = lmfl.list_;

	-- null blank emails
	update dbo.MC_ListMembersForLyris
	set emailaddr_ = null
	where ltrim(rtrim(isnull(emailaddr_,''))) = '';
	
	-- loop list by list
	select @thisListAutoID = min(autoID) from #ListsForLyrisSync;
	while @thisListAutoID is not null BEGIN
		select @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		from #ListsForLyrisSync 
		where autoID = @thisListAutoID;

		if exists (select adminSend_ from dbo.lists_ where name_ = @thisListName and adminSend_ = 'T')
			set @thisListOneWayList = 1;
		else
			set @thisListOneWayList = 0;

		set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive as varchar(5));
		set @progressLog = @progressLog + @crlf + @message;
			if @debugMode = 1 RAISERROR(@message,0,1);

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding names to update';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = 'UpdateName'
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.fullname_ <> m.fullname_
				and (m.MCOption_lockName is null or m.MCOption_lockName=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select lm.siteID, lm.MCMemberID, lm.list_, 'Name changed from ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'UpdateName'
					and (m.MCOption_lockName is null or m.MCOption_lockName=0);

				-- update the full names that have been marked
				update m 
				set m.fullname_ = lm.fullname_
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'UpdateName'
					and (m.MCOption_lockName is null or m.MCOption_lockName=0);

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated names - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail' else 'UpdateEmail' end
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
			left outer join dbo.members_ existingAddresses on lm.list_ = existingAddresses.list_
				and (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			where existingAddresses.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from dbo.MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				if @thisListAutoManageActive = 1 BEGIN
					insert into #tmpLogMessages (siteID, memberID, listName, msg)
					select lm.siteID, lm.MCMemberID, lm.list_, 'Email changed from ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail1' else 'UpdateEmail1' end
			from (
				select min(autoID) as autoID 
				from MC_ListMembersForLyris 
				where list_ = @thisListName 
				and updateStatus is null 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ is not null
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				from dbo.MC_ListMembersForLyris lm2
				inner join dbo.members_ existingAddresses on lm2.list_ = existingAddresses.list_
					and (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ and existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				where lm2.list_ = @thisListName
				and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				if @thisListAutoManageActive = 1
				BEGIN
					insert into #tmpLogMessages (siteID, memberID, listName, msg)
					select lm.siteID, lm.MCMemberID, lm.list_, 'Email changed from ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m 
						on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);
					
					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				null as functionName, 'expired' as updateStatus, l.isAutoManageActive
			from #ListsForLyrisSync l
			inner join members_ m on l.autoID = @thisListAutoID
				and l.list_ = m.list_ collate Latin1_General_CI_AI
				and m.membertype_ in ('confirm','held','normal')
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
			left outer join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID collate Latin1_General_CI_AI
				and m.list_ = lm.list_ 
			where lm.autoID is null and (m.MCOption_keepActive is null or m.MCOption_keepActive=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				update m 
				set m.membertype_ = 'expired',
					m.ExpireDate_ = getdate()
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'expired';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm 
			set lm.updateStatus = 'reactivate'
			from members_ m
			inner join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
				and m.list_ = lm.list_
				and m.list_ = @thisListName
				and m.membertype_ = 'expired'
				and lm.functionName in ('managePopulation','manageStatus');

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';

				update m 
				set m.membertype_ = 'normal',
					m.ExpireDate_ = null
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Reactivated memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = 'added'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					and nullif(lm.emailaddr_,'') is not null
				left outer join members_ m
					on lm.list_ = m.list_
					and (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = right(lm.emailaddr_,len(lm.emailaddr_)-charindex('@',lm.emailaddr_)) and m.usernamelc_ = left(lm.emailaddr_,charindex('@',lm.emailaddr_)-1))
					)
			where m.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				from MC_ListMembersForLyris
				where list_ = @thisListName
				and updateStatus = 'added';

				insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				select getdate() as DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype as membertype_, @defaultSubType as subype_
				from MC_ListMembersForLyris lm
				where lm.list_ = @thisListName
				and lm.updateStatus = 'added';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Added new memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding old expired memberships to delete';
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					null as functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' as updateStatus, l.isAutoManageActive
				from #ListsForLyrisSync l
				inner join members_ m on l.autoID = @thisListAutoID
					and l.list_ = m.list_ collate Latin1_General_CI_AI
					and m.membertype_ = 'expired'
					and m.ExpireDate_ < @expireDateCutoff
					and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''

				set @lastrowcount = @@rowcount;

				if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
					delete m
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members deleted - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		select @thisListAutoID = min(autoID) from #ListsForLyrisSync where autoID > @thisListAutoID;
	END

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END


	-- send email if there are members with no email address
	IF EXISTS (select 1 from dbo.members_ where EmailAddr_ = '') BEGIN
		exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc='', @bcc='', @subject='PRODUCTION - Developer Needed - Lyris Members with blank email address', 
			@message='There are records in dbo.members_ where EmailAddr_ = ''''', 
			@priority='high',  @smtpserver='***********', @authUsername='',  @authPassword='';
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	set @escalateError = 1;
END CATCH

if len(rtrim(ltrim(@errorLog))) > 0 BEGIN
	set @errorLog = @errorLog + @crlf + @crlf + @crlf + isnull(@progressLog,'');
	set @emailSubject =  convert(varchar(19), getdate(), 121) + ' - MC ListSync Process: Errors Generated';

	exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject=@emailSubject, @message=@errorLog,  @priority='normal',  @smtpserver='***********', 
		@authUsername='',  @authPassword='';

	set @message =  convert(varchar(19), getdate(), 121) + ' : Sent Error Log Email';
		if @debugMode = 1 RAISERROR(@message,0,1);
END

if ( @escalateError = 1) BEGIN
	set @message =  convert(varchar(19), getdate(), 121) + ' : Escalating Fatal Error';
	if @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END

IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
	DROP TABLE #ListsForLyrisSync;
IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

RETURN 0;
GO