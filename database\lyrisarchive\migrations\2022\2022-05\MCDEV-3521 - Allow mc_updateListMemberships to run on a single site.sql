use trialslyris1;
GO

ALTER PROC dbo.mc_updateListMemberships
@debugMode BIT = 0,
@sitecode varchar(10) = NULL

AS

BEGIN TRY

	DECLARE @progressLog VARCHAR(max), @errorLog VARCHAR(max), @emailSubject VARCHAR(500), @emailTitle VARCHAR(300), @escalateError BIT,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno INT, @proc sysname, @lineno INT, 
		@defaultMembertype VARCHAR(100), @defaultSubType VARCHAR(100), @defaultMCOption_keepActive BIT, 
		@defaultMCOption_lockAddress BIT, @defaultMCOption_lockName BIT, @thisListName VARCHAR(100), @thisListAutoID INT,
		@thisListAutoManageActive BIT, @message VARCHAR(500), @lastrowcount INT, @thisListOneWayList BIT, @expireDateCutoff DATETIME,
		@runByMemberID INT;

	SET @escalateError = 0;
	SET @errorLog = '';
	SET @defaultMembertype = 'normal';
	SET @defaultSubType = 'mail';
	SET @defaultMCOption_keepActive = 0;
	SET @defaultMCOption_lockAddress = 0;
	SET @defaultMCOption_lockName = 0;
	SET @expireDateCutoff = DATEADD(year,-1,GETDATE());

	EXEC MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #ListsForLyrisSync (autoid INT IDENTITY(1,1), siteID INT, siteCode VARCHAR(10), orgID INT, orgCode VARCHAR(10), 
		list_ VARCHAR(100), isAutoManageActive BIT);
	CREATE TABLE #tmpLogMessages (autoid INT IDENTITY(1,1), siteID INT, memberID INT, listName VARCHAR(100), msg VARCHAR(500));

	IF @sitecode IS NOT NULL
	BEGIN
		delete from dbo.MC_ListMembersForLyris where sitecode = @sitecode;
	END ELSE BEGIN
		TRUNCATE TABLE dbo.MC_ListMembersForLyris;
	END

	INSERT INTO #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	SELECT siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	FROM membercentral.datatransfer.dbo.ListsForLyris
	where sitecode = isnull(@sitecode,sitecode)
	ORDER BY orgcode, list_;

	-- delete lists that no longer exist in Lyris
	DELETE s
	FROM #ListsForLyrisSync s
	LEFT OUTER JOIN lists_ l
		on s.list_ = l.name_ COLLATE Latin1_General_CI_AI
    WHERE l.ListID_ IS NULL


	INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	SELECT lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.MCMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, RIGHT(lmfl.emailaddr_,LEN(lmfl.emailaddr_)-CHARINDEX('@',lmfl.emailaddr_)), 
		LEFT(lmfl.emailaddr_,CHARINDEX('@',lmfl.emailaddr_)-1)
	FROM membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	INNER JOIN #ListsForLyrisSync lfl ON lfl.list_ = lmfl.list_;

	-- null blank emails
	UPDATE dbo.MC_ListMembersForLyris
	SET emailaddr_ = NULL
	WHERE ltrim(rtrim(ISNULL(emailaddr_,''))) = '' and sitecode = isnull(@sitecode,sitecode);
	
	-- loop list by list
	SELECT @thisListAutoID = min(autoID) FROM #ListsForLyrisSync;
	WHILE @thisListAutoID IS NOT NULL BEGIN
		SELECT @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		FROM #ListsForLyrisSync 
		WHERE autoID = @thisListAutoID;

		IF exists (SELECT adminSend_ FROM dbo.lists_ WHERE name_ = @thisListName AND adminSend_ = 'T')
			SET @thisListOneWayList = 1;
		ELSE
			SET @thisListOneWayList = 0;

		SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive AS VARCHAR(5));
		SET @progressLog = @progressLog + '<br/>' + @message;
			IF @debugMode = 1 RAISERROR(@message,0,1);

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding names to update';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET updateStatus = 'UpdateName'
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.fullname_ <> m.fullname_
				AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Name changed FROM ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				-- update the full names that have been marked
				UPDATE m 
				SET m.fullname_ = lm.fullname_
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated names - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail' ELSE 'UpdateEmail' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
			LEFT OUTER JOIN dbo.members_ existingAddresses ON lm.list_ = existingAddresses.list_
				AND (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ AND existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			WHERE existingAddresses.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				IF @thisListAutoManageActive = 1 BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET lm.updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail1' ELSE 'UpdateEmail1' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND updateStatus IS NULL 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ IS NOT NULL
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN dbo.members_ existingAddresses ON lm2.list_ = existingAddresses.list_
					AND (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ AND existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				WHERE lm2.list_ = @thisListName
				AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM MC_ListMembersForLyris lm
					INNER JOIN members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				IF @thisListAutoManageActive = 1
				BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m 
						on lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);
					
					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			SELECT l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				NULL AS functionName, 'expired' AS updateStatus, l.isAutoManageActive
			FROM #ListsForLyrisSync l
			INNER JOIN members_ m ON l.autoID = @thisListAutoID
				AND l.list_ = m.list_ COLLATE Latin1_General_CI_AI
				AND m.membertype_ in ('confirm','held','normal')
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
			LEFT OUTER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID COLLATE Latin1_General_CI_AI
				AND m.list_ = lm.list_ 
			WHERE lm.autoID IS NULL AND (m.MCOption_keepActive IS NULL or m.MCOption_keepActive=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				UPDATE m 
				SET m.membertype_ = 'expired',
					m.ExpireDate_ = GETDATE()
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'expired';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Expired members - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm 
			SET lm.updateStatus = 'reactivate'
			FROM members_ m
			INNER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
				AND m.list_ = lm.list_
				AND m.list_ = @thisListName
				AND m.membertype_ = 'expired'
				AND lm.functionName in ('managePopulation','manageStatus');

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				UPDATE m 
				SET m.membertype_ = 'normal',
					m.ExpireDate_ = NULL
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Reactivated memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET lm.updateStatus = 'added'
			FROM 
				(
					SELECT min(autoID) AS autoID FROM MC_ListMembersForLyris WHERE list_ = @thisListName AND updateStatus IS NULL AND functionName in ('managePopulation') GROUP BY emailaddr_
				) deduped
				INNER JOIN MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					AND NULLif(lm.emailaddr_,'') IS NOT NULL
				LEFT OUTER JOIN members_ m
					on lm.list_ = m.list_
					AND (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = RIGHT(lm.emailaddr_,LEN(lm.emailaddr_)-CHARINDEX('@',lm.emailaddr_)) AND m.usernamelc_ = LEFT(lm.emailaddr_,CHARINDEX('@',lm.emailaddr_)-1))
					)
			WHERE m.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				FROM MC_ListMembersForLyris
				WHERE list_ = @thisListName
				AND updateStatus = 'added';

				INSERT INTO members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				SELECT GETDATE() AS DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype AS membertype_, @defaultSubType AS subype_
				FROM MC_ListMembersForLyris lm
				WHERE lm.list_ = @thisListName
				AND lm.updateStatus = 'added';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Added new memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding old expired memberships to delete';
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);

				INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				SELECT l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					NULL AS functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' AS updateStatus, l.isAutoManageActive
				FROM #ListsForLyrisSync l
				INNER JOIN members_ m ON l.autoID = @thisListAutoID
					AND l.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND m.membertype_ = 'expired'
					AND m.ExpireDate_ < @expireDateCutoff
					AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''

				SET @lastrowcount = @@rowcount;

				IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
					DELETE m
					FROM MC_ListMembersForLyris lm
					INNER JOIN members_ m ON lm.list_ = m.list_
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID
						AND lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Expired members deleted - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		SELECT @thisListAutoID = min(autoID) FROM #ListsForLyrisSync WHERE autoID > @thisListAutoID;
	END

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID AS VARCHAR(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID AS VARCHAR(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID AS VARCHAR(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END


	-- send email if there are members with no email address
	IF EXISTS (SELECT 1 FROM dbo.members_ WHERE EmailAddr_ = '') BEGIN
		SET @emailSubject = 'Lyris Members with blank email address';
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailSubject, @messageContent='There are records in dbo.members_ WHERE EmailAddr_ is an empty string.', @forDev=1;
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	SET @escalateError = 1;
END CATCH

IF LEN(rtrim(ltrim(@errorLog))) > 0 BEGIN
	SET @errorLog = @errorLog + '<br/><br/>' + ISNULL(@progressLog,'');
	SET @emailSubject =  convert(varchar(19), GETDATE(), 121) + ' - MC ListSync Process: Errors Generated';
	SET @emailTitle =  'MC ListSync Process: Errors Generated';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailTitle, @messageContent=@errorLog, @forDev=1;

	SET @message =  convert(varchar(19), GETDATE(), 121) + ' : Sent Error Log Email';
		IF @debugMode = 1 RAISERROR(@message,0,1);
END

IF ( @escalateError = 1) BEGIN
	SET @message =  convert(varchar(19), GETDATE(), 121) + ' : Escalating Fatal Error';
	IF @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END

IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
	DROP TABLE #ListsForLyrisSync;
IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

RETURN 0;
GO