use lyrisarchive;
GO
ALTER PROC up_BackupLyrisMembers
AS

/* ********************************************* */
/* backup members_ table into lyrisMembersBackup */
/* ********************************************* */
declare @cmd varchar(6000);
declare @backupDate varchar(20);
select @backupDate = replace(replace(replace(convert(varchar(20),getdate(),120),'-',''),' ',''),':','');

-- bcp out data ~ 30 sec
set @cmd = 'bcp trialslyris1.dbo.members_ out c:\temp\members_' + @backupDate + '.txt -h TABLOCK -n -T -S' + CAST(serverproperty('servername') as varchar(40));
exec master..xp_cmdshell @cmd, NO_OUTPUT;

-- create table
set @cmd = 'SELECT * INTO lyrisMembersBackup.dbo.members_' + @backupDate + ' FROM trialslyris1.dbo.members_ WHERE 1=0';
EXEC(@cmd);

-- bcp in data ~ 30 sec
set @cmd = 'bcp lyrisMembersBackup.dbo.members_' + @backupDate + ' in c:\temp\members_' + @backupDate + '.txt -h TABLOCK -n -T -S' + CAST(serverproperty('servername') as varchar(40));
exec master..xp_cmdshell @cmd, NO_OUTPUT;

-- delete bcp file
set @cmd = 'del c:\temp\members_' + @backupDate + '.txt';
exec master..xp_cmdshell @cmd, NO_OUTPUT;



/* ******************** */
/* remove older backups */
/* ******************** */
declare @tblTables TABLE (tablename sysname);
declare @tablename sysname;
declare @oldestDate varchar(20);
select @oldestDate = replace(replace(replace(convert(varchar(20),dateadd(d,-30,getdate()),120),'-',''),' ',''),':','');

insert into @tblTables (tablename)
select o.name
from sys.objects as o
where o.type = 'U'
and left(o.name,10) = 'members_20'
and o.name < 'members_' + @oldestDate

select @tablename = min(tablename) from @tblTables
while @tablename is not null begin
	set @cmd = 'DROP TABLE lyrisMembersBackup.dbo.' + @tablename + ';'
	EXEC(@cmd)
	select @tablename = min(tablename) from @tblTables where tableName > @tablename 
end


/* *************************************************************************** */
/* remove older backups (old style -- can delete this when the table is empty) */
/* *************************************************************************** */
declare @30daysago datetime
set @30daysago = dateadd(d,-30,getdate())

delete from lyrisMembersBackup.dbo.members_DailyBackup
where backupdate < @30daysago

GO