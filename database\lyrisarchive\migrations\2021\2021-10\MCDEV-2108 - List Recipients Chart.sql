USE lyrisarchive
GO

ALTER PROC dbo.dash_lists_messagePerMonth
@siteid int,
@sitecode varchar(5),
@nummonths int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @reportDate date, @startRange date;

	-- start with the end of this month
	set @reportDate = EOMONTH(GETDATE());
	set @startRange = EOMONTH(DATEADD(MONTH,@nummonths*-1,@reportDate));

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (reportMonth varchar(10), reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (reportMonth varchar(10));
	
	with yearmonthrange(yearmonthinrange) as (
		select 0
			union all
		select yearmonthinrange+1 
		from yearmonthrange
		where yearmonthinrange < @nummonths-1
	)
	insert into #tmpListCountsExpectedRows (reportmonth)
	select 
		  format(dateadd(month,-yearmonthinrange,getdate()),'yyyy-MM') as reportmonth
	from yearmonthrange

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF @@ROWCOUNT = 0
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (reportMonth, reportCount)
	select format(creatStamp_,'yyyy-MM') as reportMonth, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID
		and format(m.creatStamp_,'yyyy-MM') > format(@startRange,'yyyy-MM')
	group by format(creatStamp_,'yyyy-MM');

	-- fill in missing expected rows
	insert into #tmpListCounts (reportMonth)
	select reportMonth
	from #tmpListCountsExpectedRows
		except
	select reportMonth
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select reportmonth as reportmonth, reportcount
		from #tmpListCounts as datarow
		order by reportmonth
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO