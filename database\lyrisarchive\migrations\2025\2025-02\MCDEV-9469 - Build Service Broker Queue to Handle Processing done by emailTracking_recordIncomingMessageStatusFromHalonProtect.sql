USE EmailTracking;
GO

-- CREATE SERVICE BROKER QUEUE AND SERVICE
CREATE QUEUE HalonProtectMsgStatusRecorderInitiatorQueue;
GO
CREATE QUEUE HalonProtectMsgStatusRecorderTargetQueue;
GO

CREATE SERVICE HalonProtectMsgStatusRecorderInitiatorService ON QUEUE HalonProtectMsgStatusRecorderInitiatorQueue;
GO

CREATE SERVICE HalonProtectMsgStatusRecorderTargetService ON QUEUE HalonProtectMsgStatusRecorderTargetQueue ([EmailTracking/GeneralXMLEOSContract]);
GO

CREATE PROCEDURE HalonProtectMsgStatusRecorderInitiatorQueue_activated
AS

BEGIN

	DECLARE @handle UNIQUEIDENTIFIER;
	DECLARE @message_type SYSNAME;

	BEGIN TRANSACTION;
		WAITFOR (
			RECEIVE TOP(1) @handle = [conversation_handle], @message_type = [message_type_name]
			FROM HalonProtectMsgStatusRecorderInitiatorQueue
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 1 BEGIN
			-- Expect target response to EndOfStream message.
			IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @handle;
			END
		END
	COMMIT TRAN;

END
GO

CREATE PROCEDURE dbo.HalonProtectMsgStatusRecorderTargetQueue_activated
AS

SET NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @receive_table TABLE (queuing_order BIGINT, [conversation_handle] UNIQUEIDENTIFIER, message_type_name SYSNAME, message_body VARBINARY(MAX));
DECLARE message_cursor CURSOR LOCAL FORWARD_ONLY READ_ONLY 
	FOR SELECT [conversation_handle], message_type_name, message_body
	FROM @receive_table
	ORDER BY queuing_order;
DECLARE @conversation_handle UNIQUEIDENTIFIER, @message_type SYSNAME, @message_body VARBINARY(max), @xmldata xml,
	@error_number INT, @error_message VARCHAR(4000), @error_severity INT, @error_state INT, @error_procedure SYSNAME,
	@error_line INT, @error_dialog VARCHAR(50),
	@hdrMessageid varchar(1000), @sesID varchar(150), @stepFunctionName varchar(250), @dateCreated datetime,
	@subject varchar(500), @fromAddress varchar(320), @area1Disposition varchar(50), @mcDisposition varchar(50),
	@status varchar(50), @reason varchar(3000), @sentTimestamp bigint, @incomingMessageID int;

BEGIN TRY
WHILE (1 = 1) BEGIN
	BEGIN TRANSACTION;

		-- Receive all available messages into the table.
		-- Wait 5 seconds for messages.
		WAITFOR (
			RECEIVE [queuing_order], [conversation_handle], [message_type_name], [message_body]
			FROM HalonProtectMsgStatusRecorderTargetQueue
			INTO @receive_table
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 0 BEGIN
			COMMIT;
			BREAK;
		END ELSE BEGIN
			OPEN message_cursor;
			WHILE (1=1) BEGIN
				FETCH NEXT FROM message_cursor INTO @conversation_handle, @message_type, @message_body;
				IF (@@FETCH_STATUS != 0) BREAK;

				BEGIN TRY
					IF @message_type = 'EmailTracking/GeneralXMLRequest' BEGIN
						-- process the msg.
						SET @xmldata = cast(@message_body as xml);

						SELECT
							@hdrMessageid = @xmldata.value('(/t/msgid)[1]','varchar(1000)'),
							@sesID = @xmldata.value('(/t/sesid)[1]','varchar(150)'),
							@stepFunctionName = @xmldata.value('(/t/sfn)[1]','varchar(250)'),
							@dateCreated = @xmldata.value('(/t/dt)[1]','datetime'),
							@subject = @xmldata.value('(/t/sub)[1]','varchar(500)'),
							@fromAddress = isnull(@xmldata.value('(/t/fa)[1]','varchar(320)'),''),
							@area1Disposition = @xmldata.value('(/t/ad)[1]','varchar(50)'),
							@mcDisposition = @xmldata.value('(/t/md)[1]','varchar(50)'),
							@status = @xmldata.value('(/t/st)[1]','varchar(50)'),
							@reason = @xmldata.value('(/t/r)[1]','varchar(3000)'),
							@sentTimestamp = @xmldata.value('(/t/sts)[1]','bigint');

						IF @hdrMessageid is not null
							EXEC dbo.emailTracking_recordIncomingMessageStatusFromHalonProtect
								@hdrMessageid=@hdrMessageid,
								@sesID=@sesID,
								@stepFunctionName=@stepFunctionName,
								@dateCreated=@dateCreated,
								@subject=@subject,
								@fromAddress=@fromAddress,
								@area1Disposition=@area1Disposition,
								@mcDisposition=@mcDisposition,
								@status=@status,
								@reason=@reason,
								@sentTimestamp=@sentTimestamp,
								@incomingMessageID=@incomingMessageID OUTPUT;
					END
					IF @message_type = 'EmailTracking/EndOfStream' BEGIN
						-- initiator is signaling end of message stream: end the dialog
						END CONVERSATION @conversation_handle;
					END
					IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
						WITH XMLNAMESPACES ('https://schemas.microsoft.com/SQL/ServiceBroker/Error' AS ssb)
						SELECT @error_number = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Code)[1]', 'INT'),
							@error_message = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Description)[1]', 'VARCHAR(4000)');
						SET @error_dialog = CAST(@conversation_handle AS VARCHAR(50));

						RAISERROR('Error in dialog %s: %s (%i)', 16, 1, @error_dialog, @error_message, @error_number);
						END CONVERSATION @conversation_handle;
					END
				END TRY
				BEGIN CATCH
					SET @error_message = ERROR_MESSAGE();
					IF XACT_STATE() = -1 BEGIN
						-- The transaction is doomed. Only rollback possible.
						-- This could disable the queue if done 5 times consecutively!
						ROLLBACK TRANSACTION;

						-- Record the error.
						BEGIN TRAN;
							INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
							VALUES(@error_message, @message_body);
						COMMIT TRAN;

						-- For this level of error, it is best to exit the proc and give the queue monitor control.
						-- Breaking to the outer catch will accomplish this.
						RAISERROR ('Message processing error', 16, 1);
					END
					ELSE IF XACT_STATE() = 1 BEGIN
						-- Record error and continue processing messages.
						-- Failing message could also be put aside for later processing here.
						-- Otherwise it will be discarded.
						INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
						VALUES(@error_message, @message_body);
					END
				END CATCH
			END

			CLOSE message_cursor;
			DELETE FROM @receive_table;
		END

	COMMIT TRAN;
END
END TRY
BEGIN CATCH
	-- Process the error and exit the proc to give the queue monitor control
	SET @error_message = ERROR_MESSAGE();

	IF XACT_STATE() = -1 BEGIN
		-- The transaction is doomed. Only rollback possible.
		-- This could disable the queue if done 5 times consecutively!
		ROLLBACK TRANSACTION;

		-- Record the error.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
	ELSE IF XACT_STATE() = 1 BEGIN
		-- Record error and commit transaction.
		-- Here you could also save anything else you want before exiting.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
END CATCH
GO

-- modify queue to set activation proc
ALTER QUEUE HalonProtectMsgStatusRecorderInitiatorQueue WITH
	  STATUS = ON,
	  ACTIVATION (STATUS = ON,
				  PROCEDURE_NAME = dbo.HalonProtectMsgStatusRecorderInitiatorQueue_activated,
				  MAX_QUEUE_READERS = 1,
				  EXECUTE AS OWNER)
GO

ALTER QUEUE HalonProtectMsgStatusRecorderTargetQueue WITH
	  STATUS = ON,
	  ACTIVATION (STATUS = ON,
				  PROCEDURE_NAME = dbo.HalonProtectMsgStatusRecorderTargetQueue_activated,
				  MAX_QUEUE_READERS = 1,
				  EXECUTE AS OWNER)
GO

CREATE PROC dbo.HalonProtectMsgStatusRecorder_sendMessage
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	EXEC dbo.sb_dialogpool_sendMessage N'HalonProtectMsgStatusRecorderInitiatorService', N'HalonProtectMsgStatusRecorderTargetService',
		N'EmailTracking/GeneralXMLEOSContract', N'EmailTracking/GeneralXMLRequest', @messageBody;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO