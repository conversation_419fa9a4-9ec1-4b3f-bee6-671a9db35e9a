use trialslyris1;
GO
ALTER PROC dbo.[swl_updateMarketingList-Natle]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.100.1.86'
IF @@SERVERNAME = 'MCDEV01\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100);
DECLARE @listName varchar(100), @now datetime;

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

create TABLE #swl_eligibleForNatleMarketingList (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(100), 
	email varchar(100),
	usernameLC_ varchar(100),
	domain_ varchar(250)
);

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

exec membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList;

if not exists (select * from membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList ended with no rows in table  membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO
ALTER PROC [dbo].[job_updateList-natle_justiceServices]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.100.1.86'
IF @@SERVERNAME = 'MCDEV01\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @listName varchar(100), @now datetime

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers

create TABLE #tempListMembers (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(500), 
	email varchar(200),
	usernameLC_ varchar(100),
	domain_ varchar(150)
)


set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

exec membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList

if not exists (select * from membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList ended with no rows in table  membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList



	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email))


	-- update email addresses/fullname based on matching membernumber and association
	update m set
		domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers tmp
	inner join members_ m
		on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
	left outer join members_ prexistingEmail
		on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null

	-- mark email addresses that are NOT in temp table as expired
	update m set
		membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
		and tmp.email is null
		and m.membertype_ in ('normal','held')
		and m.association_ not in ('CT')

	-- reactivate previously expired email addresses that are in temp table
	update m set
		membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired'
		and m.association_ not in ('CT')

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from dbo.members_
		where list_ = @listname 
	) 

	insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now,domain_,email,fullname,@listName ,usernameLc_ ,memberNumber as ExternalMemberID,orgcode
	from #tempListMembers
	where orgcode not in ('CT')
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers
GO