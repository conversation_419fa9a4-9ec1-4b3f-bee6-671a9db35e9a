use EmailTracking
GO

update EmailTracking.dbo.deliveryStatuses set 
    statusCode = 'bounce-invalid'
where statuscode = 'bounced-invalid'

update EmailTracking.dbo.deliveryStatuses set 
    statusCode = 'bounce-mailboxUnavailable'
where statuscode = 'bounced-mailboxUnavailable'

update EmailTracking.dbo.deliveryStatuses set 
    statusCode = 'bounce-technical'
where statuscode = 'bounced-technical'

update EmailTracking.dbo.deliveryStatuses set 
    statusCode = 'bounce-unclassified'
where statuscode = 'bounced-unclassified'

GO

ALTER PROC dbo.SendGridStatusTrackingTargetQueue_Activated
AS

SET NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @receive_table TABLE (queuing_order BIGINT, [conversation_handle] UNIQUEIDENTIFIER, message_type_name SYSNAME, message_body VARBINARY(MAX));
DECLARE message_cursor CURSOR LOCAL FORWARD_ONLY READ_ONLY 
	FOR SELECT [conversation_handle], message_type_name, message_body 
	FROM @receive_table 
	ORDER BY queuing_order;
DECLARE @conversation_handle UNIQUEIDENTIFIER, @message_type SYSNAME, @message_body VARBINARY(max), @xmldata xml, 
	@error_number INT, @error_message VARCHAR(4000), @error_severity INT, @error_state INT, @error_procedure SYSNAME, 
	@error_line INT, @error_dialog VARCHAR(50), @messagetype varchar(50), @periodcode int, @applicationtype varchar(20), 
	@applicationname varchar(100), @event varchar(50), @sgmessageid varchar(100), @sgeventid varchar(100), @timestamp datetime, 
	@membernumber varchar(100), @sendingapplicationid int, @sendingapplicationmessageid int, @sendingapplicationrecipientid int, 
	@siteid int, @emailusername varchar(100), @emailDomainID int, @emaildomain varchar(250), @deliverystatusid int, @attempt int, 
	@ippoolid int, @sendgridsubuserid int, @sendgridsubuserdomainid int, @messagetemplate varchar(1000), @messagesha1 varchar(40), 
	@messagetemplatematchfound bit, @useragent varchar(500), @useragentid int, @useragentSHA1 varchar(40), @ipaddress varchar(50), 
	@ismachineopen bit, @url varchar(1000), @urlDomain varchar(500), @smtpstatuscode varchar(25);

BEGIN TRY
WHILE (1 = 1) BEGIN
	BEGIN TRANSACTION;

		-- Receive all available messages into the table.
		-- Wait 5 seconds for messages.
		WAITFOR (
			RECEIVE [queuing_order], [conversation_handle], [message_type_name], [message_body]
			FROM SendGridStatusTrackingTargetQueue
			INTO @receive_table
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 0 BEGIN
			COMMIT;
			BREAK;
		END ELSE BEGIN
			OPEN message_cursor;
			WHILE (1=1) BEGIN
				FETCH NEXT FROM message_cursor INTO @conversation_handle, @message_type, @message_body; 
				IF (@@FETCH_STATUS != 0) BREAK;

				BEGIN TRY
					IF @message_type = 'EmailTracking/GeneralXMLRequest' BEGIN
						-- process the msg.
						SET @xmldata = cast(@message_body as xml);

						-- get data common to both delivery and engagement events
						select @messagetype = @xmldata.value('(/t/messagetype)[1]','varchar(50)'),
							@applicationtype = @xmldata.value('(/t/applicationtype)[1]','varchar(20)'),
							@applicationname = @xmldata.value('(/t/applicationname)[1]','varchar(100)'),
							@periodcode = @xmldata.value('(/t/periodcode)[1]','int'),
							@event = @xmldata.value('(/t/event)[1]','varchar(50)'),
							@sgmessageid = @xmldata.value('(/t/sgmessageid)[1]','varchar(100)'),
							@sgeventid = @xmldata.value('(/t/sgeventid)[1]','varchar(100)'),
							@timestamp = @xmldata.value('(/t/timestamp)[1]','datetime'),
							@membernumber = @xmldata.value('(/t/membernumber)[1]','varchar(100)'),
							@sendingapplicationid = NULLIF(@xmldata.value('(/t/sendingapplicationid)[1]','int'),0),
							@sendingapplicationmessageid = @xmldata.value('(/t/sendingapplicationmessageid)[1]','int'),
							@sendingapplicationrecipientid = @xmldata.value('(/t/sendingapplicationrecipientid)[1]','int'),
							@siteid = @xmldata.value('(/t/siteid)[1]','int'),
							@emailusername = @xmldata.value('(/t/emailusername)[1]','varchar(100)'),
							@emaildomain = @xmldata.value('(/t/emaildomain)[1]','varchar(250)'),
							@emailDomainID = @xmldata.value('(/t/emaildomainid)[1]','int');

						IF @messagetype = 'delivery' BEGIN
							select @deliverystatusid = @xmldata.value('(/t/deliverystatusid)[1]','int'),
								@attempt = @xmldata.value('(/t/attempt)[1]','int'),
								@ippoolid = @xmldata.value('(/t/ippoolid)[1]','int'),
								@sendgridsubuserid = @xmldata.value('(/t/sendgridsubuserid)[1]','int'),
								@sendgridsubuserdomainid = @xmldata.value('(/t/sendgridsubuserdomainid)[1]','int'),
								@messagetemplate = @xmldata.value('(/t/messagetemplate)[1]','varchar(1000)'),
								@messagesha1 = NULLIF(@xmldata.value('(/t/messagesha1)[1]','varchar(40)'),''),
								@messagetemplatematchfound = @xmldata.value('(/t/messagetemplatematchfound)[1]','bit'),
								@smtpstatuscode = @xmldata.value('(/t/smtpstatuscode)[1]','varchar(25)');

							EXEC dbo.emailTracking_recordSendGridDeliveryEvent @messagetype=@messagetype, @applicationtype=@applicationtype,
								@applicationname=@applicationname, @periodCode=@periodCode, @event=@event, @sgmessageid=@sgmessageid,
								@sgeventid=@sgeventid, @timestamp=@timestamp, @membernumber=@membernumber, @sendingapplicationid=@sendingapplicationid,
								@sendingapplicationmessageid=@sendingapplicationmessageid, @sendingapplicationrecipientid=@sendingapplicationrecipientid,
								@siteid=@siteid, @emailusername=@emailusername, @emailDomainID=@emailDomainID, @emaildomain=@emaildomain,
								@deliverystatusid=@deliverystatusid, @attempt=@attempt, @ippoolid=@ippoolid, 
								@sendgridsubuserid=@sendgridsubuserid, @sendgridsubuserdomainid=@sendgridsubuserdomainid,
								@messagetemplate=@messagetemplate, @messagesha1=@messagesha1, @smtpstatuscode=@smtpstatuscode,
								@messagetemplatematchfound=@messagetemplatematchfound;
						END

						IF @messagetype = 'engagement' BEGIN
							select @useragent = @xmldata.value('(/t/useragent)[1]','varchar(500)'),
								@useragentid = NULLIF(@xmldata.value('(/t/useragentid)[1]','int'),0),
								@useragentSHA1 = NULLIF(@xmldata.value('(/t/useragentSHA1)[1]','varchar(40)'),''),
								@ipaddress = @xmldata.value('(/t/ipaddress)[1]','varchar(50)'),
								@ismachineopen = @xmldata.value('(/t/ismachineopen)[1]','bit'),
								@url = @xmldata.value('(/t/url)[1]','varchar(1000)'),
								@urldomain = @xmldata.value('(/t/urldomain)[1]','varchar(500)');

							EXEC dbo.emailTracking_recordSendGridEngagementEvent @messagetype=@messagetype, @applicationtype=@applicationtype,
								@applicationname=@applicationname, @periodCode=@periodCode, @event=@event, @sgmessageid=@sgmessageid,
								@sgeventid=@sgeventid, @timestamp=@timestamp, @membernumber=@membernumber, 
								@sendingapplicationid=@sendingapplicationid, @sendingapplicationmessageid=@sendingapplicationmessageid,
								@sendingapplicationrecipientid=@sendingapplicationrecipientid, @siteid=@siteid, @emailusername=@emailusername,
								@emailDomainID=@emailDomainID, @emaildomain=@emaildomain, @useragent=@useragent, @useragentid=@useragentid,
								@useragentSHA1=@useragentSHA1, @ipaddress=@ipaddress, @ismachineopen=@ismachineopen, @url=@url,
								@urlDomain=@urlDomain;
						END

					END
					IF @message_type = 'EmailTracking/EndOfStream' BEGIN
						-- initiator is signaling end of message stream: end the dialog
						END CONVERSATION @conversation_handle;
					END
					IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
						WITH XMLNAMESPACES ('https://schemas.microsoft.com/SQL/ServiceBroker/Error' AS ssb)
						SELECT @error_number = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Code)[1]', 'INT'),
							@error_message = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Description)[1]', 'VARCHAR(4000)');
						SET @error_dialog = CAST(@conversation_handle AS VARCHAR(50));

						RAISERROR('Error in dialog %s: %s (%i)', 16, 1, @error_dialog, @error_message, @error_number);
						END CONVERSATION @conversation_handle;
					END
				END TRY
				BEGIN CATCH
					SET @error_message = ERROR_MESSAGE();
					IF XACT_STATE() = -1 BEGIN
						-- The transaction is doomed. Only rollback possible.
						-- This could disable the queue if done 5 times consecutively!
						ROLLBACK TRANSACTION;

						-- Record the error.
						BEGIN TRAN;
							INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
							VALUES(@error_message, @message_body);
						COMMIT TRAN;

						-- For this level of error, it is best to exit the proc and give the queue monitor control.
						-- Breaking to the outer catch will accomplish this.
						RAISERROR ('Message processing error', 16, 1);
					END
					ELSE IF XACT_STATE() = 1 BEGIN
						-- Record error and continue processing messages.
						-- Failing message could also be put aside for later processing here.
						-- Otherwise it will be discarded.
						INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
						VALUES(@error_message, @message_body);
					END
				END CATCH
			END

			CLOSE message_cursor;
			DELETE FROM @receive_table;
		END

	COMMIT TRAN;
END
END TRY
BEGIN CATCH
	-- Process the error and exit the proc to give the queue monitor control
	SET @error_message = ERROR_MESSAGE();

	IF XACT_STATE() = -1 BEGIN
		-- The transaction is doomed. Only rollback possible.
		-- This could disable the queue if done 5 times consecutively!
		ROLLBACK TRANSACTION;

		-- Record the error.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
	ELSE IF XACT_STATE() = 1 BEGIN
		-- Record error and commit transaction.
		-- Here you could also save anything else you want before exiting.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
END CATCH
GO

ALTER PROC dbo.emailTracking_recordSendGridDeliveryEvent
@messagetype varchar(50),
@applicationtype varchar(20),
@applicationname varchar(100),
@periodCode int,
@event varchar(50),
@sgmessageid varchar(100),
@sgeventid varchar(100),
@timestamp datetime,
@membernumber varchar(100),
@sendingapplicationid int,
@sendingapplicationmessageid int,
@sendingapplicationrecipientid int,
@siteid int,
@emailusername varchar(100),
@emailDomainID int,
@emaildomain varchar(250),
@deliverystatusid int,
@attempt int,
@ippoolid int,
@sendgridsubuserid int,
@sendgridsubuserdomainid int,
@messagetemplate varchar(1000),
@messagesha1 varchar(40),
@smtpstatuscode varchar(25),
@messagetemplatematchfound bit

AS

SET XACT_ABORT, NOCOUNT ON;
declare 
	@eventadded bit = 0,
	@statusprocessed int,
	@statusdropped int,
	@statusdeferred int,
	@statusdelivered int;

select @statusprocessed = deliveryStatusID from dbo.deliveryStatuses where statusCode='processed';
select @statusdropped = deliveryStatusID from dbo.deliveryStatuses where statusCode='dropped';
select @statusdeferred = deliveryStatusID from dbo.deliveryStatuses where statusCode='deferred';
select @statusdelivered = deliveryStatusID from dbo.deliveryStatuses where statusCode='delivered';



-- get sendingapplicationid if NULL was passed in 
IF NULLIF(@sendingapplicationid,0) IS NULL
	EXEC dbo.emailTracking_createApplicationID @applicationName=@applicationName, @applicationType=@applicationType, @sendingApplicationID=@sendingapplicationid OUTPUT;

IF NULLIF(@emailDomainID,0) IS NULL
	EXEC dbo.emailTracking_createDomainID @domain=@emaildomain, @domainID=@emailDomainID OUTPUT;

IF NULLIF(@messagesha1,'') IS NOT NULL and @messagetemplatematchfound=0 and len(@messagetemplate) > 0
	EXEC dbo.emailTracking_createDeliveryMessageTemplate @statusMessageTemplate=@messagetemplate, @messageTemplateSHA1=@messagesha1;

-- create messageStats entry if it doesn't exist already
IF NOT EXISTS (
	select 1
	from dbo.messageStats
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid
) BEGIN
	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	insert into dbo.messageStats (sendingApplicationID, sendingApplicationMessageID, siteID, ipPoolID, sendgrid_subuserID,
		sendgrid_subuserDomainID, periodCode, uniqueOpens, uniqueClicks, totalOpens, totalClicks, totalSpamReports)
	select @sendingApplicationID, @sendingApplicationMessageID, @siteID, @ipPoolID, @sendgridsubuserid, @sendgridsubuserdomainid,
		@periodCode, 0, 0, 0, 0, 0
	WHERE NOT EXISTS (
		select 1
		from dbo.messageStats
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid
	);

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
END

-- create recipient entry if it doesn't exist already
IF NOT EXISTS (
	select 1
	from dbo.recipients 
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
) BEGIN
	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	insert into dbo.recipients (sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID, siteID,
		username, domainID, DeliveryStatusID, sendingApplicationRecipientID, deliveryAttempts)
	select @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
		@emailDomainID, @deliverystatusid, @sendingapplicationrecipientid, 0
	WHERE NOT EXISTS (
		select 1
		from dbo.recipients 
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_message_id = @sgmessageid
	);

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
END

-- acknowledge receipt by Sendgrid
-- status not being set either because either it was already set by the insert above or by another message
-- since they are processed in parallel, a delivered or deferred event may have already been processed
-- if this recipient was dropped, then there would be no other event and the insert above would have taken care of it
IF @event = 'processed' or @event = 'dropped'
	update recipients set 
		dateacknowledged = @timestamp,
		deliverySeconds = case when deliveryStatusID = @statusdelivered then datediff(second,@timestamp, dateLastAttempted) else deliverySeconds end
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

IF @event = 'delivered'
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryAttempts = deliveryAttempts+1,
		deliveryStatusID = @deliverystatusid,
		deliverySeconds =  datediff(second,dateAcknowledged, @timestamp)
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

IF @event = 'deferred' or @event = 'dropped' or @event = 'bounce' BEGIN
	-- create messageStats entry if it doesn't exist already
	IF NOT EXISTS (
		select 1
		from dbo.recipientDeliveryMessages
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientDeliveryMessages (sg_event_id, sg_message_id, memberNumber, sendingApplicationID,
			sendingApplicationMessageID, siteID, username, domainID, periodCode, dateReceived, smtpStatusCode,
			MessageTemplateSHA1, hasBeenAggregated, deliveryStatusID)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, 
			@emailusername, @emailDomainID, @periodCode, @timestamp, @smtpstatuscode, @messagesha1, 0, @deliverystatusid
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientDeliveryMessages
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	END
END

IF @event = 'deferred' and @eventadded = 1
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryAttempts = @attempt,
		deliveryStatusID = @deliverystatusid
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
	and deliveryAttempts < @attempt
	and DeliveryStatusID <> @statusdelivered;

IF @event = 'bounce' and @eventadded = 1
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryStatusID = @deliverystatusid
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

RETURN 0;
GO

ALTER PROC dbo.emailTracking_recordSendGridEngagementEvent
@messagetype varchar(50),
@applicationtype varchar(20),
@applicationname varchar(100),
@periodCode int,
@event varchar(50),
@sgmessageid varchar(100),
@sgeventid varchar(100),
@timestamp datetime,
@membernumber varchar(100),
@sendingapplicationid int,
@sendingapplicationmessageid int,
@sendingapplicationrecipientid int,
@siteid int,
@emailusername varchar(100),
@emailDomainID int,
@emaildomain varchar(250),
@useragent varchar(500),
@useragentid int,
@useragentSHA1 varchar(40),
@ipaddress varchar(50),
@ismachineopen bit,
@url varchar(1000),
@urlDomain varchar(500)

AS

SET XACT_ABORT, NOCOUNT ON;

declare @eventadded bit = 0;


IF NULLIF(@sendingapplicationid,0) IS NULL
	EXEC dbo.emailTracking_createApplicationID @applicationName=@applicationName, @applicationType=@applicationType, @sendingApplicationID=@sendingapplicationid OUTPUT;

IF NULLIF(@emailDomainID,0) IS NULL
	EXEC dbo.emailTracking_createDomainID @domain=@emaildomain, @domainID=@emailDomainID OUTPUT;

IF NULLIF(@useragentid,0) IS NULL
	EXEC dbo.emailTracking_createUseragentID @userAgentString=@useragent, @useragentSHA1=@useragentSHA1, @useragentid=@useragentid OUTPUT;

IF @event = 'open' BEGIN
	IF NOT EXISTS (
		select 1
		from dbo.recipientOpens
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientOpens (sg_event_id, sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID,
			siteID, username, domainID, periodCode, dateReceived, useragentID, ipaddress, isMachineOpen, hasBeenAggregated)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
			@emailDomainID, @periodCode, @timestamp, @useragentID, @ipaddress, @isMachineOpen, 0
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientOpens
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @eventadded = 1
			update dbo.recipients 
			set firstOpened = @timestamp
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_message_id = @sgmessageid
			and (firstOpened IS NULL or firstOpened > @timestamp);
	END
END

IF @event = 'click' BEGIN
	IF NOT EXISTS (
		select 1
		from dbo.recipientClicks
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientClicks (sg_event_id, sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID,
			siteID, username, domainID, periodCode, dateReceived, useragentID, ipaddress, clickDomain, clickURL, hasBeenAggregated)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
			@emailDomainID, @periodCode, @timestamp, @useragentID, @ipaddress, @urlDomain, @url, 0
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientClicks
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @eventadded = 1
			update dbo.recipients 
			set firstClicked = @timestamp
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_message_id = @sgmessageid
			and (firstClicked IS NULL or firstClicked > @timestamp);
	END
END

IF @event = 'spamreport'
	update dbo.recipients 
	set firstReportedSpam = @timestamp
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
	and (firstReportedSpam IS NULL or firstReportedSpam > @timestamp);

RETURN 0;
GO
