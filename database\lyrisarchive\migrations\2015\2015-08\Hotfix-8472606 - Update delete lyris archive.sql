use lyrisArchive
GO

ALTER PROC dbo.lyris_removeArchive
@listname varchar(100),
@filelocation varchar(200)

AS

SET NOCOUNT ON

set @listname = lower(@listname)

-- list must be removed from lyris already
IF EXISTS (select name_ from LYRIS.trialslyris1.dbo.lists_ where name_ = @listname)
	RETURN 0


-- queue deletion in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3
CREATE TABLE #tmpS3 (objectKey varchar(200))

DECLARE @sql varchar(max)
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')'
EXEC(@sql)

INSERT INTO membercentral.platformQueue.dbo.queue_S3Delete (s3bucketName, objectKey)
SELECT 'messages.membercentral.com', objectKey
FROM #tmpS3

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3


-- delete from lyris, membercentral, and lyrisarchive
delete from LYRIS.trialslyris1.dbo.lists_format
where name = @listname

declare @SRID int
select @SRID = siteResourceID from membercentral.membercentral.dbo.lists_lists where listname = @listname
IF @SRID is not null	
	EXEC membercentral.membercentral.dbo.cms_deleteSiteResourceAndChildren @SRID

delete from membercentral.membercentral.dbo.comm_lists
where listID in (select listID from membercentral.membercentral.dbo.lists_lists where listname = @listname)

delete from membercentral.membercentral.dbo.lists_lists
where listname = @listname

declare @listID int
select @listID = listid from lyrisarchive.dbo.messageLists where list = @listname
IF @listID is not null
	delete from lyrisarchive.dbo.messages_
	where listid = @listID

delete from lyrisarchive.dbo.messageLists 
where list = @listname


SET NOCOUNT OFF

RETURN 0
GO