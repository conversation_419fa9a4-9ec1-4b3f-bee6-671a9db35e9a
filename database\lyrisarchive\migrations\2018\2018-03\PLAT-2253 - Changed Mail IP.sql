use trialslyris1
GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

set nocount on

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10),depomemberdataid int)
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int)
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int)
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100))


DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @currentListName varchar(100)

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END


set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'

exec TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers


if not exists (select emailaddr_ from TLASITES.transfer.dbo.trialsmithMarketingListPopulation)
BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table TLASITES.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE
BEGIN

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ = 'unsub'


    insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
    from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null


    --delete subscribed members with email addresses that are no longer in the pool

    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from members_ m
    left outer join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') and pool.poolid is null and m.membertype_ <> 'unsub'

    delete m
    from members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_


    -- update

    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from members_ m WITH(NOLOCK)
    inner join #memberPool pool
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
				or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub'


    update m set 
	    DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_,
		depomemberdataid = pool.depomemberdataid
    from members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool
	    on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') and m.membertype_ <> 'unsub'


    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join members_ m WITH(NOLOCK)
	    on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers')

    -- insert new memberships
    insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid
    from #memberPool


	--update trialsmithUsage
	truncate table dbo.tsdata
	insert into dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, expList, numBadSrc, subType, expires)
	select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
	from TLASITES.transfer.dbo.trialsmithMarketingListPopulation lp
	where depomemberdataid is not null

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers'
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs

set nocount off

GO

ALTER PROC dbo.[swl_updateMarketingList-Natle]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100);
DECLARE @listName varchar(100), @now datetime;

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

create TABLE #swl_eligibleForNatleMarketingList (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(100), 
	email varchar(100),
	usernameLC_ varchar(100),
	domain_ varchar(250)
);

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

exec membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList;

if not exists (select * from tlasites.dataTransfer.dbo.swl_eligibleForNatleMarketingList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.swl_getEligibleForNatleMarketingList ended with no rows in table  membercentral.dataTransfer.dbo.swl_eligibleForNatleMarketingList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.swl_eligibleForNatleMarketingList;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

ALTER PROC dbo.mc_updateListMemberships
@debugMode bit = 0

AS

BEGIN TRY

	declare @progressLog varchar(max), @errorLog varchar(max), @CRLF varchar(2), @emailSubject varchar(500), @escalateError bit,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int, 
		@defaultMembertype varchar(100), @defaultSubType varchar(100), @defaultMCOption_keepActive bit, 
		@defaultMCOption_lockAddress bit, @thisListName varchar(100), @thisListAutoID int, @thisListAutoManageActive bit,
		@message varchar(500);
	DECLARE @updatedMemberships TABLE (id int identity(1,1), DateJoined_ datetime, domain_ varchar(250), emailaddr_ varchar(100),
		fullname_ varchar(100), list_ varchar(60), usernameLc_ varchar(100), ExternalMemberID varchar(100));

	set @crlf = char(13) + char(10);
	set @escalateError = 0;
	set @errorLog = '';
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	set @defaultMCOption_keepActive = 0;
	set @defaultMCOption_lockAddress = 0;

	exec MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	CREATE TABLE #ListsForLyrisSync (autoid int IDENTITY(1,1), siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
		list_ varchar(100), isAutoManageActive bit);

	truncate table dbo.MC_ListMembersForLyris;

	insert into #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	from membercentral.datatransfer.dbo.ListsForLyris
	order by orgcode, list_;

	insert into MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	select lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, right(lmfl.emailaddr_,len(lmfl.emailaddr_)-charindex('@',lmfl.emailaddr_)), 
		left(lmfl.emailaddr_,charindex('@',lmfl.emailaddr_)-1)
	from membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	inner join #ListsForLyrisSync lfl on lfl.list_ = lmfl.list_;

	-- null blank emails
	update MC_ListMembersForLyris
	set emailaddr_ = null
	where ltrim(rtrim(isnull(emailaddr_,''))) = '';

	-- null blank externalMemberIDs
	update MC_ListMembersForLyris
	set externalMemberID = null
	where ltrim(rtrim(isnull(externalMemberID,''))) = '';

	select @thisListAutoID = min(autoID) from #ListsForLyrisSync;
	while @thisListAutoID is not null BEGIN
		select @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		from #ListsForLyrisSync 
		where autoID = @thisListAutoID;

		set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive as varchar(5));
		set @progressLog = @progressLog + @crlf + @message;
			if @debugMode = 1 RAISERROR(@message,0,1);

		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = 'update'
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and (lm.emailaddr_ <> m.emailaddr_ or lm.fullname_ <> m.fullname_)
				and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0
			left outer join dbo.members_ existingAddresses on lm.list_ = existingAddresses.list_
				and (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			where existingAddresses.memberID_ is null;

			update lm2 
			set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
			from dbo.MC_ListMembersForLyris lm2
			inner join (
				select lm.emailaddr_, lm.list_, lm.externalMemberID
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'update'
					and isnull(m.MCOption_lockAddress,0) = 0
				group by lm.emailaddr_, lm.list_, lm.externalMemberID
				having count(*) > 1
			) as temp on lm2.emailaddr_ = temp.emailaddr_
				and lm2.list_ = temp.list_
				and lm2.externalMemberID = temp.externalMemberID;

			if @thisListAutoManageActive = 1 BEGIN
				-- update the email addresses that have been marked
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updating memberships (Pass 1) - Records: ' + cast(@@rowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				update m 
				set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
					m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
					m.fullname_ = lm.fullname_,
					m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'update'
					and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0;
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = 'update1'
			from (
				select min(autoID) as autoID 
				from MC_ListMembersForLyris 
				where list_ = @thisListName 
				and updateStatus is null 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ is not null
				and (lm.emailaddr_ <> m.emailaddr_ or lm.fullname_ <> m.fullname_)
				and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0
			left outer join members_ existingAddresses on lm.list_ = existingAddresses.list_
				and (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			where existingAddresses.memberID_ is null;

			update lm2 
			set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
			from MC_ListMembersForLyris lm2
			inner join (
				select lm.emailaddr_, lm.list_, lm.externalMemberID
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'update1'
					and isnull(m.MCOption_lockAddress,0) = 0
				group by lm.emailaddr_, lm.list_, lm.externalMemberID
				having count(*) > 1
			) as temp on lm2.emailaddr_ = temp.emailaddr_
				and lm2.list_ = temp.list_
				and lm2.externalMemberID = temp.externalMemberID;

			if @thisListAutoManageActive = 1
			BEGIN
				-- update the email addresses that have been marked
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updating memberships (Pass 2) - Records: ' + cast(@@rowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				update m 
				set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
					m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
					m.fullname_ = lm.fullname_,
					m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m 
					on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'update1'
					and isnull(m.MCOption_lockAddress,@defaultMCOption_lockAddress) = 0;
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				null as functionName, 'expired' as updateStatus, l.isAutoManageActive
			from #ListsForLyrisSync l
			inner join members_ m on l.autoID = @thisListAutoID
				and l.list_ = m.list_ collate Latin1_General_CI_AI
				and m.membertype_ in ('confirm','held','normal')
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
			left outer join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID collate Latin1_General_CI_AI
				and m.list_ = lm.list_ 
			where (lm.autoID is null and isnull(m.MCOption_keepActive,@defaultMCOption_keepActive) = 0);

			if @thisListAutoManageActive = 1 BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expiring members - Records: ' + cast(@@rowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				update m 
				set m.membertype_ = 'expired',
					m.ExpireDate_ = getdate()
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'expired';
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm 
			set lm.updateStatus = 'reactivate'
			from members_ m
			inner join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
				and m.list_ = lm.list_
				and m.list_ = @thisListName
				and m.membertype_ = 'expired'
				and lm.functionName in ('managePopulation','manageStatus');

			if @thisListAutoManageActive = 1 BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Reactivating memberships - Records: ' + cast(@@rowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
				
				update m 
				set m.membertype_ = 'normal',
					m.ExpireDate_ = null
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = 'added'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					and nullif(lm.emailaddr_,'') is not null
				left outer join members_ m
					on lm.list_ = m.list_
					and (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = right(lm.emailaddr_,len(lm.emailaddr_)-charindex('@',lm.emailaddr_)) and m.usernamelc_ = left(lm.emailaddr_,charindex('@',lm.emailaddr_)-1))
					)
			where m.memberID_ is null;

			if @thisListAutoManageActive = 1 BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Adding new memberships - Records: ' + cast(@@rowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				select getdate() as DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype as membertype_, @defaultSubType as subype_
				from MC_ListMembersForLyris lm
				where lm.list_ = @thisListName
				and lm.updateStatus = 'added';
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		select @thisListAutoID = min(autoID) from #ListsForLyrisSync where autoID > @thisListAutoID;
	END


	-- send email if there are members with no email address
	IF EXISTS (select 1 from dbo.members_ where EmailAddr_ = '') BEGIN
		exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc='', @bcc='', @subject='PRODUCTION - Developer Needed - Lyris Members with blank email address', 
			@message='There are records in dbo.members_ where EmailAddr_ = ''''', 
			@priority='high',  @smtpserver='***********', @authUsername='',  @authPassword='';
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	set @escalateError = 1;
END CATCH

if len(rtrim(ltrim(@errorLog))) > 0 BEGIN
	set @errorLog = @errorLog + @crlf + @crlf + @crlf + isnull(@progressLog,'');
	set @emailSubject =  convert(varchar(19), getdate(), 121) + ' - MC ListSync Process: Errors Generated';

	exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject=@emailSubject, @message=@errorLog,  @priority='normal',  @smtpserver='***********', 
		@authUsername='',  @authPassword='';

	set @message =  convert(varchar(19), getdate(), 121) + ' : Sent Error Log Email';
		if @debugMode = 1 RAISERROR(@message,0,1);
END

if ( @escalateError = 1) BEGIN
	set @message =  convert(varchar(19), getdate(), 121) + ' : Escalating Fatal Error';
	if @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END

RETURN 0;
GO

ALTER PROC [dbo].[job_updateList-natle_justiceServices]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @listName varchar(100), @now datetime

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers

create TABLE #tempListMembers (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(500), 
	email varchar(200),
	usernameLC_ varchar(100),
	domain_ varchar(150)
)


set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

exec membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList

if not exists (select * from tlasites.dataTransfer.dbo.natle_eligibleForJusticeServicesList)
BEGIN

	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList ended with no rows in table  membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList. Check for timeout or other issues.' 

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	print 'email sent: ' + @errmsg

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.natle_eligibleForJusticeServicesList



	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email))


	-- mark email addresses that are NOT in temp table as expired
	update m set
		membertype_ = 'expired',
		ExpireDate_ = @now
	from members_ m 
	left outer join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
		and tmp.email is null
		and m.membertype_ in ('normal','held')
		and m.association_ not in ('CT')

	-- reactivate previously expired email addresses that are in temp table
	update m set
		membertype_ = 'normal',
		ExpireDate_ = null
	from members_ m 
	inner join #tempListMembers tmp
		on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired'
		and m.association_ not in ('CT')

	-- update email addresses/fullname based on matching membernumber and association
	update m set
		domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers tmp
	inner join members_ m
		on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
		and m.membertype_ = 'normal'
	left outer join members_ prexistingEmail
		on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from dbo.members_
		where list_ = @listname 
	) 

	insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now,domain_,email,fullname,@listName ,usernameLc_ ,memberNumber as ExternalMemberID,orgcode
	from #tempListMembers
	where orgcode not in ('CT')
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers
GO

ALTER PROC dbo.job_runHourlyJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings  */
/* ********************** */
BEGIN TRY
	exec dbo.job_enforceListSettings
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC tlasites.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


RETURN 0
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* job_updateList-natle_justiceServices */
/* ********************** */
BEGIN TRY
	EXEC dbo.[job_updateList-natle_justiceServices]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.job_updateList-natle_justiceServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_doSyncListMemberData */
/* ********************** */
BEGIN TRY
	EXEC dbo.ky_doSyncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.ky_doSyncListMemberData'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

use lyriscustom
GO

ALTER PROC dbo.job_runDailyMaintenanceChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY


	DECLARE @smtpserver varchar(20), @smtpUsername varchar(30), @smtpPassword varchar(30), @tier varchar(20), 
		@errorSubjectRoot varchar(100), @errorSubjectRootNonDev varchar(100), @errorSubject varchar(300), 
		@errmsg varchar(max), @crlf varchar(10), @tableHTML VARCHAR(MAX);

	/* variables */
	SET @tier = 'PRODUCTION'
	SET @smtpserver = '***********'
	IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
		SET @tier = 'DEVELOPMENT'
		SET @smtpserver = 'mail.trialsmith.com'
	END
	IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
		SET @tier = 'BETA'
		SET @smtpserver = 'mail.trialsmith.com'
	END

	SET @crlf = char(13) + char(10);
	SET @errorSubjectRoot = @tier + ' - Developer Needed - '
	SET @errorSubjectRootNonDev = @tier + ' - Non-Developer Needed - '



	/* Databases with fullbackups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;

			declare @fullbackupCheck_now datetime = getdate()
			declare @fullbackupCheck_defaultOldestAllowedDate datetime = dateadd(day,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_oneMonthAgo datetime = dateadd(month,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_firstsaturdayThisMonth datetime, @fullbackupCheck_firstsaturdayLastMonth datetime

			SELECT @fullbackupCheck_firstsaturdayThisMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0))
			SELECT @fullbackupCheck_firstsaturdayLastMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),-1)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),0))

			declare @fullbackupCheck_oldestDateOverrides TABLE (database_name varchar(100), warningDate datetime) 

			--trialslyris1 full backups are twice a week .... should also be one within last 4 days
			insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('trialslyris1',dateadd(day,-4,getdate()))

			-- lyrisarchive fullbacks are the first saturday of the month
			-- if been at least 1 day since midnight of first saturday of month use first this Saturday, otherwise use last month's first Saturday
			if (datediff(day,@fullbackupCheck_firstsaturdayThisMonth,@fullbackupCheck_now) >= 1 )
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayThisMonth)
			else 
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayLastMonth)

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Full Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					left outer join @fullbackupCheck_oldestDateOverrides o
						on o.database_name = bs.database_name COLLATE Latin1_General_CI_AI
					where bs.type = 'D'
					GROUP BY bs.database_name, o.warningDate
					having MAX(bs.backup_finish_date) < case when o.warningDate is not null then o.warningDate else @fullbackupCheck_defaultOldestAllowedDate end
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);


			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with full backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the full backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with diff backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twodaysago datetime = dateadd(hour,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Diff Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'I'
					GROUP BY bs.database_name
					having MAX(bs.backup_finish_date) < @backupcheck_twodaysago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with diff backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the diff backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with log backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twohoursago datetime = dateadd(minute,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Log Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'L' and bs.recovery_model = 'FULL'
					GROUP BY bs.database_name, recovery_model
					having MAX(bs.backup_finish_date) < @backupcheck_twohoursago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with log backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases (full recovery model only) where the log backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END




	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_emailWhenFailedDigests

AS

declare @badlist varchar(60), @message varchar(1000);
EXEC dbo.checkForFailedDigests @dateoflastdigest=null, @daysToLookBack=1, @fixList=0, @badlist=@badlist OUTPUT;

IF @badlist is not null begin
	set @message = 'Possible Failed Lyris Digests with list ' + @badlist;

	exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject='Production - Developer Needed - Possible Failed Lyris Digests',
		@message=@message, @priority='high', @smtpserver='***********', @authUsername='', @authPassword='';
end
GO


