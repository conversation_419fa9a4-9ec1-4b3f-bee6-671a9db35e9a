USE [trialslyris1]
GO

DROP INDEX [IX_members__MCEmailKey__Includes3] ON [dbo].[members_]
GO



/*

-- this is the definition of the index that was dropped. Lyris Web Admin was breaking due to the presence of this filtered index

CREATE NONCLUSTERED INDEX [IX_members__MCEmailKey__Includes3] ON [dbo].[members_]
(
	[MCEmailKey] ASC
)
INCLUDE([MemberID_],[Association_],[ExternalMemberID]) 
WHERE ([MCEmailK<PERSON>] IS NOT NULL)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

*/