use lyrisCustom
GO

ALTER PROC dbo.job_emailWhenFailedDigests

AS

declare @badlist varchar(60), @message varchar(1000);
EXEC dbo.checkForFailedDigests @dateoflastdigest=null, @daysToLookBack=1, @fixList=0, @badlist=@badlist OUTPUT;

IF @badlist is not null begin
	set @message = 'Possible Failed Lyris Digests with list ' + @badlist;

	exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject='Production - Developer Needed - Possible Failed Lyris Digests',
		@message=@message, @priority='high', @smtpserver='10.100.1.86', @authUsername='', @authPassword='';
end
GO
