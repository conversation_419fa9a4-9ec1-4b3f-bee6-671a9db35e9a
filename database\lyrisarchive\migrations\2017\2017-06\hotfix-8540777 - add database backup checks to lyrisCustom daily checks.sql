use lyrisCustom;
GO

CREATE PROC dbo.job_runDailyMaintenanceChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY


	DECLARE @smtpserver varchar(20), @smtpUsername varchar(30), @smtpPassword varchar(30), @tier varchar(20), 
		@errorSubjectRoot varchar(100), @errorSubjectRootNonDev varchar(100), @errorSubject varchar(300), 
		@errmsg varchar(max), @crlf varchar(10), @tableHTML VARCHAR(MAX);

	/* variables */
	SET @tier = 'PRODUCTION'
	SET @smtpserver = '10.100.1.86'
	IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
		SET @tier = 'DEVELOPMENT'
		SET @smtpserver = 'mail.trialsmith.com'
	END
	IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
		SET @tier = 'BETA'
		SET @smtpserver = 'mail.trialsmith.com'
	END

	SET @crlf = char(13) + char(10);
	SET @errorSubjectRoot = @tier + ' - Developer Needed - '
	SET @errorSubjectRootNonDev = @tier + ' - Non-Developer Needed - '



	/* Databases with fullbackups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;

			declare @fullbackupCheck_now datetime = getdate()
			declare @fullbackupCheck_defaultOldestAllowedDate datetime = dateadd(day,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_oneMonthAgo datetime = dateadd(month,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_firstsaturdayThisMonth datetime, @fullbackupCheck_firstsaturdayLastMonth datetime

			SELECT @fullbackupCheck_firstsaturdayThisMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0))
			SELECT @fullbackupCheck_firstsaturdayLastMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),-1)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),0))

			declare @fullbackupCheck_oldestDateOverrides TABLE (database_name varchar(100), warningDate datetime) 

			--trialslyris1 full backups are twice a week .... should also be one within last 4 days
			insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('trialslyris1',dateadd(day,-4,getdate()))

			-- lyrisarchive fullbacks are the first saturday of the month
			-- if been at least 1 day since midnight of first saturday of month use first this Saturday, otherwise use last month's first Saturday
			if (datediff(day,@fullbackupCheck_firstsaturdayThisMonth,@fullbackupCheck_now) >= 1 )
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayThisMonth)
			else 
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayLastMonth)

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Full Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					left outer join @fullbackupCheck_oldestDateOverrides o
						on o.database_name = bs.database_name COLLATE Latin1_General_CI_AI
					where bs.type = 'D'
					GROUP BY bs.database_name, o.warningDate
					having MAX(bs.backup_finish_date) < case when o.warningDate is not null then o.warningDate else @fullbackupCheck_defaultOldestAllowedDate end
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);


			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with full backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the full backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with diff backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twodaysago datetime = dateadd(hour,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Diff Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'I'
					GROUP BY bs.database_name
					having MAX(bs.backup_finish_date) < @backupcheck_twodaysago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with diff backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the diff backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with log backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twohoursago datetime = dateadd(minute,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Log Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'L' and bs.recovery_model = 'FULL'
					GROUP BY bs.database_name, recovery_model
					having MAX(bs.backup_finish_date) < @backupcheck_twohoursago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with log backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases (full recovery model only) where the log backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END




	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO