USE lyrisCustom
GO 

ALTER PROC dbo.job_emailWhenFailedDigests

AS

declare @badlist varchar(60), @message varchar(1000);
EXEC dbo.checkForFailedDigests @dateoflastdigest=null, @daysToLookBack=1, @fixList=0, @badlist=@badlist OUTPUT;

IF @badlist is not null begin
	set @message = 'Possible Failed Lyris Digests with list ' + @badlist;

	exec membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject='Production - Developer Needed - Possible Failed Lyris Digests',
		@message=@message, @priority='high', @smtpserver='***********', @authUsername='', @authPassword='';
end
GO
ALTER PROC dbo.job_runDailyMaintenanceChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY


	DECLARE @smtpserver varchar(20), @smtpUsername varchar(30), @smtpPassword varchar(30), @tier varchar(20), 
		@errorSubjectRoot varchar(100), @errorSubjectRootNonDev varchar(100), @errorSubject varchar(300), 
		@errmsg varchar(max), @crlf varchar(10), @tableHTML VARCHAR(MAX);

	/* variables */
	SET @tier = 'PRODUCTION'
	SET @smtpserver = '***********'
	IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
		SET @tier = 'DEVELOPMENT'
		SET @smtpserver = 'mail.trialsmith.com'
	END
	IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
		SET @tier = 'BETA'
		SET @smtpserver = 'mail.trialsmith.com'
	END

	SET @crlf = char(13) + char(10);
	SET @errorSubjectRoot = @tier + ' - Developer Needed - '
	SET @errorSubjectRootNonDev = @tier + ' - Non-Developer Needed - '



	/* Databases with fullbackups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;

			declare @fullbackupCheck_now datetime = getdate()
			declare @fullbackupCheck_defaultOldestAllowedDate datetime = dateadd(day,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_oneMonthAgo datetime = dateadd(month,-1,@fullbackupCheck_now)
			declare @fullbackupCheck_firstsaturdayThisMonth datetime, @fullbackupCheck_firstsaturdayLastMonth datetime

			SELECT @fullbackupCheck_firstsaturdayThisMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0))
			SELECT @fullbackupCheck_firstsaturdayLastMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),-1)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),0))

			declare @fullbackupCheck_oldestDateOverrides TABLE (database_name varchar(100), warningDate datetime) 

			--trialslyris1 full backups are twice a week .... should also be one within last 4 days
			insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('trialslyris1',dateadd(day,-4,getdate()))

			-- lyrisarchive fullbacks are the first saturday of the month
			-- if been at least 1 day since midnight of first saturday of month use first this Saturday, otherwise use last month's first Saturday
			if (datediff(day,@fullbackupCheck_firstsaturdayThisMonth,@fullbackupCheck_now) >= 1 )
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayThisMonth)
			else 
				insert into @fullbackupCheck_oldestDateOverrides (database_name,warningDate) values ('lyrisarchive',@fullbackupCheck_firstsaturdayLastMonth)

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Full Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					left outer join @fullbackupCheck_oldestDateOverrides o
						on o.database_name = bs.database_name COLLATE Latin1_General_CI_AI
					where bs.type = 'D'
					GROUP BY bs.database_name, o.warningDate
					having MAX(bs.backup_finish_date) < case when o.warningDate is not null then o.warningDate else @fullbackupCheck_defaultOldestAllowedDate end
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);


			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with full backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the full backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with diff backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twodaysago datetime = dateadd(hour,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Diff Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'I'
					GROUP BY bs.database_name
					having MAX(bs.backup_finish_date) < @backupcheck_twodaysago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with diff backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases where the diff backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with log backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			declare @backupcheck_twohoursago datetime = dateadd(minute,-2,getdate())

			SET @tableHTML = '' + 
				replace(Stuff((
					SELECT '|' + bs.database_name + ' - Last Log Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) as [text()]
					FROM msdb.dbo.backupset bs
					where bs.type = 'L' and bs.recovery_model = 'FULL'
					GROUP BY bs.database_name, recovery_model
					having MAX(bs.backup_finish_date) < @backupcheck_twohoursago
					FOR XML PATH ('')
				),1,1,''),'|',@crlf);

			IF len(@tableHTML) > 1 BEGIN
				SET @errorSubject = @errorSubjectRoot + 'Databases with log backups behind schedule - ' + @@SERVERNAME;
				SET @errmsg = @errorSubject + @crlf + @crlf + 'These are the databases (full recovery model only) where the log backups appear to be behind schedule. Please ensure that the backup job is running.' + @crlf + @crlf + @tableHTML;
				EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
					@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
					@smtpserver=@smtpserver, @authUsername=@smtpUsername, @authPassword=@smtpPassword;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END




	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.natle_justiceServices

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(10), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.natle_justiceServicesEligible;

if not exists (select * from tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating NATLE Justice Services List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_justiceServicesEligible ended with no rows in table membercentral.dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held')
	and m.association_ not in ('CT');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired'
	and m.association_ not in ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.association_ not in ('CT')
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers
	where orgcode not in ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO
ALTER PROC dbo.natle_seminarWebLive

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate();
set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';
set @listName = 'seminarweblive';

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
create TABLE #swl_eligibleForNatleMarketingList (platform varchar(20), orgcode varchar(10), memberID int, 
	membernumber varchar(100), fullname varchar(100), email varchar(100), usernameLC_ varchar(100), 
	domain_ varchar(250));

exec membercentral.customApps.dbo.natle_seminarWebLiveEligible;

if not exists (select * from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating SeminarWebLive Marketing List for NATLE';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table  membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.' ;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	update #swl_eligibleForNatleMarketingList 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- update fullname/association based on matching email address
	update m 
	set association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	from #swl_eligibleForNatleMarketingList tmp
	inner join trialslyris1.dbo.members_ m on m.list_ = @listName
		and m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and (m.association_ <> tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ m 
	left outer join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and isListAdm_ <> 'T'
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ m 
	inner join #swl_eligibleForNatleMarketingList tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and m.list_ = @listName
		and m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #swl_eligibleForNatleMarketingList ec
	where exists (
		select usernameLc_, domain_
		from trialslyris1.dbo.members_
		where list_ = @listname 
		and usernameLc_ = ec.usernameLc_ collate SQL_Latin1_General_CP1_CI_AS
		and domain_ = ec.domain_ collate SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	select *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) as rowNum
	into #swl_eligibleForNatleMarketingList2
	from #swl_eligibleForNatleMarketingList;

	delete from #swl_eligibleForNatleMarketingList2
	where rowNum > 1;

	insert into trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	from #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO
ALTER PROC dbo.trialsmith_updateMarketingLists
AS

set nocount on;

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool;
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers;
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete;
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs;
CREATE TABLE #memberPool (poolid int identity(1,1),DateJoined_ datetime,domain_ varchar(250),emailaddr_ varchar(100),fullname_ varchar(100),list_ varchar(60),usernameLc_ varchar(100),ExternalMemberID varchar(100),association_ varchar(10),depomemberdataid int);
CREATE TABLE #updatedMembers (id int identity(1,1),poolid int, memberID_ int);
CREATE TABLE #membershipsToDelete (id int identity(1,1),memberid_ int);
CREATE TABLE #unsubs (id int identity(1,1),emailaddr_ varchar(100));

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100), @currentListName varchar(100), @tier varchar(20), 
	@errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10);

set @defaultMembertype = 'normal';
set @defaultSubType = 'mail';

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

exec membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

if not exists (select emailaddr_ from membercentral.transfer.dbo.trialsmithMarketingListPopulation) BEGIN

	SET @errorSubject = @tier + ': Error Updating TrialSmith Marketing Lists';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

    insert into #unsubs (emailaddr_)
    select emailaddr_
    from trialslyris1.dbo.members_ m 
    where list_ in ('trialsmith','trialsmith_subscribers') 
	and m.membertype_ = 'unsub';

	insert into #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
    from membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
    left outer join #unsubs u on u.emailaddr_ = lp.emailaddr_ collate Latin1_General_CI_AI
    where u.emailaddr_ is null;

    --delete subscribed members with email addresses that are no longer in the pool
    insert into #membershipsToDelete (memberID_)
    select m.memberID_
    from trialslyris1.dbo.members_ m
    left outer join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers') 
	and pool.poolid is null 
	and m.membertype_ <> 'unsub';

    delete m
    from trialslyris1.dbo.members_ m
    inner join #membershipsToDelete md on m.memberid_ = md.memberid_;

    -- update
    insert into #updatedMembers (poolid, memberid_)
    select pool.poolid, m.memberID_
    from trialslyris1.dbo.members_ m WITH(NOLOCK)
    inner join #memberPool pool on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
	    and (
			    m.emailaddr_ <> pool.emailaddr_ collate Latin1_General_CI_AI
			    or m.fullname_ <> pool.fullname_ collate Latin1_General_CI_AI
			    or m.ExternalMemberID <> pool.ExternalMemberID collate Latin1_General_CI_AI
			    or m.association_ <> pool.association_ collate Latin1_General_CI_AI
				or isnull(m.depomemberdataID,0) <> isnull(pool.depomemberdataid,0)
	    )
	    and m.list_ in ('trialsmith','trialsmith_subscribers')
	    and m.membertype_ <> 'unsub';

    update m 
	set DateJoined_ = pool.DateJoined_,
	    fullname_= pool.fullname_,
	    list_= pool.list_,
	    ExternalMemberID = pool.ExternalMemberID,
	    association_ = pool.association_,
		depomemberdataid = pool.depomemberdataid
    from trialslyris1.dbo.members_ m WITH(NOLOCK)
    inner join #updatedMembers updated on m.memberid_ = updated.memberid_
    inner join #memberPool pool on updated.poolid = pool.poolid
    where m.list_ in ('trialsmith','trialsmith_subscribers') 
	and m.membertype_ <> 'unsub';

    -- delete all preexisting memberships from pool, leaving only entries that need to be created
    delete pool
    from #memberPool pool
    inner join trialslyris1.dbo.members_ m WITH(NOLOCK) on m.emailaddr_ = pool.emailaddr_ collate Latin1_General_CI_AI
	    and m.list_ = pool.list_ collate Latin1_General_CI_AI
    where m.list_ in ('trialsmith','trialsmith_subscribers');

    -- insert new memberships
    insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
    select DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid
    from #memberPool;

	--update trialsmithUsage
	truncate table trialslyris1.dbo.tsdata
	insert into trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, expList, numBadSrc, subType, expires)
	select distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
	from membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
	where depomemberdataid is not null;

    exec dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';
END

IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
	DROP TABLE #memberPool;
IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
	DROP TABLE #updatedMembers;
IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
	DROP TABLE #membershipsToDelete;
IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
	DROP TABLE #unsubs;

GO
ALTER PROC dbo.ts_membercentraladmins

AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errmsg nvarchar(2048), 
	@proc sysname, @lineno int, @crlf varchar(10), @defaultMembertype varchar(100), @defaultSubType varchar(100),
	@listName varchar(100), @now datetime;

set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'membercentraladmins'

SET @crlf = char(13) + char(10);
SET @tier = 'PRODUCTION';
SET @smtpserver = '***********';
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT';
	SET @smtpserver = 'mail.trialsmith.com';
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA';
	SET @smtpserver = 'mail.trialsmith.com';
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
create TABLE #tempListMembers (platform varchar(20), orgcode varchar(10), memberID int, membernumber varchar(100), 
	fullname varchar(500), email varchar(200), usernameLC_ varchar(100), domain_ varchar(150));

exec membercentral.customApps.dbo.ts_membercentraladminsEligible;

if not exists (select * from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible) BEGIN
	SET @errorSubject = @tier + ': Error Updating MemberCentralAdmins List';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + ' stored proc membercentral.customApps.dbo.ts_membercentraladminsEligible ended with no rows in table membercentral.dataTransfer.dbo.ts_membercentraladminsEligible. Check for timeout or other issues.';

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;

END ELSE BEGIN

	insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	select platform, orgcode, memberID, membernumber, fullname, email
	from membercentral.dataTransfer.dbo.ts_membercentraladminsEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	update #tempListMembers 
	set usernameLC_ = left(email,charindex('@',email)-1),
		domain_ = right(email,len(email)-charindex('@',email));

	-- mark email addresses that are NOT in temp table as expired
	update m 
	set membertype_ = 'expired',
		ExpireDate_ = @now
	from trialslyris1.dbo.members_ as m  
	left outer join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName 
	and tmp.email is null
	and m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	update m 
	set membertype_ = 'normal',
		ExpireDate_ = null
	from trialslyris1.dbo.members_ as m 
	inner join #tempListMembers as tmp on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
	where m.list_ = @listName
	and m.membertype_ = 'expired';

	-- update email addresses/fullname based on matching membernumber and association
	update m 
	set domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	from #tempListMembers as tmp
	inner join trialslyris1.dbo.members_ as m on m.list_ = @listName
		and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
		and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
		and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
		and m.membertype_ = 'normal'
	left outer join trialslyris1.dbo.members_ as prexistingEmail on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
		and prexistingEmail.list_ = @listName
		and prexistingEmail.memberID_ <> m.memberID_
	where prexistingEmail.memberID_ is null;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	delete ec
	from #tempListMembers ec
	where email in (
		select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
		from trialslyris1.dbo.members_
		where list_ = @listname 
	);

	insert into trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	select @now, domain_, email, fullname, @listName, usernameLc_, memberNumber as ExternalMemberID, orgcode
	from #tempListMembers;
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO


USE trialslyris1
GO
ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ts_membercentraladmins';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Make sure EClips Sending Addresses are setup correctly */
/* ********************** */
BEGIN TRY
	update m set
		ExternalMemberID = null,
		ExpireDate_ = null,
		membertype_ = 'normal',
		subtype_ = 'nomail',
		IsListAdm_ = 'T',
		fullname_ = 'TrialSmith EClips Sending Address - DO NOT DELETE'
	from members_ m
	where EmailAddr_ = '<EMAIL>'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error making sure EClips Sending Addresses are setup correctly'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH





/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Delete orphaned list members */
/* ********************** */
BEGIN TRY

	delete m
	from members_ m
	left outer join lists_ l 
		on l.name_ = m.list_
	where l.ListID_ is null

END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error deleting orphaned list members from lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH



/* ********************** */
/*    Ensure proper digest format in in place for members   */
/* ********************** */
BEGIN TRY 
	EXEC lyrisCustom.dbo.lists_syncListDigestSettings;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.lists_syncListDigestSettings';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH


/* ********************** */
/*    Queue List Digest   */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=0;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.list_queueDigest';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH


/* ********************** */
/*    Queue List Digest  Test Mode */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=1;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.list_queueDigest testmode';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ******************************************** */
/*    Log Daily Sends per Lyris ListID 			*/
/* ******************************************** */
BEGIN TRY
	DECLARE @reportDate date = DATEADD(DAY,-1,GETDATE());
	EXEC lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay @reportDate=@reportDate;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

RETURN 0
GO
ALTER PROC dbo.job_runHourlyJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings  */
/* ********************** */
BEGIN TRY
	exec dbo.job_enforceListSettings
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC tlasites.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


RETURN 0
GO
ALTER PROC dbo.mc_updateListMemberships
@debugMode bit = 0

AS

BEGIN TRY

	declare @progressLog varchar(max), @errorLog varchar(max), @CRLF varchar(2), @emailSubject varchar(500), @escalateError bit,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int, 
		@defaultMembertype varchar(100), @defaultSubType varchar(100), @defaultMCOption_keepActive bit, 
		@defaultMCOption_lockAddress bit, @defaultMCOption_lockName bit, @thisListName varchar(100), @thisListAutoID int,
		@thisListAutoManageActive bit, @message varchar(500), @lastrowcount int, @thisListOneWayList bit, @expireDateCutoff datetime,
		@runByMemberID int;

	set @crlf = char(13) + char(10);
	set @escalateError = 0;
	set @errorLog = '';
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	set @defaultMCOption_keepActive = 0;
	set @defaultMCOption_lockAddress = 0;
	set @defaultMCOption_lockName = 0;
	set @expireDateCutoff = dateadd(year,-1,getdate());

	exec MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #ListsForLyrisSync (autoid int IDENTITY(1,1), siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
		list_ varchar(100), isAutoManageActive bit);
	CREATE TABLE #tmpLogMessages (autoid int IDENTITY(1,1), siteID int, memberID int, listName varchar(100), msg varchar(500));

	truncate table dbo.MC_ListMembersForLyris;

	insert into #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	from membercentral.datatransfer.dbo.ListsForLyris
	order by orgcode, list_;

	-- delete lists that no longer exist in Lyris
	delete s
	from #ListsForLyrisSync s
	left outer join lists_ l
		on s.list_ = l.name_ collate Latin1_General_CI_AI
    where l.ListID_ is null


	insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	select lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.MCMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, right(lmfl.emailaddr_,len(lmfl.emailaddr_)-charindex('@',lmfl.emailaddr_)), 
		left(lmfl.emailaddr_,charindex('@',lmfl.emailaddr_)-1)
	from membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	inner join #ListsForLyrisSync lfl on lfl.list_ = lmfl.list_;

	-- null blank emails
	update dbo.MC_ListMembersForLyris
	set emailaddr_ = null
	where ltrim(rtrim(isnull(emailaddr_,''))) = '';
	
	-- loop list by list
	select @thisListAutoID = min(autoID) from #ListsForLyrisSync;
	while @thisListAutoID is not null BEGIN
		select @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		from #ListsForLyrisSync 
		where autoID = @thisListAutoID;

		if exists (select adminSend_ from dbo.lists_ where name_ = @thisListName and adminSend_ = 'T')
			set @thisListOneWayList = 1;
		else
			set @thisListOneWayList = 0;

		set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive as varchar(5));
		set @progressLog = @progressLog + @crlf + @message;
			if @debugMode = 1 RAISERROR(@message,0,1);

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding names to update';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = 'UpdateName'
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.fullname_ <> m.fullname_
				and (m.MCOption_lockName is null or m.MCOption_lockName=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select lm.siteID, lm.MCMemberID, lm.list_, 'Name changed from ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'UpdateName'
					and (m.MCOption_lockName is null or m.MCOption_lockName=0);

				-- update the full names that have been marked
				update m 
				set m.fullname_ = lm.fullname_
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'UpdateName'
					and (m.MCOption_lockName is null or m.MCOption_lockName=0);

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated names - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail' else 'UpdateEmail' end
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
			left outer join dbo.members_ existingAddresses on lm.list_ = existingAddresses.list_
				and (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			where existingAddresses.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from dbo.MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				if @thisListAutoManageActive = 1 BEGIN
					insert into #tmpLogMessages (siteID, memberID, listName, msg)
					select lm.siteID, lm.MCMemberID, lm.list_, 'Email changed from ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail1' else 'UpdateEmail1' end
			from (
				select min(autoID) as autoID 
				from MC_ListMembersForLyris 
				where list_ = @thisListName 
				and updateStatus is null 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ is not null
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				from dbo.MC_ListMembersForLyris lm2
				inner join dbo.members_ existingAddresses on lm2.list_ = existingAddresses.list_
					and (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ and existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				where lm2.list_ = @thisListName
				and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				if @thisListAutoManageActive = 1
				BEGIN
					insert into #tmpLogMessages (siteID, memberID, listName, msg)
					select lm.siteID, lm.MCMemberID, lm.list_, 'Email changed from ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m 
						on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);
					
					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				null as functionName, 'expired' as updateStatus, l.isAutoManageActive
			from #ListsForLyrisSync l
			inner join members_ m on l.autoID = @thisListAutoID
				and l.list_ = m.list_ collate Latin1_General_CI_AI
				and m.membertype_ in ('confirm','held','normal')
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
			left outer join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID collate Latin1_General_CI_AI
				and m.list_ = lm.list_ 
			where lm.autoID is null and (m.MCOption_keepActive is null or m.MCOption_keepActive=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				update m 
				set m.membertype_ = 'expired',
					m.ExpireDate_ = getdate()
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'expired';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm 
			set lm.updateStatus = 'reactivate'
			from members_ m
			inner join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
				and m.list_ = lm.list_
				and m.list_ = @thisListName
				and m.membertype_ = 'expired'
				and lm.functionName in ('managePopulation','manageStatus');

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';

				update m 
				set m.membertype_ = 'normal',
					m.ExpireDate_ = null
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Reactivated memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = 'added'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					and nullif(lm.emailaddr_,'') is not null
				left outer join members_ m
					on lm.list_ = m.list_
					and (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = right(lm.emailaddr_,len(lm.emailaddr_)-charindex('@',lm.emailaddr_)) and m.usernamelc_ = left(lm.emailaddr_,charindex('@',lm.emailaddr_)-1))
					)
			where m.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into #tmpLogMessages (siteID, memberID, listName, msg)
				select siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				from MC_ListMembersForLyris
				where list_ = @thisListName
				and updateStatus = 'added';

				insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				select getdate() as DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype as membertype_, @defaultSubType as subype_
				from MC_ListMembersForLyris lm
				where lm.list_ = @thisListName
				and lm.updateStatus = 'added';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Added new memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding old expired memberships to delete';
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					null as functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' as updateStatus, l.isAutoManageActive
				from #ListsForLyrisSync l
				inner join members_ m on l.autoID = @thisListAutoID
					and l.list_ = m.list_ collate Latin1_General_CI_AI
					and m.membertype_ = 'expired'
					and m.ExpireDate_ < @expireDateCutoff
					and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''

				set @lastrowcount = @@rowcount;

				if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
					delete m
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members deleted - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		select @thisListAutoID = min(autoID) from #ListsForLyrisSync where autoID > @thisListAutoID;
	END

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END


	-- send email if there are members with no email address
	IF EXISTS (select 1 from dbo.members_ where EmailAddr_ = '') BEGIN
		exec membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc='', @bcc='', @subject='PRODUCTION - Developer Needed - Lyris Members with blank email address', 
			@message='There are records in dbo.members_ where EmailAddr_ = ''''', 
			@priority='high',  @smtpserver='***********', @authUsername='',  @authPassword='';
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	set @escalateError = 1;
END CATCH

if len(rtrim(ltrim(@errorLog))) > 0 BEGIN
	set @errorLog = @errorLog + @crlf + @crlf + @crlf + isnull(@progressLog,'');
	set @emailSubject =  convert(varchar(19), getdate(), 121) + ' - MC ListSync Process: Errors Generated';

	exec membercentral.membercentral.dbo.sys_mcSendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject=@emailSubject, @message=@errorLog,  @priority='normal',  @smtpserver='***********', 
		@authUsername='',  @authPassword='';

	set @message =  convert(varchar(19), getdate(), 121) + ' : Sent Error Log Email';
		if @debugMode = 1 RAISERROR(@message,0,1);
END

if ( @escalateError = 1) BEGIN
	set @message =  convert(varchar(19), getdate(), 121) + ' : Escalating Fatal Error';
	if @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END

IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
	DROP TABLE #ListsForLyrisSync;
IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

RETURN 0;
GO