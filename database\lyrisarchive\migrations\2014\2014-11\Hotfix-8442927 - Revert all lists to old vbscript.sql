use trialslyris1;
GO

declare @oldcommand varchar(1000), @newcommand varchar(1000)

set @oldcommand  = 'C:\Progra~1\groovy\groovy-2.3.0\bin\groovy.bat d:\groovyscripts\DiscussionListProcessor.groovy -m $MESSAGEID -e production -d d:\groovyscripts\'
set @newcommand = 'c:\windows\system32\wscript.exe  //b d:\ehl\htmlconvert.vbs $MESSAGEID'


update dbo.lists_ set
	pgmbefore_ = @newcommand
where cast(pgmbefore_ as varchar(1000)) = @oldcommand
and name_ <> 'clone2way'