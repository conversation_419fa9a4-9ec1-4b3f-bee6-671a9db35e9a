use trialslyris1
GO

ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @proc sysname, @lineno INT;

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	DELETE top (50000) sd
	FROM lyrReportSummaryData sd
	INNER JOIN lists_ l ON sd.list = l.name_
		AND sd.created < DATEADD(day,-1 *KeepOutmailPostings_,GETDATE())
		AND KeepOutmailPostings_ <> 0;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to Clear old lyrReportSummaryData entries';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ********************** */
/* trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.natle_seminarWebLive';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;		
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.natle_justiceServices';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.ts_membercentraladmins';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.ky_listServices';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* dbo.mc_notifyListMemberIssues */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_notifyListMemberIssues;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running dbo.mc_notifyListMemberIssues';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Make sure EClips Sending Addresses are setup correctly */
/* ********************** */
BEGIN TRY
	UPDATE dbo.members_ 
	set ExternalMemberID = NULL,
		ExpireDate_ = NULL,
		membertype_ = 'normal',
		subtype_ = 'nomail',
		IsListAdm_ = 'T',
		fullname_ = 'TrialSmith EClips Sending Address - DO NOT DELETE'
	WHERE EmailAddr_ = '<EMAIL>';
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error making sure EClips Sending Addresses are setup correctly';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    UPDATE om 
	set title_ = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title_,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title_,70)
	    end
    FROM dbo.outmail_ om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID_
	    AND om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_skribe','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title_ not like '%|%';

    -- update mailings in needs approval
    UPDATE om 
	set title_ = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title_,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title_,70)
	    end
    FROM dbo.moderate_ om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID_
	    AND om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_skribe','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title_ not like '%|%';

    -- update mailings in summary data table
    UPDATE om 
	set title = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title,70)
	    end
    FROM dbo.lyrReportSummaryData om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID
	    AND om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_skribe','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title not like '%|%';
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to add segment name to trialsmith marketing list';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Delete orphaned list members */
/* ********************** */
BEGIN TRY
	DELETE m
	FROM members_ m
	LEFT OUTER JOIN lists_ l on l.name_ = m.list_
	WHERE l.ListID_ IS NULL;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error deleting orphaned list members from lists';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Ensure proper digest format in in place for members */
/* ********************** */
BEGIN TRY 
	EXEC lyrisCustom.dbo.lists_syncListDigestSettings;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.lists_syncListDigestSettings';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ******************* */
/* Queue List Digest   */
/* ******************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=0;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.list_queueDigest';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Queue List Digest  Test Mode */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=1;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.list_queueDigest testmode';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************************* */
/* Log Daily Sends per Lyris ListID  */
/* ********************************* */
BEGIN TRY
	DECLARE @reportDate date = DATEADD(DAY,-1,GETDATE());
	EXEC lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay @reportDate=@reportDate;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

RETURN 0
GO
