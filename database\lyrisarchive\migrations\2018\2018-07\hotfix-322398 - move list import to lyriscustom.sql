use lyrisCustom
GO

CREATE PROC dbo.ts_listIntegrationReportImport
@delUnsub bit,
@lockaddr bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- this proc requires #tmpListReport is created and populated
	IF OBJECT_ID('tempdb..#tmpListReport') IS NULL 
		RAISERROR('tmpListReport was not found.',16,1);

	IF NOT EXISTS (select 1 from #tmpListReport)
		RAISERROR('tmpListReport is empty.',16,1);

	-- remove all spaces in lists
	update #tmpListReport set lists = replace(lists,' ','');

	-- expand table
	IF OBJECT_ID('tempdb..#tmpListReportExpand') IS NOT NULL 
		DROP TABLE #tmpListReportExpand;
	CREATE TABLE #tmpListReportExpand (memberID int, statusType varchar(20), email varchar(400), membernumber varchar(50), listItem varchar(100));

	insert into #tmpListReportExpand (statusType, email, membernumber, listItem)
	select tmp.StatusType, tmp.Email, tmp.membernumber, vct.listitem
	from #tmpListReport as tmp
	cross apply lyrisArchive.dbo.fn_varCharListToTable(tmp.lists,'|') as vct
	where vct.listitem <> '';

	-- get memberIDs for joins below
	update tmp
	set tmp.memberID = m.memberID_
	from #tmpListReportExpand as tmp
	inner join trialslyris1.dbo.members_ as m on tmp.email = m.emailaddr_ COLLATE Latin1_General_CI_AI 
		and tmp.listitem = m.list_ COLLATE Latin1_General_CI_AI;

	-- update external memberIDs
	-- if we need to unsubscribe the DELETEs
	-- if we need to set the mcLockAddress for LOCKs
	update m
	set m.externalMemberID = tmp.membernumber,
		m.memberType_ = case when @delUnsub = 1 and tmp.statusType = 'DELETE' then 'expired' else m.memberType_ end,
		m.MCOption_lockAddress = case when @lockaddr = 1 and tmp.statusType = 'LOCK' then 1 else m.MCOption_lockAddress end
	from #tmpListReportExpand as tmp
	inner join trialslyris1.dbo.members_ as m on m.memberid_ = tmp.memberID;
	
	IF OBJECT_ID('tempdb..#tmpListReportExpand') IS NOT NULL 
		DROP TABLE #tmpListReportExpand;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
