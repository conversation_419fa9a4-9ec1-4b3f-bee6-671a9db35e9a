{"CGIReadOnly": "true", "adminSalt": "C3686DBB-9B51-456E-99AE6EC99903758F", "applicationListener": "mixed", "applicationMode": "curr2root", "applicationTimeout": "1,0,0,0", "bufferTagBodyOutput": "true", "clientCookies": "true", "clientManagement": "false", "clientStorage": "cookie", "clientTimeout": "90,0,0,0", "compression": "false", "CFMappings": {"/mc": {"PHYSICAL": "/app/membercentral"}, "/SemWebSWODPlayer": {"PHYSICAL": "/app/membercentral/model/seminarweb"}, "/logs": {"PHYSICAL": "/app/logs"}}, "datasources": {"customApps_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=customApps&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "customApps", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "datatransfer_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=datatransfer&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "datatransfer", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "lyrisarchive_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=lyrisarchive&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "lyrisarchive", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "MCBETA01.headquarters.trialsmith.com", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "15001", "storage": "false", "username": "sa", "validate": "false"}, "mailarchive_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=mailarchive&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "mailarchive", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "MCBETA01.headquarters.trialsmith.com", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "15001", "storage": "false", "username": "sa", "validate": "false"}, "membercentral_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=membercentral&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "membercentral", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformMail_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformMail&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformMail", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformQueue_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformQueue&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformQueue", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformstatsMC_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformstatsMC&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformstatsMC", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "searchMC_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=searchMC&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "searchMC", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_formbuilder_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=formbuilder&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "formbuilder", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_platformstats_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformstats&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformstats", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_search_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=search&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "search", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_seminarweb_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=seminarweb&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "seminarweb", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_trialsmith_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=trialsmith&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "trialsmith", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "trialslyris1_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=trialslyris1&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "trialslyris1", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "MCBETA01.headquarters.trialsmith.com", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "15001", "storage": "false", "username": "sa", "validate": "false"}, "lyrisCustom_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=lyrisCustom&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "lyrisCustom", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "MCBETA01.headquarters.trialsmith.com", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "15001", "storage": "false", "username": "sa", "validate": "false"}, "EmailTracking_beta": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=EmailTracking&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "EmailTracking", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "MCBETA01.headquarters.trialsmith.com", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "15001", "storage": "false", "username": "sa", "validate": "false"}}, "dotNotationUpperCase": "true", "hspw": "370309a6015d633c1a738cdb79826f79a3dd8699be8ffe47bee65446cb4ab6f3", "inspectTemplate": "always", "localScopeMode": "classic", "mailServers": [{"idleTimeout": "60000", "lifeTimeout": "300000", "password": "*********************************************************************", "port": "2525", "smtp": "smtp.sendgrid.net", "ssl": "false", "tls": "false", "username": "apikey"}], "mergeURLAndForm": "false", "nullSupport": "false", "requestTimeout": "0,0,1,0", "requestTimeoutEnabled": true, "requestTimeoutInURL": "false", "scopeCascading": "standard", "scriptProtect": "all", "searchResultsets": "true", "sessionMangement": "true", "sessionStorage": "memory", "sessionTimeout": "0,0,5,0", "sessionType": "application", "suppressWhitespaceBeforecfargument": "true", "supressContentForCFCRemoting": "false", "templateCharset": "windows-1252", "thisLocale": "en_US", "thisTimeZone": "America/Chicago", "timeServer": "pool.ntp.org", "useTimeServer": "false", "whitespaceManagement": "white-space-pref"}