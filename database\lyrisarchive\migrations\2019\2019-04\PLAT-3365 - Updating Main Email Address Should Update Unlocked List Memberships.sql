use trialslyris1
GO

CREATE PROC dbo.mc_updateListEmailAddress
@orgcode varchar(10), 
@memberNumber varchar(50), 
@oldEmailAddress varchar(255), 
@newEmailAddress varchar(255)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @newEmailAddrDomain varchar(100), @newEmailAddrUsername varchar(100);
	SET @newEmailAddrDomain = LOWER(RIGHT(@newEmailAddress, LEN(@newEmailAddress) - CHARINDEX('@', @newEmailAddress)));
	SET @newEmailAddrUsername = LOWER(LEFT(@newEmailAddress, CHARINDEX('@', @newEmailAddress) - 1));
	
	UPDATE m
	SET emailaddr_ = @newEmailAddress,
		domain_ = @newEmailAddrDomain,
		usernameLC_ = @newEmailAddrUsername
	FROM dbo.members_ as m
	INNER JOIN dbo.lists_format as lf on m.list_ = lf.name
		AND	lf.orgcode = @orgcode
		AND m.externalMemberID = @memberNumber
		AND isnull(m.MCOption_lockAddress,0) = 0
		AND m.emailaddr_ = @oldEmailAddress
	LEFT OUTER JOIN dbo.members_ as existingMembers on existingMembers.emailaddr_ = @newEmailAddress
		AND existingMembers.list_ = m.list_
		AND m.memberID_ <> existingMembers.memberID_ 
	WHERE existingMembers.emailaddr_ IS NULL;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO