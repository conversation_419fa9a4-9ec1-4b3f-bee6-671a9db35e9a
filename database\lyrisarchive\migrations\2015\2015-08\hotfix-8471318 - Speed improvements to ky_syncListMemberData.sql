use trialsLyris1
GO

CREATE TABLE [dbo].[ky_syncListMemberData] (
	[memberid_] [int] NOT NULL,
	[Address1_] [varchar](100) NULL,
	[Address2_] [varchar](100) NULL,
	[Address3_] [varchar](100) NULL,
	[areaofpractice1_] [varchar](250) NULL,
	[areaofpractice2_] [varchar](250) NULL,
	[areaofpractice3_] [varchar](250) NULL,
	[BarDate_] [varchar](10) NULL,
	[City_] [varchar](35) NULL,
	[Company_] [varchar](200) NULL,
	[CongressionalDistrict_] [varchar](20) NULL,
	[ContactPosition_] [varchar](100) NULL,
	[County_] [varchar](50) NULL,
	[District_] [varchar](20) NULL,
	[Fax_] [varchar](20) NULL,
	[Firstname_] [varchar](50) NULL,
	[Gender_] [varchar](10) NULL,
	[HD] [varchar](5) NULL,
	[JoinDate_] [varchar](10) NULL,
	[LastName_] [varchar](50) NULL,
	[Legislative_] [varchar](50) NULL,
	[MemberLevel_] [varchar](250) NULL,
	[MemberStatus_] [varchar](19) NULL,
	[MiddleName_] [varchar](25) NULL,
	[nickname_] [varchar](250) NULL,
	[numeric1_] [int] NULL,
	[numeric2_] [int] NOT NULL,
	[numeric3_] [int] NULL,
	[PostalCode_] [varchar](10) NULL,
	[prefix_] [varchar](10) NULL,
	[ProfSuffix_] [varchar](20) NULL,
	[renewLink_] [varchar](250) NULL,
	[SD] [varchar](5) NULL,
	[StateProvince_] [varchar](4) NULL,
	[Suffix_] [varchar](20) NULL,
	[Text1_] [varchar](250) NULL,
	[Text3_] [varchar](250) NULL,
	[Text4_] [nvarchar](250) NULL,
	[Text5_] [varchar](250) NULL,
	[Website_] [varchar](250) NULL,
	[WorkPhone_] [varchar](20) NULL
) ON [PRIMARY]
GO
ALTER TABLE dbo.ky_syncListMemberData ADD CONSTRAINT
	PK_ky_syncListMemberData PRIMARY KEY CLUSTERED 
	(
	memberid_
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

CREATE PROC dbo.ky_doSyncListMemberData
AS

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists


-- get all KY lists
select [name] as listname
into #tmpKYLists
from dbo.lists_format 
where orgcode = 'KY'
and [name] <> 'eclips_ky';


-- get membernumbers on those lists from lyris
delete from MEMBERCENTRAL.datatransfer.dbo.ky_syncListMemberData_members;

insert into MEMBERCENTRAL.datatransfer.dbo.ky_syncListMemberData_members (memberid_, MCMemberNumber, MCMemberID)
select m.memberid_, m.externalMemberID, null
from dbo.members_ as m 
inner join #tmpKYLists as tmp on tmp.listname = m.list_
where m.externalMemberID is not null;


-- call customApps ky_syncListMemberData to populate data
EXEC MEMBERCENTRAL.customApps.dbo.ky_doSyncListMemberData;


-- get data into local table
truncate table dbo.ky_syncListMemberData;

insert into dbo.ky_syncListMemberData (memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_)
select memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_
from MEMBERCENTRAL.datatransfer.dbo.ky_syncListMemberData;


-- update lyris member data
update m
set m.Address1_ = tmp.Address1_,
	m.Address2_ = tmp.Address2_,
	m.Address3_ = tmp.Address3_,
	m.areaofpractice1_ = tmp.areaofpractice1_,
	m.areaofpractice2_ = tmp.areaofpractice2_,
	m.areaofpractice3_ = tmp.areaofpractice3_,
	m.BarDate_ = tmp.BarDate_,
	m.City_ = tmp.City_,
	m.Company_ = tmp.Company_,
	m.CongressionalDistrict_ = tmp.CongressionalDistrict_,
	m.Contactposition_ = tmp.Contactposition_,
	m.County_ = tmp.County_,
	m.District_ = tmp.district_,
	m.Fax_ = tmp.Fax_,
	m.Firstname_ = tmp.Firstname_,
	m.Gender_ = tmp.Gender_,
	m.HD = tmp.HD,
	m.JoinDate_ = tmp.JoinDate_,
	m.LastName_ = tmp.LastName_,
	m.Legislative_ = tmp.Legislative_,
	m.MemberLevel_ = tmp.MemberLevel_,
	m.MemberStatus_ = tmp.MemberStatus_,
	m.MiddleName_ = tmp.MiddleName_,
	m.nickname_ = tmp.nickname_,
	m.numeric1_ = tmp.numeric1_,
	m.numeric2_ = tmp.numeric2_,
	m.numeric3_ = tmp.numeric3_,
	m.PostalCode_ = tmp.PostalCode_,
	m.prefix_ = tmp.prefix_,
	m.ProfSuffix_ = tmp.ProfSuffix_,
	m.RenewLink_ = tmp.RenewLink_,
	m.SD = tmp.SD,
	m.StateProvince_ = tmp.StateProvince_,
	m.Suffix_ = tmp.Suffix_,
	m.Text1_ = tmp.Text1_,
	m.Text3_ = tmp.Text3_,
	m.Text4_ = tmp.Text4_,
	m.Text5_ = tmp.Text5_,
	m.Website_ = tmp.Website_,
	m.WorkPhone_ = tmp.WorkPhone_
from dbo.members_ as m
inner join dbo.ky_syncListMemberData as tmp on tmp.memberid_ = m.memberid_;


-- cleanup
truncate table dbo.ky_syncListMemberData;

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists;

GO

ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'MCDEV01\TLASITES' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\TLASITES' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings */
/* ********************** */
BEGIN TRY
	-- turn off join by email for all lists
	update lists_
	set NoEmailSub_ = 'T'
	where NoEmailSub_ <> 'T'

	-- force all lists to only allow admins to add members
	update lists_ 
	set security_ = 'private'
	where security_ = 'open' and name_ not in ('eclips_js','brandigy')

	-- set all lists to invisible in Discussion Forum Interface
	update lists_ 
	set MriVisibility_ = 'I'
	where MriVisibility_ <> 'I'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */
BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* job_updateList-natle_justiceServices */
/* ********************** */
BEGIN TRY
	EXEC dbo.[job_updateList-natle_justiceServices]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.job_updateList-natle_justiceServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_doSyncListMemberData */
/* ********************** */
BEGIN TRY
	EXEC dbo.ky_doSyncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.ky_doSyncListMemberData'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

