use lyrisarchive
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sqsMissingMessages]') AND type in (N'U'))
DROP TABLE [dbo].[sqsMissingMessages]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[sqsMissingMessages](
	[missingMessageID] [int] IDENTITY(1,1) NOT NULL,
	[messageID] [int] NOT NULL,
	[list] [varchar](100) NULL,
	[s3key] [varchar](100) NULL,
	[originatingQueue] [varchar](50) NULL,
 CONSTRAINT [PK_sqsMissingMessages] PRIMARY KEY CLUSTERED 
(
	[missingMessageID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE NONCLUSTERED INDEX [IX__sqsMissingMessages__messageID_include3] ON [dbo].[sqsMissingMessages]
(
	[messageID] ASC
)
INCLUDE([list],[s3key],[originatingQueue]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
