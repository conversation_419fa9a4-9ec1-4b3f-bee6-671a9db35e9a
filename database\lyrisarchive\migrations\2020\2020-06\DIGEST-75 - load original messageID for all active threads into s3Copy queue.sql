use lyrisarchive;
GO

declare @possibleThreads TABLE (parentID_ int PRIMARY KEY)

declare @messagesToConvert TABLE (messageid_ int PRIMARY KEY, list varchar(150), objectkey varchar(500))

insert into @possibleThreads
select parentID_
from trialslyris1.dbo.messages_ m
group by parentID_

insert into @messagesToConvert (messageid_, list,objectkey)
select m.messageID_, ml.list, 'sigparser/tobedone/' + ml.list + '/' + RIGHT('000000' + cast(m.messageID_ % 1000 as varchar(10)),4) + '/' + cast(m.messageID_ as varchar(10)) + '.txt'
from @possibleThreads t
inner join messages_ m
    on t.parentID_ = m.messageID_
   and m.CreatStamp_ <  '5/13/2020'
inner join messageLists ml
    on ml.listID = m.listID


insert into membercentral.platformqueue.dbo.queue_s3copy (s3bucketname, objectkey, news3bucketname, newobjectkey)
select 'messages.membercentral.com', 'sigparser/donotdelete.txt' , 'messages.membercentral.com', objectkey
from @messagesToConvert c

