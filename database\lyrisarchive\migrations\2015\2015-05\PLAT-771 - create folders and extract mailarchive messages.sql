use mailarchive
GO

/* ********************************************** */
-- create folders
/* ********************************************** */
declare @rootfolder varchar(50)
set @rootfolder = '\\tssql3\e$\mailArchiveMessages\';

WITH q AS (
	SELECT 0 AS num
		UNION ALL
	SELECT num + 1
	FROM q
	WHERE num < 999
)
SELECT  dbo.fn_createDirectory(@rootfolder + lower(o.name) + '\')
FROM    sys.objects as o
where o.type = 'U'
union
SELECT  dbo.fn_createDirectory(@rootfolder + lower(o.name) + '\' + RIGHT ('0000'+ cast(q.num as varchar(5)),4))
FROM    q
cross join sys.objects as o
where o.type = 'U'
order by 1
OPTION ( MAXRECURSION 1000 )
GO



/* ******************************************** */
-- write out messages
/* ******************************************** */

-- aolfeedback - 20 min - max: 128822
-- domains - 45 sec - max: 10102
-- exceptions - 15 min - max: 95673
-- memberImport - 20 sec - max: 7858
-- orders - 7 min - max: 184386
-- support - 25 min - max: 157704
-- supportSemWeb - 9 min - max: 70418
-- tlatransactions - 66 min - max: 656652
-- tscustomermail - 133 min - max: 1751506

set nocount on

declare @messageBatch TABLE (emailid int PRIMARY KEY)
declare @results TABLE (done int)
declare @maxEmailID int, @rootfolder varchar(50)

set @rootfolder = '\\TSFILE1\f$\MailArchiveMessages\'
set @maxEmailID = 1749505

RUNBATCH:

insert into @messageBatch (emailID)
select top 500 emailID
from dbo.tscustomermail
where emailID > @maxEmailID
order by emailID

IF not exists (select * from @messageBatch) GOTO DONE

insert into @results (done)
select dbo.fn_writeFile(@rootfolder + 'tscustomermail\' + RIGHT ('0000'+ cast(tmp.emailID % 1000 as varchar(4)),4) + '\' + cast(tmp.emailID as varchar(20)) + '.eml', m.emailMessage, 1)
from @messageBatch tmp
inner join dbo.tscustomermail m with (nolock) on m.emailID = tmp.emailID

select @maxEmailID = max(emailID) from @messageBatch
print @maxEmailID

delete from @messageBatch
delete from @results

GOTO RUNBATCH

DONE:
print 'Done'

set nocount off
GO

/* ******************************************** */
-- drop columns
/* ******************************************** */
ALTER TABLE dbo.aolfeedback DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.[domains] DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.exceptions DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.memberImport DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.orders DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.support DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.supportSemWeb DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.tlatransactions DROP COLUMN emailMessage;
GO
ALTER TABLE dbo.tscustomermail DROP COLUMN emailMessage;
GO


DBCC CLEANTABLE (mailarchive,'dbo.aolfeedback',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.memberimport',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.domains',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.orders',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.supportSemWeb',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.exceptions',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.support',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.tlatransactions',10000) WITH NO_INFOMSGS;
GO
DBCC CLEANTABLE (mailarchive,'dbo.tscustomermail',10000) WITH NO_INFOMSGS;
GO

