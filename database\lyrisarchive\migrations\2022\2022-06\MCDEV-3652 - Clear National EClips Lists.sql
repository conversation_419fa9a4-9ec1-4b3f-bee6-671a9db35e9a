use lyrisarchive;
GO
declare @nationalEclipsLists TABLE (listname varchar(200) PRIMARY KEY)

insert into @nationalEclipsLists
select lyrislistname
from membercentral.trialsmith.dbo.eclipsPublications
where isStateEdition = 1 and isSpecial=0 and lyrislistname like 'eclips%'
group by lyrislistname
having count(*) = 1

update lf set [hidden]=1, orgcode=null
from @nationalEclipsLists temp
inner join trialslyris1.dbo.lists_ l
    on temp.listname = l.name_
left outer join trialslyris1.dbo.lists_format lf
	on l.name_ = lf.name
	

delete m
from @nationalEclipsLists temp
inner join trialslyris1.dbo.members_ m
    on m.List_ = temp.listname
GO