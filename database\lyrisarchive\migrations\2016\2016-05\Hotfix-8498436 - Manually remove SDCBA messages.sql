use lyrisarchive
GO

declare @listID int, @listID2 int, @listID3 int, @listID4 int, @listID5 int;
select @listID = listid from dbo.messageLists where list = 'sdcbajudicial';
select @listID2 = listid from dbo.messageLists where list = 'sdcbajeecsub1';
select @listID3 = listid from dbo.messageLists where list = 'sdcbajeecsub2';
select @listID4 = listid from dbo.messageLists where list = 'sdcbajeecsub3';
select @listID5 = listid from dbo.messageLists where list = 'sdcbajeecsub4';

select messageid_, 'https://admin.trialsmith.com/tsadmin/ListsRemoveMessage.cfm?messageID='+cast(messageid_ as varchar(20)) + '&listname=' + ml.list + '&saveremove=1' as urltorun
from dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listID = m.listID
where m.listID in (@listID,@listID2,@listID3,@listID4,@listID5)
and creatStamp_ < '1/1/2016'



