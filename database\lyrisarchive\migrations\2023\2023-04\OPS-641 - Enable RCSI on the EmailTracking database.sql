use master
GO

-- To check whether RCSI has been enabled:  
SELECT is_read_committed_snapshot_on FROM sys.databases WHERE [name] = 'emailTracking'

-- take the database offline
ALTER DATABASE emailTracking SET OFFLINE WITH ROLLBACK IMMEDIATE 
GO

-- enable read committed snapshot  
ALTER DATABASE emailTracking SET READ_COMMITTED_SNAPSHOT ON WITH ROLLBACK IMMEDIATE 
GO

-- bring database back online  
ALTER DATABASE emailTracking SET ONLINE 
GO

-- To check whether RCSI has been enabled:  
SELECT is_read_committed_snapshot_on FROM sys.databases WHERE [name] = 'emailTracking'
