use trialslyris1;
GO
ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ts_membercentraladmins';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Make sure EClips Sending Addresses are setup correctly */
/* ********************** */
BEGIN TRY
	update m set
		ExternalMemberID = null,
		ExpireDate_ = null,
		membertype_ = 'normal',
		subtype_ = 'nomail',
		IsListAdm_ = 'T',
		fullname_ = 'TrialSmith EClips Sending Address - DO NOT DELETE'
	from members_ m
	where EmailAddr_ = '<EMAIL>'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error making sure EClips Sending Addresses are setup correctly'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH





/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Delete orphaned list members */
/* ********************** */
BEGIN TRY

	delete m
	from members_ m
	left outer join lists_ l 
		on l.name_ = m.list_
	where l.ListID_ is null

END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error deleting orphaned list members from lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH




RETURN 0
GO

ALTER PROC dbo.mc_updateListMemberships
@debugMode bit = 0

AS

BEGIN TRY

	declare @progressLog varchar(max), @errorLog varchar(max), @CRLF varchar(2), @emailSubject varchar(500), @escalateError bit,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int, 
		@defaultMembertype varchar(100), @defaultSubType varchar(100), @defaultMCOption_keepActive bit, 
		@defaultMCOption_lockAddress bit, @defaultMCOption_lockName bit, @thisListName varchar(100), @thisListAutoID int,
		 @thisListAutoManageActive bit, @message varchar(500), @lastrowcount int, @thisListOneWayList bit, @expireDateCutoff datetime;
	DECLARE @updatedMemberships TABLE (id int identity(1,1), DateJoined_ datetime, domain_ varchar(250), emailaddr_ varchar(100),
		fullname_ varchar(100), list_ varchar(60), usernameLc_ varchar(100), ExternalMemberID varchar(100));

	set @crlf = char(13) + char(10);
	set @escalateError = 0;
	set @errorLog = '';
	set @defaultMembertype = 'normal';
	set @defaultSubType = 'mail';
	set @defaultMCOption_keepActive = 0;
	set @defaultMCOption_lockAddress = 0;
	set @defaultMCOption_lockName = 0;
	set @expireDateCutoff = dateadd(year,-1,getdate());

	exec MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	CREATE TABLE #ListsForLyrisSync (autoid int IDENTITY(1,1), siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
		list_ varchar(100), isAutoManageActive bit);

	truncate table dbo.MC_ListMembersForLyris;

	insert into #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	from membercentral.datatransfer.dbo.ListsForLyris
	order by orgcode, list_;

	-- delete lists that no longer exist in Lyris
	delete s
	from #ListsForLyrisSync s
	inner join lists_ l
		on s.list_ = l.name_ collate Latin1_General_CI_AI


	insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	select lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, right(lmfl.emailaddr_,len(lmfl.emailaddr_)-charindex('@',lmfl.emailaddr_)), 
		left(lmfl.emailaddr_,charindex('@',lmfl.emailaddr_)-1)
	from membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	inner join #ListsForLyrisSync lfl on lfl.list_ = lmfl.list_;

	-- null blank emails
	update dbo.MC_ListMembersForLyris
	set emailaddr_ = null
	where ltrim(rtrim(isnull(emailaddr_,''))) = '';

	-- null blank externalMemberIDs
	update dbo.MC_ListMembersForLyris
	set externalMemberID = null
	where ltrim(rtrim(isnull(externalMemberID,''))) = '';

	-- loop list by list
	select @thisListAutoID = min(autoID) from #ListsForLyrisSync;
	while @thisListAutoID is not null BEGIN
		select @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		from #ListsForLyrisSync 
		where autoID = @thisListAutoID;

		if exists (select adminSend_ from dbo.lists_ where name_ = @thisListName and adminSend_ = 'T')
			set @thisListOneWayList = 1;
		else
			set @thisListOneWayList = 0;

		set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive as varchar(5));
		set @progressLog = @progressLog + @crlf + @message;
			if @debugMode = 1 RAISERROR(@message,0,1);

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding names to update';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = 'UpdateName'
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.fullname_ <> m.fullname_
				and (m.MCOption_lockName is null or m.MCOption_lockName=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				-- update the full names that have been marked
				update m 
				set m.fullname_ = lm.fullname_
				from dbo.MC_ListMembersForLyris lm
				inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
					and lm.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
					and lm.updateStatus = 'UpdateName'
					and (m.MCOption_lockName is null or m.MCOption_lockName=0);

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated names - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message = convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail' else 'UpdateEmail' end
			from (
				select min(autoID) as autoID 
				from dbo.MC_ListMembersForLyris 
				where list_ = @thisListName 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join dbo.MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join dbo.members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.emailaddr_ is not null
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
			left outer join dbo.members_ existingAddresses on lm.list_ = existingAddresses.list_
				and (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ and existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			where existingAddresses.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from dbo.MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				if @thisListAutoManageActive = 1 BEGIN
					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = case when updatestatus = 'UpdateName' then 'UpdateNameAndEmail1' else 'UpdateEmail1' end
			from (
				select min(autoID) as autoID 
				from MC_ListMembersForLyris 
				where list_ = @thisListName 
				and updateStatus is null 
				and functionName in ('managePopulation','manageStatus') 
				group by emailaddr_
			) as deduped
			inner join MC_ListMembersForLyris lm on deduped.autoID = lm.autoID
			inner join members_ m on lm.list_ = m.list_
				and lm.list_ = @thisListName
				and lm.externalMemberID = m.externalMemberID
				and lm.emailaddr_ is not null
				and lm.emailaddr_ <> m.emailaddr_
				and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 BEGIN
				update lm2 
				set lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				from dbo.MC_ListMembersForLyris lm2
				inner join dbo.members_ existingAddresses on lm2.list_ = existingAddresses.list_
					and (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ and existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				where lm2.list_ = @thisListName
				and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				update lm2 
				set lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				from MC_ListMembersForLyris lm2
				inner join (
					select lm.emailaddr_, lm.list_
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and lm.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0)
					group by lm.emailaddr_, lm.list_
					having count(*) > 1
				) as temp on lm2.emailaddr_ = temp.emailaddr_
					and lm2.list_ = temp.list_
					and lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				if @thisListAutoManageActive = 1
				BEGIN
					-- update the email addresses that have been marked
					update m 
					set m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					from dbo.MC_ListMembersForLyris lm
					inner join dbo.members_ m 
						on lm.list_ = m.list_ collate Latin1_General_CI_AI
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID collate Latin1_General_CI_AI
						and lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						and (m.MCOption_lockAddress is null or m.MCOption_lockAddress=0);
					
					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				null as functionName, 'expired' as updateStatus, l.isAutoManageActive
			from #ListsForLyrisSync l
			inner join members_ m on l.autoID = @thisListAutoID
				and l.list_ = m.list_ collate Latin1_General_CI_AI
				and m.membertype_ in ('confirm','held','normal')
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
			left outer join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID collate Latin1_General_CI_AI
				and m.list_ = lm.list_ 
			where lm.autoID is null and (m.MCOption_keepActive is null or m.MCOption_keepActive=0);

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				update m 
				set m.membertype_ = 'expired',
					m.ExpireDate_ = getdate()
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'expired';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm 
			set lm.updateStatus = 'reactivate'
			from members_ m
			inner join MC_ListMembersForLyris lm on m.externalMemberID = lm.externalMemberID
				and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''
				and m.list_ = lm.list_
				and m.list_ = @thisListName
				and m.membertype_ = 'expired'
				and lm.functionName in ('managePopulation','manageStatus');

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				update m 
				set m.membertype_ = 'normal',
					m.ExpireDate_ = null
				from MC_ListMembersForLyris lm
				inner join members_ m on lm.list_ = m.list_
					and m.list_ = @thisListName
					and lm.externalMemberID = m.externalMemberID
					and lm.updateStatus = 'reactivate';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Reactivated memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			set @progressLog = @progressLog + @crlf + @message;
				if @debugMode = 1 RAISERROR(@message,0,1);

			update lm
			set lm.updateStatus = 'added'
			from 
				(
					select min(autoID) as autoID from MC_ListMembersForLyris where list_ = @thisListName and updateStatus is null and functionName in ('managePopulation') group by emailaddr_
				) deduped
				inner join MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					and nullif(lm.emailaddr_,'') is not null
				left outer join members_ m
					on lm.list_ = m.list_
					and (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = right(lm.emailaddr_,len(lm.emailaddr_)-charindex('@',lm.emailaddr_)) and m.usernamelc_ = left(lm.emailaddr_,charindex('@',lm.emailaddr_)-1))
					)
			where m.memberID_ is null;

			set @lastrowcount = @@rowcount;

			if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
				insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				select getdate() as DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype as membertype_, @defaultSubType as subype_
				from MC_ListMembersForLyris lm
				where lm.list_ = @thisListName
				and lm.updateStatus = 'added';

				set @lastrowcount = @@rowcount;
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Added new memberships - Records: ' + cast(@lastrowcount as varchar(10));
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Finding old expired memberships to delete';
				set @progressLog = @progressLog + @crlf + @message;
					if @debugMode = 1 RAISERROR(@message,0,1);

				insert into dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				select l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					null as functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' as updateStatus, l.isAutoManageActive
				from #ListsForLyrisSync l
				inner join members_ m on l.autoID = @thisListAutoID
					and l.list_ = m.list_ collate Latin1_General_CI_AI
					and m.membertype_ = 'expired'
					and m.ExpireDate_ < @expireDateCutoff
					and ltrim(rtrim(isnull(m.externalMemberID,''))) <> ''

				set @lastrowcount = @@rowcount;

				if @lastrowcount > 0 and @thisListAutoManageActive = 1 BEGIN
					delete m
					from MC_ListMembersForLyris lm
					inner join members_ m on lm.list_ = m.list_
						and m.list_ = @thisListName
						and lm.externalMemberID = m.externalMemberID
						and lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					set @lastrowcount = @@rowcount;
					set @message =  convert(varchar(19), getdate(), 121) + ' - ' + @thisListName + ': Expired members deleted - Records: ' + cast(@lastrowcount as varchar(10));
					set @progressLog = @progressLog + @crlf + @message;
						if @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			exec mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH



		select @thisListAutoID = min(autoID) from #ListsForLyrisSync where autoID > @thisListAutoID;
	END


	-- send email if there are members with no email address
	IF EXISTS (select 1 from dbo.members_ where EmailAddr_ = '') BEGIN
		exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc='', @bcc='', @subject='PRODUCTION - Developer Needed - Lyris Members with blank email address', 
			@message='There are records in dbo.members_ where EmailAddr_ = ''''', 
			@priority='high',  @smtpserver='***********', @authUsername='',  @authPassword='';
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	set @escalateError = 1;
END CATCH

if len(rtrim(ltrim(@errorLog))) > 0 BEGIN
	set @errorLog = @errorLog + @crlf + @crlf + @crlf + isnull(@progressLog,'');
	set @emailSubject =  convert(varchar(19), getdate(), 121) + ' - MC ListSync Process: Errors Generated';

	exec membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc='', @bcc='', @subject=@emailSubject, @message=@errorLog,  @priority='normal',  @smtpserver='***********', 
		@authUsername='',  @authPassword='';

	set @message =  convert(varchar(19), getdate(), 121) + ' : Sent Error Log Email';
		if @debugMode = 1 RAISERROR(@message,0,1);
END

if ( @escalateError = 1) BEGIN
	set @message =  convert(varchar(19), getdate(), 121) + ' : Escalating Fatal Error';
	if @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END


RETURN 0;
GO
