use lyrisarchive
GO

CREATE PROC [dbo].[lists_getDigestInfo]
@listname varchar(100),
@startDate datetime,
@endDate datetime

AS


declare @digestThreads TABLE (messageid_ int PRIMARY KEY, threadStartDate datetime, firstMessageInDateRange datetime, digestMessageCount int, previousMessageCount int, subject varchar(200), hdr<PERSON>rom varchar(500), fromAddress varchar(500), fullname varchar(500), memberNumber varchar (100))
declare @digestMessages TABLE (messageid_ int PRIMARY KEY, threadMessageID int, messagedate datetime, hdrFrom varchar(500), fromaddress varchar(500), hasAttachments bit, memberNumber varchar (100),fullname varchar(500))
declare @digestMemberPhotos TABLE (memberNumber varchar (100) PRIMARY KEY, hasPhoto bit)



insert into @digestThreads (messageid_, threadStartDate, firstMessageInDateRange, digestMessageCount, subject, hdrFrom, fromAddress)
SELECT max(m.parentid_) as parentid_, parentMessage.CreatStamp_, min(m.CreatStamp_), count(m.messageid_), parentMessage.hdrSubject_,parentMessage.HdrFrom_, parentMessage.HdrFromSpc_
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
inner join dbo.messages_ parentmessage on m.parentID_ = parentMessage.messageID_
where ml.list = @listname 
and m.creatstamp_ between @startDate and @enddate
and m.parentid_ is not null
group by m.parentid_, parentMessage.hdrSubject_, parentMessage.CreatStamp_, parentMessage.HdrFrom_, parentMessage.HdrFromSpc_

update t2 SET
    previousMessageCount = temp.previousMessageCount
from @digestThreads t2
inner join (
    select t.messageid_, count(*) as previousMessageCount
    from @digestThreads t
    inner join messages_ m 
        on m.ParentID_ = t.messageid_
        and m.CreatStamp_ < @startDate
    group by t.messageid_
) as temp on temp.messageid_ = t2.messageid_


update @digestThreads SET
    previousMessageCount = 0
where previousMessageCount is null

insert into @digestMessages  (messageid_, threadMessageID,messagedate, HdrFrom,  fromaddress, hasAttachments)
select m.MessageID_, t.messageid_, m.CreatStamp_, m.HdrFrom_,m.HdrFromSpc_, isnull(m.attachmentflag,0)
from @digestThreads t
inner join messages_ m
    on t.messageid_ = m.ParentID_
    and m.creatstamp_ between @startDate and @enddate


update t set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestThreads t
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = t.fromaddress
    and m.List_ = @listname
    and m.ExternalMemberID is not null

update m2 set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestMessages m2
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = m2.fromaddress
    and m.List_ = @listname
    and m.ExternalMemberID is not null


select t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.previousMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,
    numParticipants = count(distinct dm.fromaddress), 
    messagesWithAttachments = sum( case when dm.hasAttachments = 1 then cast(1 as int) else cast(0 as int) end),
    numReplies = sum( case when dm.fromaddress = t.fromAddress then cast(0 as int) else cast(1 as int) end),
    participants = lyrisCustom.dbo.PipeList(distinct isnull(dm.fullname, dm.fromAddress))
from @digestThreads t
inner join @digestMessages dm
    on dm.threadMessageID = t.messageid_
group by t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.previousMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber


select messageid_, threadMessageID , messagedate, hdrFrom, fromaddress, hasAttachments, memberNumber,fullname
from @digestMessages
order by threadMessageID, messageID_

GO