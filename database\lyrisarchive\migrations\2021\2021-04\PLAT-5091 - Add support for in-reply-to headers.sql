use lyrisarchive
GO

ALTER TABLE dbo.messages_ ADD
	hdrInReplyTo varchar(2000) NULL
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[messageInReplyTo](
	[inReplyToID] [int] IDENTITY(1,1) NOT NULL,
	[messageID] [int] NOT NULL,
	[inReplyTo] [varchar](255) NULL,
	[sortOrder] [int] NULL,
 CONSTRAINT [PK_messageInReplyTo] PRIMARY KEY CLUSTERED 
(
	[inReplyToID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE dbo.messageInReplyTo ADD CONSTRAINT
	FK_messageInReplyTo_messages_ FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.messages_
	(
	messageID_
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 

GO

CREATE NONCLUSTERED INDEX [IX__messageInReplyTo__messageID__inc2] ON [dbo].[messageInReplyTo]
(
	[messageID] ASC
)
INCLUDE([inReplyTo],[sortOrder]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO


