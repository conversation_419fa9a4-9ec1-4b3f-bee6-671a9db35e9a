USE lyrisarchive;   
GO  
EXEC sp_rename 'dbo.messages_', 'messages_old'; 
GO

EXEC sp_rename 'dbo.messageIndex', 'messages_'; 
GO

ALTER PROCEDURE [dbo].[lyris_processLyrisArchiveChangeQueue]

AS

DECLARE @currentMessageID int  
DECLARE @currentAutoID int  
DECLARE @currentOperationID int  
DECLARE @batchSize int  

Select @batchSize = 10000    

DECLARE @lastRecordProcessed int  
DECLARE @newLastRecordProcessed int    
select @lastRecordProcessed = lastRecordProcessed 
from TLASITES.search.dbo.tblSearchUpdateStatus 
where name = 'listmessageChangeQueue'      

DECLARE @MessageQueue TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, messageid_ int, changeID int, operationID int)    
INSERT INTO @MessageQueue (messageid_, changeID, operationID)  
select top (@batchSize) messageid_, changeID, operationID 
from dbo.messageChangeQueue  
where changeID > @lastRecordProcessed  
order by changeID    

select @newLastRecordProcessed = max(changeID) from @MessageQueue      
select top 1 @currentMessageID = messageid_, @currentOperationID = operationID, @currentAutoID = autoid 
from @MessageQueue 
order by autoid   

while @currentMessageID is not null  begin   
	if @currentOperationID = 1   
		begin
			update mst set
				searchtext = dbo.fn_RegExReplace(searchtext, '\btslistname[^\s]+xxx', 'tslistname' + ml.list + 'xxx')
			from messageSearchText mst
			inner join messages_ m
				on m.MessageID_ = mst.messageid_
			inner join messageLists ml
				on ml.listID = m.listid
				and m.MessageID_ = @currentMessageID
		end     
	if exists (select top 1 messageid_ from @MessageQueue where autoid > @currentAutoID)
		select top 1 @currentMessageID = messageid_, @currentOperationID = operationID, @currentAutoID = autoid from @MessageQueue where autoid > @currentAutoID order by autoid   
	else    
		select @currentMessageID = null  
end    
if @newLastRecordProcessed is not null   
	update TLASITES.search.dbo.tblSearchUpdateStatus set lastRecordProcessed = @newLastRecordProcessed 
	where name = 'listmessageChangeQueue'


RETURN 0
GO
ALTER PROC [dbo].[lyris_copyMessagesToArchive]
AS

declare @maxid int, @firstid int
select @maxid = max(messageid_) from lyrisarchive.dbo.messages_
select TOP 1 @firstid = MessageID_ FROM trialslyris1.dbo.messages_ WHERE CreatStamp_ < DATEADD(minute, -1, getdate()) ORDER BY MessageID_ desc


declare @tmpMessages TABLE (
	[Body_] [text] NULL,
	[CreatStamp_] [smalldatetime] NOT NULL,
	[HdrAll_] [text] NULL,
	[HdrFrom_] [varchar](200) NULL,
	[HdrFromSpc_] [varchar](200) NULL,
	[HdrSubject_] [varchar](1000) NULL,
	[List_] [varchar](60) NOT NULL,
	[MessageID_] [int] NOT NULL,
	[attachmentflag] [bit] NULL,
	[ParentID_] [int] NULL,
	[listid] [int] NULL,
	[MemberID_] [int] NOT NULL,
	[externalMemberID] [varchar](100) NULL,
	[Digested_] [char](1) NOT NULL,
	[inmailID] int NULL
)

declare @messagesToActivate TABLE (
	[MessageID_] [int] NOT NULL
)

declare @inmailmap TABLE (inmailID int, messageid_ int)

declare @oneweekago datetime
set @oneweekago = dateadd(day,-7,getdate())

insert into @inmailmap (inmailID , messageid_ )
select im.inmailID, om.archiveMessageID_
from dbo.inmailMessages as im 
inner join trialslyris1.dbo.outmail_ om
	on im.inmailID = om.inmailID_
	and im.messageID_ is null
	and om.archiveMessageID_ is not null
	and om.created_ > @oneweekago
inner join trialslyris1.dbo.lists_ l
	on l.name_ = om.list_
	and l.NoArchive_ = 'F'

-- update inmailMessages for attachment link processing
update im set
	messageID_ = tmp.messageid_
from @inmailmap as tmp
inner join dbo.inmailMessages as im on im.inmailID = tmp.inmailID

update m set
	body_ = fs.body_,
	hdrall_ = fs.hdrall_
from @inmailmap as tmp
inner join dbo.inmailMessages as im
	on im.inmailID = tmp.inmailID
inner join dbo.inmailMesssageFullSource fs
	on fs.inmailID = tmp.inmailID
inner join TrialsLyris1.dbo.messages_ m
	on m.messageID_ = im.messageID_


delete fs
from @inmailmap as tmp
inner join dbo.inmailMessages as im
	on im.inmailID = tmp.inmailID
inner join dbo.inmailMesssageFullSource fs
	on fs.inmailID = tmp.inmailID
inner join dbo.messages_ m
	on m.messageID_ = im.messageID_

-- get messages we need to copy over
INSERT INTO @tmpMessages (body_, creatStamp_, HdrAll_, HdrFrom_, HdrFromSpc_, HdrSubject_, MessageID_, attachmentflag, ParentID_, list_, MemberID_,externalMemberID,Digested_, inmailID)
SELECT top 2500 m.Body_, m.CreatStamp_, m.HdrAll_, m.HdrFrom_, isnull(lm.emailaddr_,m.hdrfromspc_), m.HdrSubject_, m.MessageID_, m.attachmentflag, m.ParentID_, m.list_, m.MemberID_, lm.externalMemberID, m.Digested_, om.inmailID_
FROM TrialsLyris1.dbo.messages_ m
left outer join TrialsLyris1.dbo.members_ lm on lm.memberid_ = m.memberID_ and lm.list_ = m.list_
left outer join TrialsLyris1.dbo.outmail_ om on om.archiveMessageID_ = m.messageID_
WHERE m.MessageID_ > @maxid
AND m.MessageID_ <= @firstid
and m.MessageID_ not in (select messageid_ from lyrisarchive.dbo.messagesToArchive)
ORDER BY m.MessageID_

-- add new messages to digest clean up queue table
insert into TrialsLyris1.dbo.messageCleanerQueue (messageid_)
select messageid_
from @tmpMessages
where Digested_ = 'F'
except
select messageid_
from TrialsLyris1.dbo.messageCleanerQueue


-- handle list name lookup by getting all distinct listnames and 
-- making sure they are in the lookup table
insert into dbo.messageLists (list)
SELECT distinct tmp.list_
FROM @tmpMessages as tmp
LEFT OUTER JOIN dbo.messageLists as ml on ml.list = tmp.list_
WHERE ml.listid is null

-- put the listid in the temp messages table
update tmp
set tmp.listid = ml.listid
from @tmpMessages as tmp
inner join dbo.messageLists as ml on ml.list = tmp.list_

-- copy over the messages into messagesToArchive
insert into dbo.messagesToArchive (Body_, CreatStamp_, HdrAll_, HdrFrom_, HdrFromSpc_, HdrSubject_, MessageID_, attachmentflag, ParentID_, listID, externalMemberID)
SELECT Body_, CreatStamp_, HdrAll_, HdrFrom_, HdrFromSpc_, HdrSubject_, MessageID_, attachmentflag, ParentID_, listid, externalMemberID
FROM @tmpMessages
ORDER BY MessageID_


-- identify messages that have been parsed and uploaded
insert into @messagesToActivate (messageID_)
select messageID_
from messagesToArchive
where queueStatus = 'done'


--update searchtext for messagesToActivate that are already in messageSearchText (reprocessed messages)
if exists (select * FROM messagesToArchive ma inner join @messagesToActivate temp on temp.messageid_ = ma.messageid_ inner join messageSearchText m on ma.messageid_ = m.messageid_)
BEGIN
	update m set
		searchtext = dbo.fn_StripHTMLAndCompress(ma.searchText)
	FROM messagesToArchive ma
	inner join @messagesToActivate temp
		on temp.messageid_ = ma.messageid_
	inner join messageSearchText m
		on ma.messageid_ = m.messageid_


	delete ma
	FROM messagesToArchive ma
	inner join @messagesToActivate temp
		on temp.messageid_ = ma.messageid_
	inner join messageSearchText m
		on ma.messageid_ = m.messageid_

END

-- copy over the messages that have been parsed and uploaded
insert into dbo.messages_ (CreatStamp_, HdrFrom_, HdrFromSpc_, HdrSubject_, MessageID_, attachmentflag, ParentID_, listID, externalMemberID)
SELECT ma.CreatStamp_, ma.HdrFrom_, ma.HdrFromSpc_, ma.HdrSubject_, ma.MessageID_, ma.attachmentflag, ma.ParentID_, ma.listid, ma.externalMemberID
FROM messagesToArchive ma
inner join @messagesToActivate temp
	on temp.messageid_ = ma.messageid_
ORDER BY ma.MessageID_

insert into dbo.messageSearchText (MessageID_, searchText)
SELECT ma.MessageID_,  dbo.fn_StripHTMLAndCompress(ma.searchText)
FROM messagesToArchive ma
inner join @messagesToActivate temp
	on temp.messageid_ = ma.messageid_
ORDER BY ma.MessageID_

-- remove verified messages from messagesToArchive
delete ma
FROM messagesToArchive ma
inner join @messagesToActivate temp
	on temp.messageid_ = ma.messageid_

-- put messages into recent message ids
insert into dbo.RecentMessageIDs (MessageID_, CreatStamp_)
SELECT MessageID_, CreatStamp_
FROM @tmpMessages
ORDER BY MessageID_

GO
ALTER PROC dbo.lyris_removeMessage
@messageid_ int,
@filelocation varchar(200)

AS

SET NOCOUNT ON

-- queue deletion in S3
IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3
CREATE TABLE #tmpS3 (objectKey varchar(200))

DECLARE @sql varchar(max)
SELECT @sql = 'BULK INSERT #tmpS3 FROM ''' + @filelocation + ''' WITH (FIELDTERMINATOR = ''|'')'
EXEC(@sql)

INSERT INTO membercentral.platformQueue.dbo.queue_S3Delete (s3bucketName, objectKey)
SELECT 'messages.membercentral.com', objectKey
FROM #tmpS3

IF OBJECT_ID('tempdb..#tmpS3') IS NOT NULL 
	DROP TABLE #tmpS3


-- delete from lyris and lyrisarchive
delete from dbo.messagesToArchive
where messageid_ = @messageid_

delete from lyrisarchive.dbo.messages_ 
where messageid_ = @messageid_

delete from dbo.messageSearchText 
where messageid_ = @messageid_

delete from trialslyris1.dbo.messages_ 
where messageid_ = @messageid_


SET NOCOUNT OFF

RETURN 0
GO