USE trialslyris1
GO

ALTER PROC dbo.mc_updateListMemberships
@debugMode BIT = 0

AS

BEGIN TRY

	DECLARE @progressLog VARCHAR(max), @errorLog VARCHAR(max), @emailSubject VARCHAR(500), @emailTitle VARCHAR(300), @escalateError BIT,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno INT, @proc sysname, @lineno INT, 
		@defaultMembertype VARCHAR(100), @defaultSubType VARCHAR(100), @defaultMCOption_keepActive BIT, 
		@defaultMCOption_lockAddress BIT, @defaultMCOption_lockName BIT, @thisListName VARCHAR(100), @thisListAutoID INT,
		@thisListAutoManageActive BIT, @message VARCHAR(500), @lastrowcount INT, @thisListOneWayList BIT, @expireDateCutoff DATETIME,
		@runByMemberID INT;

	SET @escalateError = 0;
	SET @errorLog = '';
	SET @defaultMembertype = 'normal';
	SET @defaultSubType = 'mail';
	SET @defaultMCOption_keepActive = 0;
	SET @defaultMCOption_lockAddress = 0;
	SET @defaultMCOption_lockName = 0;
	SET @expireDateCutoff = DATEADD(year,-1,GETDATE());

	EXEC MEMBERCENTRAL.membercentral.dbo.lists_getListMembersForLyris;

	IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
		DROP TABLE #ListsForLyrisSync;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #ListsForLyrisSync (autoid INT IDENTITY(1,1), siteID INT, siteCode VARCHAR(10), orgID INT, orgCode VARCHAR(10), 
		list_ VARCHAR(100), isAutoManageActive BIT);
	CREATE TABLE #tmpLogMessages (autoid INT IDENTITY(1,1), siteID INT, memberID INT, listName VARCHAR(100), msg VARCHAR(500));

	TRUNCATE TABLE dbo.MC_ListMembersForLyris;

	INSERT INTO #ListsForLyrisSync (siteID, siteCode, orgID, orgCode, list_, isAutoManageActive)
	SELECT siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
	FROM membercentral.datatransfer.dbo.ListsForLyris
	ORDER BY orgcode, list_;

	-- delete lists that no longer exist in Lyris
	DELETE s
	FROM #ListsForLyrisSync s
	LEFT OUTER JOIN lists_ l
		on s.list_ = l.name_ COLLATE Latin1_General_CI_AI
    WHERE l.ListID_ IS NULL


	INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
		functionName, isAutoManageActive, domain_, usernameLC_)
	SELECT lmfl.siteID, lmfl.siteCode, lmfl.orgID, lmfl.orgCode, lmfl.list_, lmfl.externalMemberID, lmfl.MCMemberID, lmfl.emailaddr_, lmfl.fullname_, 
		lmfl.functionName, lfl.isAutoManageActive, RIGHT(lmfl.emailaddr_,LEN(lmfl.emailaddr_)-CHARINDEX('@',lmfl.emailaddr_)), 
		LEFT(lmfl.emailaddr_,CHARINDEX('@',lmfl.emailaddr_)-1)
	FROM membercentral.datatransfer.dbo.ListMembersForLyris lmfl
	INNER JOIN #ListsForLyrisSync lfl ON lfl.list_ = lmfl.list_;

	-- null blank emails
	UPDATE dbo.MC_ListMembersForLyris
	SET emailaddr_ = NULL
	WHERE ltrim(rtrim(ISNULL(emailaddr_,''))) = '';
	
	-- loop list by list
	SELECT @thisListAutoID = min(autoID) FROM #ListsForLyrisSync;
	WHILE @thisListAutoID IS NOT NULL BEGIN
		SELECT @thisListName = list_, @thisListAutoManageActive = isAutoManageActive
		FROM #ListsForLyrisSync 
		WHERE autoID = @thisListAutoID;

		IF exists (SELECT adminSend_ FROM dbo.lists_ WHERE name_ = @thisListName AND adminSend_ = 'T')
			SET @thisListOneWayList = 1;
		ELSE
			SET @thisListOneWayList = 0;

		SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Start Processing List changes - AutoManage: ' + cast(@thisListAutoManageActive AS VARCHAR(5));
		SET @progressLog = @progressLog + '<br/>' + @message;
			IF @debugMode = 1 RAISERROR(@message,0,1);

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding names to update';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET updateStatus = 'UpdateName'
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.fullname_ <> m.fullname_
				AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Name changed FROM ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				-- update the full names that have been marked
				UPDATE m 
				SET m.fullname_ = lm.fullname_
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated names - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 1)';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail' ELSE 'UpdateEmail' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
			LEFT OUTER JOIN dbo.members_ existingAddresses ON lm.list_ = existingAddresses.list_
				AND (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ AND existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			WHERE existingAddresses.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				IF @thisListAutoManageActive = 1 BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding records to update (Pass 2)';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET lm.updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail1' ELSE 'UpdateEmail1' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM MC_ListMembersForLyris 
				WHERE list_ = @thisListName 
				AND updateStatus IS NULL 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN members_ m ON lm.list_ = m.list_
				AND lm.list_ = @thisListName
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ IS NOT NULL
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN dbo.members_ existingAddresses ON lm2.list_ = existingAddresses.list_
					AND (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ AND existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				WHERE lm2.list_ = @thisListName
				AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM MC_ListMembersForLyris lm
					INNER JOIN members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				IF @thisListAutoManageActive = 1
				BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m 
						on lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);
					
					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding memberships to expire';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			SELECT l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				NULL AS functionName, 'expired' AS updateStatus, l.isAutoManageActive
			FROM #ListsForLyrisSync l
			INNER JOIN members_ m ON l.autoID = @thisListAutoID
				AND l.list_ = m.list_ COLLATE Latin1_General_CI_AI
				AND m.membertype_ in ('confirm','held','normal')
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
			LEFT OUTER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID COLLATE Latin1_General_CI_AI
				AND m.list_ = lm.list_ 
			WHERE lm.autoID IS NULL AND (m.MCOption_keepActive IS NULL or m.MCOption_keepActive=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				UPDATE m 
				SET m.membertype_ = 'expired',
					m.ExpireDate_ = GETDATE()
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'expired';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Expired members - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding reactivations';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm 
			SET lm.updateStatus = 'reactivate'
			FROM members_ m
			INNER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
				AND m.list_ = lm.list_
				AND m.list_ = @thisListName
				AND m.membertype_ = 'expired'
				AND lm.functionName in ('managePopulation','manageStatus');

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				UPDATE m 
				SET m.membertype_ = 'normal',
					m.ExpireDate_ = NULL
				FROM MC_ListMembersForLyris lm
				INNER JOIN members_ m ON lm.list_ = m.list_
					AND m.list_ = @thisListName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Reactivated memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding new memberships';
			SET @progressLog = @progressLog + '<br/>' + @message;
				IF @debugMode = 1 RAISERROR(@message,0,1);

			UPDATE lm
			SET lm.updateStatus = 'added'
			FROM 
				(
					SELECT min(autoID) AS autoID FROM MC_ListMembersForLyris WHERE list_ = @thisListName AND updateStatus IS NULL AND functionName in ('managePopulation') GROUP BY emailaddr_
				) deduped
				INNER JOIN MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					AND NULLif(lm.emailaddr_,'') IS NOT NULL
				LEFT OUTER JOIN members_ m
					on lm.list_ = m.list_
					AND (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = RIGHT(lm.emailaddr_,LEN(lm.emailaddr_)-CHARINDEX('@',lm.emailaddr_)) AND m.usernamelc_ = LEFT(lm.emailaddr_,CHARINDEX('@',lm.emailaddr_)-1))
					)
			WHERE m.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				FROM MC_ListMembersForLyris
				WHERE list_ = @thisListName
				AND updateStatus = 'added';

				INSERT INTO members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_)
				SELECT GETDATE() AS DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype AS membertype_, @defaultSubType AS subype_
				FROM MC_ListMembersForLyris lm
				WHERE lm.list_ = @thisListName
				AND lm.updateStatus = 'added';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Added new memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Finding old expired memberships to delete';
				SET @progressLog = @progressLog + '<br/>' + @message;
					IF @debugMode = 1 RAISERROR(@message,0,1);

				INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				SELECT l.siteID, l.siteCode, l.orgID, l.orgcode, l.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					NULL AS functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' AS updateStatus, l.isAutoManageActive
				FROM #ListsForLyrisSync l
				INNER JOIN members_ m ON l.autoID = @thisListAutoID
					AND l.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND m.membertype_ = 'expired'
					AND m.ExpireDate_ < @expireDateCutoff
					AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''

				SET @lastrowcount = @@rowcount;

				IF @lastrowcount > 0 AND @thisListAutoManageActive = 1 BEGIN
					DELETE m
					FROM MC_ListMembersForLyris lm
					INNER JOIN members_ m ON lm.list_ = m.list_
						AND m.list_ = @thisListName
						AND lm.externalMemberID = m.externalMemberID
						AND lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @thisListName + ': Expired members deleted - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
						IF @debugMode = 1 RAISERROR(@message,0,1);
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		SELECT @thisListAutoID = min(autoID) FROM #ListsForLyrisSync WHERE autoID > @thisListAutoID;
	END

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID AS VARCHAR(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID AS VARCHAR(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID AS VARCHAR(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END


	-- send email if there are members with no email address
	IF EXISTS (SELECT 1 FROM dbo.members_ WHERE EmailAddr_ = '') BEGIN
		SET @emailSubject = 'Lyris Members with blank email address';
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailSubject, @messageContent='There are records in dbo.members_ WHERE EmailAddr_ is an empty string.', @forDev=1;
	END
	
END TRY
BEGIN CATCH
	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	SET @escalateError = 1;
END CATCH

IF LEN(rtrim(ltrim(@errorLog))) > 0 BEGIN
	SET @errorLog = @errorLog + '<br/><br/>' + ISNULL(@progressLog,'');
	SET @emailSubject =  convert(varchar(19), GETDATE(), 121) + ' - MC ListSync Process: Errors Generated';
	SET @emailTitle =  'MC ListSync Process: Errors Generated';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailTitle, @messageContent=@errorLog, @forDev=1;

	SET @message =  convert(varchar(19), GETDATE(), 121) + ' : Sent Error Log Email';
		IF @debugMode = 1 RAISERROR(@message,0,1);
END

IF ( @escalateError = 1) BEGIN
	SET @message =  convert(varchar(19), GETDATE(), 121) + ' : Escalating Fatal Error';
	IF @debugMode = 1 RAISERROR(@message,0,1);

	RAISERROR (@errmsg, @severity, @state, @errno);
END

IF OBJECT_ID('tempdb..#ListsForLyrisSync') IS NOT NULL
	DROP TABLE #ListsForLyrisSync;
IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

RETURN 0;
GO

ALTER PROC dbo.job_runHourlyJobs
AS

DECLARE @errorSubject VARCHAR(100), @errorTitle VARCHAR(100), @errmsg nvarchar(2048), @proc sysname, @lineno INT;

/* ********************** */
/* enforce list settings  */
/* ********************** */
BEGIN TRY
	EXEC dbo.job_enforceListSettings
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )';
	SET @errorTitle = 'Error Running Adhoc queries to enforce list settings';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errmsg, @forDev=1;	
END CATCH


RETURN 0
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @proc sysname, @lineno INT;

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	DELETE top (50000) sd
	FROM lyrReportSummaryData sd
	INNER JOIN lists_ l ON sd.list = l.name_
		AND sd.created < DATEADD(day,-1 *KeepOutmailPostings_,GETDATE())
		AND KeepOutmailPostings_ <> 0;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to Clear old lyrReportSummaryData entries';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ********************** */
/* trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.natle_seminarWebLive';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;		
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.natle_justiceServices';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.ts_membercentraladmins';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.ky_listServices';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=0;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running trialslyris1.dbo.mc_updateListMemberships';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Make sure EClips Sending Addresses are setup correctly */
/* ********************** */
BEGIN TRY
	UPDATE dbo.members_ 
	set ExternalMemberID = NULL,
		ExpireDate_ = NULL,
		membertype_ = 'normal',
		subtype_ = 'nomail',
		IsListAdm_ = 'T',
		fullname_ = 'TrialSmith EClips Sending Address - DO NOT DELETE'
	WHERE EmailAddr_ = '<EMAIL>';
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error making sure EClips Sending Addresses are setup correctly';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    UPDATE om 
	set title_ = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title_,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title_,70)
	    end
    FROM dbo.outmail_ om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID_
	    AND om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title_ not like '%|%';

    -- update mailings in needs approval
    UPDATE om 
	set title_ = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title_,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title_,70)
	    end
    FROM dbo.moderate_ om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID_
	    AND om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title_ not like '%|%';

    -- update mailings in summary data table
    UPDATE om 
	set title = 
	    CASE WHEN s.type_ = 'triggered' THEN LEFT('Triggered | ' + om.title,70)
		    ELSE LEFT(upper(s.name_) + ' | ' + om.title,70)
	    end
    FROM dbo.lyrReportSummaryData om
    INNER JOIN subsets_ s
	    ON s.SubsetID_ = om.SubsetID
	    AND om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    AND om.Title not like '%|%';
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to add segment name to trialsmith marketing list';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Delete orphaned list members */
/* ********************** */
BEGIN TRY
	DELETE m
	FROM members_ m
	LEFT OUTER JOIN lists_ l on l.name_ = m.list_
	WHERE l.ListID_ IS NULL;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error deleting orphaned list members from lists';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Ensure proper digest format in in place for members */
/* ********************** */
BEGIN TRY 
	EXEC lyrisCustom.dbo.lists_syncListDigestSettings;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.lists_syncListDigestSettings';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ******************* */
/* Queue List Digest   */
/* ******************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=0;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.list_queueDigest';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************** */
/* Queue List Digest  Test Mode */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=1;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.list_queueDigest testmode';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

/* ********************************* */
/* Log Daily Sends per Lyris ListID  */
/* ********************************* */
BEGIN TRY
	DECLARE @reportDate date = DATEADD(DAY,-1,GETDATE());
	EXEC lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay @reportDate=@reportDate;
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running lyrisCustom.dbo.lyris_populateMCRecipientsByListByDay';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END CATCH

RETURN 0
GO

USE lyrisCustom
GO

ALTER PROC dbo.ts_membercentraladmins

AS

DECLARE @errorSubject VARCHAR(100), @errmsg VARCHAR(200), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'membercentraladmins'

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
CREATE TABLE #tempListMembers (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, membernumber VARCHAR(100), 
	fullname VARCHAR(500), email VARCHAR(200), usernameLC_ VARCHAR(100), domain_ VARCHAR(150));

EXEC membercentral.customApps.dbo.ts_membercentraladminsEligible;

IF NOT EXISTS (SELECT * FROM membercentral.dataTransfer.dbo.ts_membercentraladminsEligible) BEGIN
	SET @errorSubject = 'Error Updating MemberCentralAdmins List';
	SET @errmsg = 'customApps.dbo.ts_membercentraladminsEligible ended with no rows in table dataTransfer.dbo.ts_membercentraladminsEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END ELSE BEGIN

	INSERT INTO #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM membercentral.dataTransfer.dbo.ts_membercentraladminsEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	UPDATE #tempListMembers 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- mark email addresses that are NOT in temp table as expired
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ AS m  
	LEFT OUTER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ AS m 
	INNER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName
	AND m.membertype_ = 'expired';

	-- update email addresses/fullname based on matching membernumber and association
	UPDATE m 
	SET domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	FROM #tempListMembers AS tmp
	INNER JOIN trialslyris1.dbo.members_ AS m ON m.list_ = @listName
		AND m.association_ = tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.ExternalMemberID = tmp.memberNumber COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.emailaddr_ <> tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS)
		AND m.membertype_ = 'normal'
	LEFT OUTER JOIN trialslyris1.dbo.members_ AS prexistingEmail ON prexistingEmail.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND prexistingEmail.list_ = @listName
		AND prexistingEmail.memberID_ <> m.memberID_
	WHERE prexistingEmail.memberID_ IS NULL;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #tempListMembers ec
	WHERE email in (
		SELECT emailaddr_ COLLATE SQL_Latin1_General_CP1_CI_AS
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
	);

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber AS ExternalMemberID, orgcode
	FROM #tempListMembers;
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

ALTER PROC dbo.trialsmith_updateMarketingLists
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	CREATE TABLE #memberPool (poolid INT identity(1,1), DateJoined_ DATETIME, domain_ VARCHAR(250), emailaddr_ VARCHAR(100),
		fullname_ VARCHAR(100), list_ VARCHAR(60), usernameLc_ VARCHAR(100), ExternalMemberID VARCHAR(100),
		association_ VARCHAR(10), depomemberdataid INT);
	CREATE TABLE #updatedMembers (id INT identity(1,1), poolid INT, memberID_ INT);
	CREATE TABLE #membershipsToDelete (id INT identity(1,1), memberid_ INT);
	CREATE TABLE #unsubs (id INT identity(1,1), emailaddr_ VARCHAR(100));

	DECLARE @errorSubject VARCHAR(100), @errmsg varchar(200);
	
	EXEC membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

	IF NOT EXISTS (SELECT emailaddr_ FROM membercentral.transfer.dbo.trialsmithMarketingListPopulation) BEGIN
		SET @errorSubject = 'Error Updating TrialSmith Marketing Lists';
		SET @errmsg = 'trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
	END ELSE BEGIN
		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

		INSERT INTO #unsubs (emailaddr_)
		SELECT emailaddr_
		FROM trialslyris1.dbo.members_ m 
		WHERE list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ = 'unsub';

		INSERT INTO #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
		SELECT lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
		FROM membercentral.transfer.dbo.trialsmithMarketingListPopulation lp
		LEFT OUTER JOIN #unsubs u ON u.emailaddr_ = lp.emailaddr_ COLLATE Latin1_General_CI_AI
		WHERE u.emailaddr_ IS NULL;

		-- delete subscribed members with email addresses that are no longer in the pool
		INSERT INTO #membershipsToDelete (memberID_)
		SELECT m.memberID_
		FROM trialslyris1.dbo.members_ m
		LEFT OUTER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND pool.poolid IS NULL 
		AND m.membertype_ <> 'unsub';

		DELETE m
		FROM trialslyris1.dbo.members_ m
		INNER JOIN #membershipsToDelete md ON m.memberid_ = md.memberid_;

		-- update
		INSERT INTO #updatedMembers (poolid, memberid_)
		SELECT pool.poolid, m.memberID_
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
			AND (
					m.emailaddr_ <> pool.emailaddr_ COLLATE Latin1_General_CI_AI
					or m.fullname_ <> pool.fullname_ COLLATE Latin1_General_CI_AI
					or m.ExternalMemberID <> pool.ExternalMemberID COLLATE Latin1_General_CI_AI
					or m.association_ <> pool.association_ COLLATE Latin1_General_CI_AI
					or ISNULL(m.depomemberdataID,0) <> ISNULL(pool.depomemberdataid,0)
			)
			AND m.list_ in ('trialsmith','trialsmith_subscribers')
			AND m.membertype_ <> 'unsub';

		UPDATE m 
		SET DateJoined_ = pool.DateJoined_,
			fullname_= pool.fullname_,
			list_= pool.list_,
			ExternalMemberID = pool.ExternalMemberID,
			association_ = pool.association_,
			depomemberdataid = pool.depomemberdataid
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #updatedMembers updated ON m.memberid_ = updated.memberid_
		INNER JOIN #memberPool pool ON updated.poolid = pool.poolid
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ <> 'unsub';

		-- delete all preexisting memberships from pool, leaving only entries that need to be created
		DELETE pool
		FROM #memberPool pool
		INNER JOIN trialslyris1.dbo.members_ m WITH(NOLOCK) ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers');

		-- insert new memberships
		INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ ,
			ExternalMemberID, association_, depomemberdataid, mcemailkey)
		SELECT DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ , ExternalMemberID, association_,
			depomemberdataid, mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernamelc_ + '@' + domain_),2)
		FROM #memberPool;

		-- update trialsmithUsage
		truncate table trialslyris1.dbo.tsdata
		INSERT INTO trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, 
			expList, numBadSrc, subType, expires)
		SELECT distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, 
			numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, 
			last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
		FROM membercentral.transfer.dbo.trialsmithMarketingListPopulation
		WHERE depomemberdataid IS NOT NULL;

		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith', 'trialsmith_subscribers';
	END

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.natle_seminarWebLive

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'seminarweblive';

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
CREATE TABLE #swl_eligibleForNatleMarketingList (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, 
	membernumber VARCHAR(100), fullname VARCHAR(100), email VARCHAR(100), usernameLC_ VARCHAR(100), 
	domain_ VARCHAR(250));

EXEC membercentral.customApps.dbo.natle_seminarWebLiveEligible;

IF NOT EXISTS (SELECT * FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = 'Error Updating SeminarWebLive Marketing List for NATLE';
	SET @errmsg = 'customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END ELSE BEGIN

	INSERT INTO #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	UPDATE #swl_eligibleForNatleMarketingList 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- update fullname/association based on matching email address
	UPDATE m 
	SET association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	FROM #swl_eligibleForNatleMarketingList tmp
	INNER JOIN trialslyris1.dbo.members_ m ON m.list_ = @listName
		AND m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.association_ <> tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ m 
	LEFT OUTER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND isListAdm_ <> 'T'
	AND m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ m 
	INNER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.list_ = @listName
		AND m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #swl_eligibleForNatleMarketingList ec
	WHERE exists (
		SELECT usernameLc_, domain_
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
		AND usernameLc_ = ec.usernameLc_ COLLATE SQL_Latin1_General_CP1_CI_AS
		AND domain_ = ec.domain_ COLLATE SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	SELECT *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) AS rowNum
	INTO #swl_eligibleForNatleMarketingList2
	FROM #swl_eligibleForNatleMarketingList;

	DELETE FROM #swl_eligibleForNatleMarketingList2
	WHERE rowNum > 1;

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode
	FROM #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;

GO

ALTER PROC dbo.natle_justiceServices

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'natle_justiceservices';

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
CREATE TABLE #tempListMembers (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, membernumber VARCHAR(100), 
	fullname VARCHAR(500), email VARCHAR(200), usernameLC_ VARCHAR(100), domain_ VARCHAR(150));

EXEC membercentral.customApps.dbo.natle_justiceServicesEligible;

IF NOT EXISTS (SELECT * FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible) BEGIN
	SET @errorSubject = 'Error Updating NATLE Justice Services List';
	SET @errmsg = 'customApps.dbo.natle_justiceServicesEligible ended with no rows in table dataTransfer.dbo.natle_justiceServicesEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
END ELSE BEGIN

	INSERT INTO #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM tlasites.dataTransfer.dbo.natle_justiceServicesEligible;

	CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

	UPDATE #tempListMembers 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- mark email addresses that are NOT in temp table as expired
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ AS m  
	LEFT OUTER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND m.membertype_ in ('normal','held')
	AND m.association_ NOT IN ('CT');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ AS m 
	INNER JOIN #tempListMembers AS tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName
	AND m.membertype_ = 'expired'
	AND m.association_ NOT IN ('CT');

	-- update email addresses/fullname based on matching membernumber and association
	UPDATE m 
	SET domain_ = tmp.domain_,
		usernameLC_ = tmp.usernameLC_,
		emailaddr_ = tmp.email,
		fullname_ = tmp.fullname
	FROM #tempListMembers AS tmp
	INNER JOIN trialslyris1.dbo.members_ AS m ON m.list_ = @listName
		AND m.association_ = tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.ExternalMemberID = tmp.memberNumber COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.emailaddr_ <> tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS)
		AND m.association_ NOT IN ('CT')
		AND m.membertype_ = 'normal'
	LEFT OUTER JOIN trialslyris1.dbo.members_ AS prexistingEmail ON prexistingEmail.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND prexistingEmail.list_ = @listName
		AND prexistingEmail.memberID_ <> m.memberID_
	WHERE prexistingEmail.memberID_ IS NULL;

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #tempListMembers ec
	WHERE email in (
		SELECT emailaddr_ COLLATE SQL_Latin1_General_CP1_CI_AS
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
	);

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber AS ExternalMemberID, orgcode
	FROM #tempListMembers
	WHERE orgcode NOT IN ('CT');
END

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers;
GO

ALTER PROC dbo.job_runDailyMaintenanceChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @errorSubject VARCHAR(300), @errmsg VARCHAR(max), @tableHTML VARCHAR(MAX), @tier VARCHAR(12);
	
	SET @tier = 'PRODUCTION'
	IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
		SET @tier = 'DEVELOPMENT'
	END
	IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
		SET @tier = 'BETA'
	END
	
	/* Databases with fullbackups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;

			DECLARE @fullbackupCheck_now DATETIME = GETDATE()
			DECLARE @fullbackupCheck_defaultOldestAllowedDate DATETIME = DATEADD(day,-1,@fullbackupCheck_now)
			DECLARE @fullbackupCheck_oneMonthAgo DATETIME = DATEADD(month,-1,@fullbackupCheck_now)
			DECLARE @fullbackupCheck_firstsaturdayThisMonth DATETIME, @fullbackupCheck_firstsaturdayLastMonth DATETIME
			
			SELECT @fullbackupCheck_firstsaturdayThisMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_now),0))
			SELECT @fullbackupCheck_firstsaturdayLastMonth  = DATEADD(dd,(14 - @@DATEFIRST - DATEPART(dw,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),-1)))%7,DATEADD(month,DATEDIFF(mm,0,@fullbackupCheck_oneMonthAgo),0))

			DECLARE @fullbackupCheck_oldestDateOverrides TABLE (database_name VARCHAR(100), warningDate DATETIME) 

			--trialslyris1 full backups are twice a week .... should also be one within last 4 days
			INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('trialslyris1',DATEADD(day,-4,GETDATE()))

			-- lyrisarchive fullbacks are the first saturday of the month
			-- if been at least 1 day since midnight of first saturday of month use first this Saturday, otherwise use last month's first Saturday
			IF (DATEDIFF(day,@fullbackupCheck_firstsaturdayThisMonth,@fullbackupCheck_now) >= 1 )
				INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('lyrisarchive',@fullbackupCheck_firstsaturdayThisMonth)
			ELSE 
				INSERT INTO @fullbackupCheck_oldestDateOverrides (database_name,warningDate) VALUES ('lyrisarchive',@fullbackupCheck_firstsaturdayLastMonth)

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Full Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					LEFT OUTER JOIN @fullbackupCheck_oldestDateOverrides o
						on o.database_name = bs.database_name COLLATE Latin1_General_CI_AI
					WHERE bs.type = 'D'
					GROUP BY bs.database_name, o.warningDate
					HAVING MAX(bs.backup_finish_date) < CASE WHEN o.warningDate IS NOT NULL THEN o.warningDate ELSE @fullbackupCheck_defaultOldestAllowedDate end
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');


			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with full backups behind schedule';
				SET @errmsg = @errorSubject + '<br/><br/>These are the databases where the full backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;

				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
					
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with diff backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			DECLARE @backupcheck_twodaysago DATETIME = DATEADD(hour,-2,GETDATE())

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Diff Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					WHERE bs.type = 'I'
					GROUP BY bs.database_name
					HAVING MAX(bs.backup_finish_date) < @backupcheck_twodaysago
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');

			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with full backups behind schedule';
				SET @errmsg = @errorSubject + '<br/><br/>These are the databases where the diff backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;
				
				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END

	/* Databases with log backups behind schedule*/
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			DECLARE @backupcheck_twohoursago DATETIME = DATEADD(minute,-2,GETDATE())

			SET @tableHTML = '' + 
				REPLACE(Stuff((
					SELECT '|' + bs.database_name + ' - Last Log Backup: ' + CONVERT(VARCHAR(19), MAX(bs.backup_finish_date), 120) AS [text()]
					FROM msdb.dbo.backupset bs
					WHERE bs.type = 'L' AND bs.recovery_model = 'FULL'
					GROUP BY bs.database_name, recovery_model
					HAVING MAX(bs.backup_finish_date) < @backupcheck_twohoursago
					FOR XML PATH ('')
				),1,1,''),'|','<br/>');

			IF LEN(@tableHTML) > 1 BEGIN
				SET @errorSubject = 'Databases with log backups behind schedule';
				SET @errmsg =  @errorSubject + '<br/><br/>These are the databases (full recovery model only) where the log backups appear to be behind schedule. Please ensure that the backup job is running.<br/><br/>' + @tableHTML;
				
				EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;

			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END




	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_emailWhenFailedDigests

AS

DECLARE @badlist VARCHAR(60), @message VARCHAR(1000), @errorSubject VARCHAR(300);
EXEC dbo.checkForFailedDigests @dateoflastdigest=NULL, @daysToLookBack=1, @fixList=0, @badlist=@badlist OUTPUT;

IF @badlist IS NOT NULL BEGIN
	
	SET @message = 'Possible Failed Lyris Digests with list '+ @badlist;
	SET @errorSubject = 'Possible Failed Lyris Digests';
	
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@message, @forDev=1;

END
GO