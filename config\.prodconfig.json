{"CGIReadOnly": "true", "adminSalt": "C3686DBB-9B51-456E-99AE6EC99903758F", "applicationListener": "mixed", "applicationMode": "curr2root", "applicationTimeout": "1,0,0,0", "bufferTagBodyOutput": "true", "clientCookies": "true", "clientManagement": "false", "clientStorage": "cookie", "clientTimeout": "90,0,0,0", "compression": "false", "CFMappings": {"/mc": {"PHYSICAL": "/app/membercentral"}, "/SemWebSWODPlayer": {"PHYSICAL": "/app/membercentral/model/seminarweb"}, "/logs": {"PHYSICAL": "/app/logs"}}, "datasources": {"customApps": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=customApps&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "customApps", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "datatransfer": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=datatransfer&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "datatransfer", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "lyrisarchive": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=lyrisarchive&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "lyrisarchive", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sa", "validate": "false"}, "mailarchive": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=mailarchive&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "mailarchive", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sa", "validate": "false"}, "membercentral": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=membercentral&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "membercentral", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformMail": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformMail&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformMail", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformQueue": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformQueue&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformQueue", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "platformstatsMC": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformstatsMC&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformstatsMC", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "searchMC": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=searchMC&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "searchMC", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_formbuilder": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=formbuilder&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "formbuilder", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_platformstats": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=platformstats&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "platformstats", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_search": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=search&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "search", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_seminarweb": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=seminarweb&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "seminarweb", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "tlasites_trialsmith": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=trialsmith&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "trialsmith", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "***********", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sqladmin", "validate": "false"}, "trialslyris1": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=trialslyris1&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "trialslyris1", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sa", "validate": "false"}, "lyrisCustom": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=lyrisCustom&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "lyrisCustom", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sa", "validate": "false"}, "EmailTracking": {"allowAlter": true, "allowCreate": true, "allowDelete": true, "allowDrop": true, "allowGrant": true, "allowInsert": true, "allowRevoke": true, "allowSelect": true, "allowUpdate": true, "blob": "true", "class": "com.microsoft.jdbc.sqlserver.SQLServerDriver", "clob": "true", "connectionTimeout": "1", "custom": "DatabaseName=EmailTracking&sendStringParametersAsUnicode=true&SelectMethod=direct", "database": "EmailTracking", "dbdriver": "MSSQL", "dsn": "jdbc:sqlserver://{host}:{port}", "host": "************", "metaCacheTimeout": "60000", "password": "w@y2go1dah0", "port": "1433", "storage": "false", "username": "sa", "validate": "false"}}, "dotNotationUpperCase": "true", "hspw": "370309a6015d633c1a738cdb79826f79a3dd8699be8ffe47bee65446cb4ab6f3", "inspectTemplate": "once", "localScopeMode": "classic", "mailServers": [{"idleTimeout": "60000", "lifeTimeout": "300000", "password": "", "port": "25", "smtp": "***********", "ssl": "false", "tls": "false", "username": ""}], "mergeURLAndForm": "false", "nullSupport": "false", "requestTimeout": "0,0,1,0", "requestTimeoutEnabled": true, "requestTimeoutInURL": "false", "scopeCascading": "standard", "scriptProtect": "all", "searchResultsets": "true", "sessionMangement": "true", "sessionStorage": "memory", "sessionTimeout": "0,0,5,0", "sessionType": "application", "suppressWhitespaceBeforecfargument": "true", "supressContentForCFCRemoting": "false", "templateCharset": "windows-1252", "thisLocale": "en_US", "thisTimeZone": "America/Chicago", "timeServer": "pool.ntp.org", "useTimeServer": "false", "whitespaceManagement": "white-space-pref"}