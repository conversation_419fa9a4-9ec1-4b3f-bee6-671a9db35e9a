use trialslyris1;
GO
ALTER PROC [dbo].[job_updateList-natle_justiceServices]
AS

DECLARE @defaultMembertype varchar(100), @defaultSubType varchar(100)
DECLARE @listName varchar(100), @now datetime

IF OBJECT_ID('tempdb..#tempListMembers') IS NOT NULL
	DROP TABLE #tempListMembers

create TABLE #tempListMembers (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(500), 
	email varchar(200),
	usernameLC_ varchar(100),
	domain_ varchar(150)
)


set @now = getdate()
set @defaultMembertype = 'normal'
set @defaultSubType = 'mail'
set @listName = 'natle_justiceservices'

exec membercentral.customApps.dbo.natle_getEligibleForJusticeServicesList

insert into #tempListMembers (platform, orgcode, memberID, membernumber, fullname, email)
select platform, orgcode, memberID, membernumber, fullname, email
from membercentral.dataTransfer.dbo.natle_eligibleForJusticeServicesList



CREATE INDEX IX_tempListMembers_email ON #tempListMembers (email asc);

update #tempListMembers 
set usernameLC_ = left(email,charindex('@',email)-1),
	domain_ = right(email,len(email)-charindex('@',email))


-- update email addresses/fullname based on matching membernumber and association
update m set
    domain_ = tmp.domain_,
	usernameLC_ = tmp.usernameLC_,
    emailaddr_ = tmp.email,
    fullname_ = tmp.fullname
from #tempListMembers tmp
inner join members_ m
    on m.list_ = @listName
    and m.association_ = tmp.orgcode collate SQL_Latin1_General_CP1_CI_AS
    and m.ExternalMemberID = tmp.memberNumber collate SQL_Latin1_General_CP1_CI_AS
    and (m.emailaddr_ <> tmp.email collate SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname collate SQL_Latin1_General_CP1_CI_AS)
    and m.association_ not in ('CT')
left outer join members_ prexistingEmail
    on prexistingEmail.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
    and prexistingEmail.list_ = @listName
    and prexistingEmail.memberID_ <> m.memberID_
where prexistingEmail.memberID_ is null

-- mark email addresses that are NOT in temp table as expired
update m set
    membertype_ = 'expired',
    ExpireDate_ = @now
from members_ m 
left outer join #tempListMembers tmp
    on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
where m.list_ = @listName 
    and tmp.email is null
    and m.membertype_ in ('normal','held')
    and m.association_ not in ('CT')

-- reactivate previously expired email addresses that are in temp table
update m set
    membertype_ = 'normal',
    ExpireDate_ = null
from members_ m 
inner join #tempListMembers tmp
    on m.emailaddr_ = tmp.email collate SQL_Latin1_General_CP1_CI_AS
    and m.list_ = @listName
    and m.membertype_ = 'expired'
    and m.association_ not in ('CT')

-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
delete ec
from #tempListMembers ec
where email in (
	select emailaddr_ collate SQL_Latin1_General_CP1_CI_AS
	from dbo.members_
	where list_ = @listname 
) 

insert into members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_)
select @now,domain_,email,fullname,@listName ,usernameLc_ ,memberNumber as ExternalMemberID,orgcode
from #tempListMembers
where orgcode not in ('CT')

GO