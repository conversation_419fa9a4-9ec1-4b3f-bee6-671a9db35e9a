use lyrisarchive
GO

ALTER PROC dbo.lists_listMessagePerYearReportExport
@orgcode varchar(5),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport

select *
into ##tmpListPerYearExport
from (
	select ml.list, year(creatStamp_) as year, count(*) as messageCount
	from messages_ m 
	inner join dbo.messageLists ml on m.listID = ml.listID
	inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI
		and lf.orgcode = @orgcode
	group by lf.orgcode, ml.list, year(creatStamp_)
) as rawdata
PIVOT (sum(messageCount) for year in ([1998],[1999],[2000],[2001],[2002],[2003],[2004],[2005],[2006],[2007],[2008],[2009],[2010],[2011],[2012],[2013],[2014],[2015],[2016],[2017])) as pivottable

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport order by 1, 2'

IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport
GO

ALTER PROCEDURE dbo.up_exportCSV
@csvfilename varchar(400),
@sql varchar(max)

AS

-- drop the temp tables
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol

-- run query into temp table
select @sql = stuff(@sql, charIndex('from',@sql), len('from'), 'into ##tmpCSVExport from')
EXEC(@sql)

-- export as chr(31) column delimiter and chr(30) row delimiter
DECLARE @cmd varchar(7000)
set @cmd = 'bcp ##tmpCSVExport out "' + @csvfilename + '" -c -t0x1F -r0x1E -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- read in file to replace beeps with commas and escape quotes and replace nulls with empty string
declare @tmpFile varchar(max), @trash bit
select @tmpFile = dbo.fn_ReadFile(@csvfilename,0,0)
if len(@tmpFile) > 0 BEGIN
	select @tmpFile = replace(left(@tmpFile,len(@tmpFile)-1),'"','""') -- escape all quotes
	select @tmpFile = replace(replace(@tmpFile,char(13),' '),char(10),' ') -- replace cr and lf with space
	select @tmpFile = replace(@tmpFile COLLATE SQL_Latin1_General_CP1_CS_AS,char(0),'')		-- replace nulls with empty string
	select @tmpFile = replace(@tmpFile,char(31),'","')	-- replace 31 with column separator
	select @tmpFile = replace(@tmpFile,char(30),'"' + char(13) + char(10) + '"') -- replace 30 with crlf
	select @tmpFile = '"' + @tmpFile + '"'
end

-- record 1st row as field names and save file
CREATE TABLE #tmpCol (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)
INSERT INTO #tmpCol
EXEC tempdb.dbo.SP_COLUMNS ##tmpCSVExport
declare @colList varchar(max)
select @colList = COALESCE(@colList + ',', '') + '"' + column_name + '"'
	from #tmpCol 
	order by ORDINAL_POSITION
select @trash = dbo.fn_WriteFile(@csvfilename,@colList + char(13) + char(10) + @tmpFile,1)

-- get fields returned
SELECT COLUMN_NAME 
FROM #tmpCol
order by ORDINAL_POSITION

-- drop temp tables
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
GO