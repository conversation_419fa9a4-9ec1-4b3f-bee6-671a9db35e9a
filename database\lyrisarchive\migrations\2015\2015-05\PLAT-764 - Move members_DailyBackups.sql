use trialslyris1
GO
ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'MCDEV01\TLASITES' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\TLASITES' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* enforce list settings */
/* ********************** */
BEGIN TRY
	-- turn off join by email for all lists
	update lists_
	set NoEmailSub_ = 'T'
	where NoEmailSub_ <> 'T'

	-- force all lists to only allow admins to add members
	update lists_ 
	set security_ = 'private'
	where security_ = 'open' and name_ not in ('eclips_js','brandigy')

	-- set all lists to invisible in Discussion Forum Interface
	update lists_ 
	set MriVisibility_ = 'I'
	where MriVisibility_ <> 'I'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	declare @backupDate datetime;
	select @backupDate = getdate()

	insert into lyrisMembersBackup.dbo.members_DailyBackup (backupDate, Additional_, AppNeeded_, CanAppPend_, CleanAuto_, Comment_, ConfirmDat_, DateBounce_, DateHeld_, DateJoined_, DateUnsub_, Domain_, EmailAddr_, ExpireDate_, FullName_, IsListAdm_, List_, MemberID_, MemberType_, NoRepro_, NotifyErr_, NotifySubm_, NumAppNeed_, NumBounces_, Password_, RcvAdmMail_, ReceiveAck_, SubType_, UserID_, UserNameLC_, ReadsHtml_, MailFormat_, PermissionGroupID_, UnsubMessageID_, EnableWYSIWYG_, SyncCanResubscribe_, SyncResubscribeType_, Senator, SD, HD, Representative, Age_, Association_, City_, Company_, County_, Fax_, Gender_, MemberLevel_, PassKey_, PostalCode_, RenewLink_, StateProvince_, SubscriberType_, Telephone_, Username_, Text1_, Text2_, Text3_, Text4_, Text5_, Numeric1_, Numeric2_, Numeric3_, Prefix_, Firstname_, Middlename_, Lastname_, Suffix_, ProfSuffix_, Website_, Address1_, Address2_, Address3_, AssocMemberID_, AssocShortName_, AssocLongName_, nickname_, areaofpractice1_, areaofpractice2_, Firm_, WorkPhone_, areaofpractice3_, DepoMemberDataID, OrgMemberDataID, JoinDate_, Status_, Campaign_, District_, CongressionalDistrict_, Shortname_, SiteLink_, ExternalMemberID, contactposition_, MemberStatus_, MeetingNumber_, BarDate_, Legislative_, Link_, MCOption_lockAddress, MCOption_keepActive)
	select @backupDate, Additional_, AppNeeded_, CanAppPend_, CleanAuto_, Comment_, ConfirmDat_, DateBounce_, DateHeld_, DateJoined_, DateUnsub_, Domain_, EmailAddr_, ExpireDate_, FullName_, IsListAdm_, List_, MemberID_, MemberType_, NoRepro_, NotifyErr_, NotifySubm_, NumAppNeed_, NumBounces_, Password_, RcvAdmMail_, ReceiveAck_, SubType_, UserID_, UserNameLC_, ReadsHtml_, MailFormat_, PermissionGroupID_, UnsubMessageID_, EnableWYSIWYG_, SyncCanResubscribe_, SyncResubscribeType_, Senator, SD, HD, Representative, Age_, Association_, City_, Company_, County_, Fax_, Gender_, MemberLevel_, PassKey_, PostalCode_, RenewLink_, StateProvince_, SubscriberType_, Telephone_, Username_, Text1_, Text2_, Text3_, Text4_, Text5_, Numeric1_, Numeric2_, Numeric3_, Prefix_, Firstname_, Middlename_, Lastname_, Suffix_, ProfSuffix_, Website_, Address1_, Address2_, Address3_, AssocMemberID_, AssocShortName_, AssocLongName_, nickname_, areaofpractice1_, areaofpractice2_, Firm_, WorkPhone_, areaofpractice3_, DepoMemberDataID, OrgMemberDataID, JoinDate_, Status_, Campaign_, District_, CongressionalDistrict_, Shortname_, SiteLink_, ExternalMemberID, contactposition_, MemberStatus_, MeetingNumber_, BarDate_, Legislative_, Link_, MCOption_lockAddress, MCOption_keepActive
	from trialslyris1.dbo.members_

	declare @30daysago datetime
	set @30daysago = dateadd(d,-30,getdate())

	delete from lyrisMembersBackup.dbo.members_DailyBackup
	where backupdate < @30daysago
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to backup lyris members table to lyrisarchive'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC dbo.[trialsmith_updateMarketingLists]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* swl_updateMarketingList-Natle */
/* ********************** */

BEGIN TRY
	EXEC dbo.[swl_updateMarketingList-Natle]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.swl_updateMarketingList-Natle'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* job_updateList-natle_justiceServices */
/* ********************** */

BEGIN TRY
	EXEC dbo.[job_updateList-natle_justiceServices]
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.job_updateList-natle_justiceServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH



/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
    -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices')
	    and om.Title not like '%|%'


END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO