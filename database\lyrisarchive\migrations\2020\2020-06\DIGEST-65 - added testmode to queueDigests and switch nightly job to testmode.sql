use lyrisCustom;
GO
ALTER PROC dbo.list_queueDigest

@isTestMode bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyToProcessStatusID INT, @digestDate DATETIME, @digestDateStart DATETIME, @digestDateEnd DATETIME;
	DECLARE @listsWithMCThreadIndexRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(5));
	DECLARE @listsWithMCThreadDigestRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(5));

	SET @digestDate = DATEADD(D,-1,GETDATE());
	SET @digestDateStart = cast(@digestDate as date);
	SET @digestDateEnd = DATEADD(MS,-3,DATEADD(d,1,@digestDateStart));

	SELECT @queueTypeID = queueTypeID
	FROM memberCentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'listDigests';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM memberCentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'ReadyToProcess';


	IF @isTestMode = 0 BEGIN

		INSERT INTO @listsWithMCThreadIndexRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadIndex = 1
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadIndex = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		INSERT INTO @listsWithMCThreadDigestRecipients(list)
		select list_
		from trialslyris1.dbo.members_ m
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = m.list_ collate Latin1_General_CI_AI
			and m.receiveMCThreadDigest = 1
		inner join membercentral.membercentral.dbo.sites s 
			on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
		inner join membercentral.membercentral.dbo.lists_lists l
			on l.listName = lf.name collate Latin1_General_CI_AI
			and l.supportsMCThreadDigest = 1
		inner join membercentral.membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = l.siteResourceID
			and sr.siteResourceStatusID=1
			group by list_;

		END ELSE BEGIN

			INSERT INTO @listsWithMCThreadIndexRecipients(list)
			select lf.name
			from trialslyris1.dbo.lists_format lf 
			inner join membercentral.membercentral.dbo.sites s 
				on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
			inner join membercentral.membercentral.dbo.lists_lists l
				on l.listName = lf.name collate Latin1_General_CI_AI
				and l.supportsMCThreadIndex = 1
			inner join membercentral.membercentral.dbo.cms_siteResources sr
				on sr.siteResourceID = l.siteResourceID
				and sr.siteResourceStatusID=1
				group by lf.name;

			INSERT INTO @listsWithMCThreadDigestRecipients(list)
			select lf.name
			from trialslyris1.dbo.lists_format lf 
			inner join membercentral.membercentral.dbo.sites s 
				on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
			inner join membercentral.membercentral.dbo.lists_lists l
				on l.listName = lf.name collate Latin1_General_CI_AI
				and l.supportsMCThreadDigest = 1
			inner join membercentral.membercentral.dbo.cms_siteResources sr
				on sr.siteResourceID = l.siteResourceID
				and sr.siteResourceStatusID=1
				group by lf.name;
		END

	UPDATE t 
	SET t.orgcode = lf.orgcode
	FROM @listsWithMCThreadIndexRecipients t
	INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
		AND lf.disabled = 0;

	UPDATE t 
	SET t.orgcode = lf.orgcode
	FROM @listsWithMCThreadDigestRecipients t
	INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
		AND lf.disabled = 0;

	-- populate platformQueue table
	INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
	select l.orgcode, ml.list, 'ListThreadIndex', @digestDate, @readyToProcessStatusID, @isTestMode
	from @listsWithMCThreadIndexRecipients l
	inner join lyrisarchive.dbo.messageLists ml on ml.list = l.list collate Latin1_General_CI_AI
	inner join lyrisarchive.dbo.messages_ m on m.listID = ml.listID
		and m.creatStamp_ between @digestDateStart and @digestDateEnd
	group by l.orgcode, ml.list;

	INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID, isTestMode)
	select l.orgcode, ml.list, 'ListThreadDigest', @digestDate, @readyToProcessStatusID, @isTestMode
	from @listsWithMCThreadDigestRecipients l
	inner join lyrisarchive.dbo.messageLists ml
	on ml.list = l.list collate Latin1_General_CI_AI
	inner join lyrisarchive.dbo.messages_ m	on m.listID = ml.listID
	and m.creatStamp_ between @digestDateStart and @digestDateEnd
	group by l.orgcode, ml.list;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO


use trialslyris1;
GO

ALTER PROC dbo.job_runDailyCustomJobs
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'MCDEV01\MEMBERCENTRAL' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'MCBETA01\MEMBERCENTRAL' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* Clear old lyrReportSummaryData entries */
/* ********************** */
BEGIN TRY
	delete top (50000) sd
	from lyrReportSummaryData sd
	inner join lists_ l on sd.list = l.name_
		and sd.created < dateadd(day,-1 *KeepOutmailPostings_,getdate())
		and KeepOutmailPostings_ <> 0
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to Clear old lyrReportSummaryData entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Backup members table */
/* ********************** */
BEGIN TRY
	EXEC lyrisMembersBackup.dbo.up_BackupLyrisMembers
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisMembersBackup.dbo.up_BackupLyrisMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* dbo.trialsmith_updateMarketingLists */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.trialsmith_updateMarketingLists;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.trialsmith_updateMarketingLists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************** */
/* natle_seminarWebLive */
/* ******************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_seminarWebLive;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_seminarWebLive';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* natle_justiceServices */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.natle_justiceServices;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.natle_justiceServices';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************* */
/* ts_membercentraladmins */
/* ********************* */
BEGIN TRY
	EXEC lyrisCustom.dbo.ts_membercentraladmins;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ts_membercentraladmins';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* *************** */
/* ky_listServices */
/* *************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.ky_listServices
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.ky_listServices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH

/* ********************** */
/* dbo.mc_updateListMemberships */
/* ********************** */
BEGIN TRY
	EXEC dbo.mc_updateListMemberships @debugMode=1
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running trialslyris1.dbo.mc_updateListMemberships'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Make sure EClips Sending Addresses are setup correctly */
/* ********************** */
BEGIN TRY
	update m set
		ExternalMemberID = null,
		ExpireDate_ = null,
		membertype_ = 'normal',
		subtype_ = 'nomail',
		IsListAdm_ = 'T',
		fullname_ = 'TrialSmith EClips Sending Address - DO NOT DELETE'
	from members_ m
	where EmailAddr_ = '<EMAIL>'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error making sure EClips Sending Addresses are setup correctly'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH





/* ********************** */
/* add segment name to trialsmith marketing list */
/* ********************** */
BEGIN TRY
   -- update mailings in outgoing mail table
    update om set
	    title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.outmail_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_ in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in needs approval
    update om set
	   title_ = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title_,70)
		    else left(upper(s.name_) + ' | ' + om.title_,70)
	    end
    from dbo.moderate_ om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID_
	    and om.list_  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title_ not like '%|%'

    -- update mailings in summary data table
    update om set
	    title = 
	    case when s.type_ = 'triggered' then left('Triggered | ' + om.title,70)
		    else left(upper(s.name_) + ' | ' + om.title,70)
	    end
    from dbo.lyrReportSummaryData om
    inner join subsets_ s
	    on s.SubsetID_ = om.SubsetID
	    and om.list  in ('trialsmith','trialsmith_sponsor_emails','natle_justiceservices','trialsmith_sponsor_sagesettlements','trialsmith_sponsor_counselfinancial','trialsmith_non_renewal','trialsmith_associationclients')
	    and om.Title not like '%|%'
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Adhoc queries to add segment name to trialsmith marketing list'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* Delete orphaned list members */
/* ********************** */
BEGIN TRY

	delete m
	from members_ m
	left outer join lists_ l 
		on l.name_ = m.list_
	where l.ListID_ is null

END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error deleting orphaned list members from lists'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/*    Queue List Digest   */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=0;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.list_queueDigest';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH


/* ********************** */
/*    Queue List Digest  Test Mode */
/* ********************** */
BEGIN TRY
	EXEC lyrisCustom.dbo.list_queueDigest @isTestMode=1;
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running lyrisCustom.dbo.list_queueDigest testmode';

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg;

	EXEC membercentral.membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>', @cc=null, @bcc=null, 
		@subject=@errorSubject, @message=@errmsg, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null;
END CATCH


RETURN 0
GO

use lyrisarchive;
GO

ALTER PROC [dbo].[lists_getDigestInfo]
@listname varchar(100),
@startDate datetime,
@endDate datetime

AS


declare @digestThreads TABLE (messageid_ int PRIMARY KEY, threadStartDate datetime, firstMessageInDateRange datetime, digestMessageCount int, totalMessageCount int, totalParticipantCount int, subject varchar(1000), hdrFrom varchar(200), fromAddress varchar(200), fullname varchar(500), memberNumber varchar (100))
declare @digestMessages TABLE (messageid_ int PRIMARY KEY, threadMessageID int, messagedate datetime, hdrFrom varchar(200), fromaddress varchar(200), hasAttachments bit, memberNumber varchar (100),fullname varchar(500))
declare @digestMemberPhotos TABLE (memberNumber varchar (100) PRIMARY KEY, hasPhoto bit)



insert into @digestThreads (messageid_, threadStartDate, firstMessageInDateRange, digestMessageCount, subject, hdrFrom, fromAddress)
SELECT max(m.parentid_) as parentid_, parentMessage.CreatStamp_, min(m.CreatStamp_), count(m.messageid_), parentMessage.hdrSubject_,parentMessage.HdrFrom_, parentMessage.HdrFromSpc_
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
inner join dbo.messages_ parentmessage on m.parentID_ = parentMessage.messageID_
where ml.list = @listname 
and m.creatstamp_ between @startDate and @enddate
and m.parentid_ is not null
group by m.parentid_, parentMessage.hdrSubject_, parentMessage.CreatStamp_, parentMessage.HdrFrom_, parentMessage.HdrFromSpc_

update t2 SET
    totalMessageCount = temp.totalMessageCount,
    totalParticipantCount = temp.totalParticipantCount
from @digestThreads t2
inner join (
    select t.messageid_, count(*) as totalMessageCount, count(distinct m.hdrfromspc_) as totalParticipantCount
    from @digestThreads t
    inner join messages_ m 
        on m.ParentID_ = t.messageid_
        and m.CreatStamp_ < @enddate
    group by t.messageid_
) as temp on temp.messageid_ = t2.messageid_


update @digestThreads SET
    totalMessageCount = 0
where totalMessageCount is null

insert into @digestMessages  (messageid_, threadMessageID,messagedate, HdrFrom,  fromaddress, hasAttachments)
select m.MessageID_, t.messageid_, m.CreatStamp_, m.HdrFrom_,m.HdrFromSpc_, isnull(m.attachmentflag,0)
from @digestThreads t
inner join messages_ m
    on t.messageid_ = m.ParentID_
    and m.creatstamp_ between @startDate and @enddate


update t set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestThreads t
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = t.fromaddress
    and m.List_ = @listname

update m2 set 
    memberNumber=ExternalMemberID,
    fullname = m.FullName_
from @digestMessages m2
inner join trialslyris1.dbo.members_ m 
    on m.EmailAddr_ = m2.fromaddress
    and m.List_ = @listname


select t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,
    t.totalParticipantCount, digestParticipantCount = count(distinct dm.fromaddress),
    messagesWithAttachments = sum( case when dm.hasAttachments = 1 then cast(1 as int) else cast(0 as int) end),
    numReplies = sum( case when dm.fromaddress = t.fromAddress then cast(0 as int) else cast(1 as int) end),
    participants = lyrisCustom.dbo.PipeList(distinct isnull(dm.fullname, dm.fromAddress))
from @digestThreads t
inner join @digestMessages dm
    on dm.threadMessageID = t.messageid_
group by t.messageid_, t.subject, t.threadStartDate, t.firstMessageInDateRange, t.digestMessageCount, t.totalMessageCount, t.hdrFrom, t.fromAddress, t.fullname, t.memberNumber,t.totalParticipantCount


select messageid_, threadMessageID , messagedate, hdrFrom, fromaddress, hasAttachments, memberNumber,fullname
from @digestMessages
order by threadMessageID, messageID_

GO