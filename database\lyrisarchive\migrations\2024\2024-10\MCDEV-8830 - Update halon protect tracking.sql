use EmailTracking;
GO

ALTER PROCEDURE emailTracking_recordIncomingMessageStatusFromHalonProtect
	@hdrMessageid varchar(1000),
	@sesID varchar(150),
	@stepFunctionName varchar(250),
	@dateCreated datetime,
	@subject varchar(500),
	@fromAddress varchar(320),
	@area1Disposition varchar(50),
	@mcDisposition varchar(50),
	@status varchar(50),
	@reason varchar(3000),
	@sentTimestamp bigint,
    @incomingMessageID int OUTPUT
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Check if the record already exists based on sesID aka Delviered message
	IF EXISTS (SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @hdrMessageid)
	BEGIN
		-- Check if the sentTimestamp is greater than the existing record
		IF EXISTS (SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @hdrMessageid AND @sentTimestamp >= sentTimestamp)
		BEGIN
			-- Update the existing record (should be a delivery record)
			UPDATE emailtracking.dbo.incomingMessages
			SET
				hdrMessageid = @hdrMessageid,
				sesID = @sesID,
				stepFunctionName = @stepFunctionName,
				dateCreated = @dateCreated,
				[fromAddress] = @fromAddress,
				[area1Disposition] = @area1Disposition,
				[mcDisposition] = @mcDisposition,
				[status] = @status,
				[reason] = @reason,
				[sentTimestamp] = @sentTimestamp
			WHERE sesID = @hdrMessageid;

            SELECT @incomingMessageID = incomingMessageID
            FROM emailtracking.dbo.incomingMessages
            WHERE sesID = @sesID;
		END
	END
	ELSE
	BEGIN

        IF NOT EXISTS (
            SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID
        ) BEGIN
            SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
            -- Insert a new record
            INSERT INTO emailtracking.dbo.incomingMessages (
                [hdrMessageid],
                [sesID],
                [stepFunctionName],
                [dateCreated],
                [subject],
                [fromAddress],
                [area1Disposition],
                [mcDisposition],
                [status],
                [reason],
                [sentTimestamp]
            )
            select @hdrMessageid,
                @sesID,
                @stepFunctionName,
                @dateCreated,
                @subject,
                @fromAddress,
                @area1Disposition,
                @mcDisposition,
                @status,
                @reason,
                @sentTimestamp
            WHERE NOT EXISTS(
                SELECT TOP 1 sesID FROM emailtracking.dbo.incomingMessages WHERE sesID = @sesID
            );
            set @incomingMessageID = SCOPE_IDENTITY();
            SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
        END
	END;

	RETURN 0;    
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH

GO
