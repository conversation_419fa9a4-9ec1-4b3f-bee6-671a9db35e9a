use lyrisCustom;
GO

ALTER PROC dbo.list_queueDigest
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyToProcessStatusID INT, @digestDate DATETIME, @digestDateStart DATETIME, @digestDateEnd DATETIME;
	DECLARE @listsWithMCThreadIndexRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(5));
	DECLARE @listsWithMCThreadDigestRecipients TABLE (list varchar(100) PRIMARY KEY, orgcode varchar(5));

	SET @digestDate = DATEADD(D,-1,GETDATE());
	SET @digestDateStart = cast(@digestDate as date);
	SET @digestDateEnd = DATEADD(MS,-3,DATEADD(d,1,@digestDateStart));

	SELECT @queueTypeID = queueTypeID
	FROM memberCentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'listDigests';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM memberCentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'ReadyToProcess';


	INSERT INTO @listsWithMCThreadIndexRecipients(list)
	select list_
	from trialslyris1.dbo.members_ m
	inner join trialslyris1.dbo.lists_format lf 
		on lf.name = m.list_ collate Latin1_General_CI_AI
		and m.receiveMCThreadIndex = 1
	inner join membercentral.membercentral.dbo.sites s 
		on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
	inner join membercentral.membercentral.dbo.lists_lists l
		on l.listName = lf.name collate Latin1_General_CI_AI
		and l.supportsMCThreadIndex = 1
	inner join membercentral.membercentral.dbo.cms_siteResources sr
		on sr.siteResourceID = l.siteResourceID
		and sr.siteResourceStatusID=1
		group by list_;

	INSERT INTO @listsWithMCThreadDigestRecipients(list)
	select list_
	from trialslyris1.dbo.members_ m
	inner join trialslyris1.dbo.lists_format lf 
		on lf.name = m.list_ collate Latin1_General_CI_AI
		and m.receiveMCThreadDigest = 1
	inner join membercentral.membercentral.dbo.sites s 
		on s.sitecode = lf.orgcode collate Latin1_General_CI_AI 
	inner join membercentral.membercentral.dbo.lists_lists l
		on l.listName = lf.name collate Latin1_General_CI_AI
		and l.supportsMCThreadDigest = 1
	inner join membercentral.membercentral.dbo.cms_siteResources sr
		on sr.siteResourceID = l.siteResourceID
		and sr.siteResourceStatusID=1
		group by list_;

	UPDATE t 
	SET t.orgcode = lf.orgcode
	FROM @listsWithMCThreadIndexRecipients t
	INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
		AND lf.disabled = 0;

	UPDATE t 
	SET t.orgcode = lf.orgcode
	FROM @listsWithMCThreadDigestRecipients t
	INNER JOIN trialslyris1.dbo.lists_format lf ON t.list = lf.name collate Latin1_General_CI_AI
		AND lf.disabled = 0;

	-- populate platformQueue table
	INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID)
	select l.orgcode, ml.list, 'ListThreadIndex', @digestDate, @readyToProcessStatusID
	from @listsWithMCThreadIndexRecipients l
	inner join lyrisarchive.dbo.messageLists ml on ml.list = l.list collate Latin1_General_CI_AI
	inner join lyrisarchive.dbo.messages_ m on m.listID = ml.listID
		and m.creatStamp_ between @digestDateStart and @digestDateEnd
	group by l.orgcode, ml.list;

	INSERT INTO memberCentral.platformQueue.dbo.queue_listDigests (orgCode, listname, digestType, digestDate, statusID)
	select l.orgcode, ml.list, 'ListThreadDigest', @digestDate, @readyToProcessStatusID
	from @listsWithMCThreadDigestRecipients l
	inner join lyrisarchive.dbo.messageLists ml
	on ml.list = l.list collate Latin1_General_CI_AI
	inner join lyrisarchive.dbo.messages_ m	on m.listID = ml.listID
	and m.creatStamp_ between @digestDateStart and @digestDateEnd
	group by l.orgcode, ml.list;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO