use trialslyris1;
GO

ALTER PROC [dbo].[job_enforceListSettings]
AS


-- turn off join by email for all lists
update lists_
set NoEmailSub_ = 'T'
where NoEmailSub_ <> 'T'

-- force all lists to only allow admins to add members
update lists_ 
set security_ = 'private'
where security_ = 'open' and name_ not in ('eclips_js','brandigy')

-- set all lists to invisible in Discussion Forum Interface
update lists_ 
set MriVisibility_ = 'I'
where MriVisibility_ <> 'I'



-- update maxmesssize to 50000 for lists using offloadattachments with groovy script
update l set
	maxmessSiz_ = 50000
from lists_ l
inner join lists_format lf
	on lf.name = l.name_
where lf.offloadAttachments = 1 and disableHTML=0
and cast(pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
and maxmessSiz_ <> 50000



-- update maxmesssize to 1500 for discussion lists not using offloadattachments
update l set
	maxmessSiz_ = 1500
from lists_ l
inner join (
	select name_
	from lists_ 
	where maxmessSiz_ <> 1500 and adminsend_ = 'F'

	except

	select name_
	from lists_ l2
	inner join lists_format lf
		on lf.name = l2.name_
	where lf.offloadAttachments = 1 and disableHTML=0
	and cast(pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
) as temp
on temp.name_ = l.name_

-- turn on advanced scripting for all lists using HTMLConversion

update l set l.MergeCapabilities_ = 2
from lists_ l
inner join lists_format lf
	on lf.name = l.name_
	and l.MergeCapabilities_ <> 2 
	and cast(l.pgmbefore_ as varchar(500))<> ''
	and disableHTML=0


RETURN 0