USE trialslyris1
GO

ALTER PROC dbo.createSendgridSubusersAndDomains
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @tier varchar(12);
	select @tier = 
			case
			when @@SERVERNAME = 'MCDEV01\LYRISARCHIVE' then 'Development'
			when @@SERVERNAME = 'MCBETA01\LYRISARCHIVE' then 'Beta'
			else 'Production'
			end;
	select @tier;
	
	if @tier <> 'Production'
		GOTO on_done;
	
	DECLARE @subuserListserversID int, @sysMemberID int, @queueTypeID int, 
			@statusReadyToCreate int, @domainAuthQueueTypeID int, @statusWaitingForSubuserCreation int, @xmlMessage xml, @statusID int;

	declare @existingSubusersAndDomains TABLE (siteID int, sitecode varchar(10), sendingHostname varchar(500), subuserid int);
	declare @neededSubusersAndDomains TABLE (siteID int, sitecode varchar(10), sendingHostname varchar(500));

	declare @createdSubusers TABLE (siteID int, sitecode varchar(10), sendingHostname varchar(500), subuserID int);

	select @queueTypeID = queueTypeID from membercentral.platformQueue.dbo.tblQueueTypes where queueType = 'SendgridSubuserCreate';
	select @statusReadyToCreate = queueStatusID from membercentral.platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToCreate';    
	select @domainAuthQueueTypeID = queueTypeID from membercentral.platformQueue.dbo.tblQueueTypes where queueType = 'SendgridSubuserDomainAuth';
	select @statusWaitingForSubuserCreation = queueStatusID from membercentral.platformQueue.dbo.tblQueueStatuses where queueTypeID = @domainAuthQueueTypeID and queueStatus = 'waitingForSubuserCreation';
	SELECT @sysMemberID = memberID
	FROM membercentral.membercentral.dbo.ams_members
	WHERE orgID = 1 
	AND memberNumber = 'SYSTEM'
	AND status = 'A';

	select @statusID = subuserStatusID from membercentral.platformMail.dbo.sendgrid_subuserStatuses where [status] = 'Not Created';
	if @statusID is null 
		GOTO on_done;
	

    insert into @existingSubusersAndDomains (siteID, sitecode, sendingHostname, subuserid)
    select su.siteID, s.sitecode, sud.sendingHostname, su.subuserid
    from membercentral.platformmail.dbo.email_mailstreams ms
    inner join membercentral.platformmail.dbo.sendgrid_subuserMailstreams sums
        on sums.mailstreamID=ms.mailStreamID
        and ms.mailStreamCode='lists'
    inner join membercentral.platformmail.dbo.sendgrid_subusers su
        on su.subuserID = sums.subuserID
    inner join membercentral.membercentral.dbo.sites s
        on s.siteID = su.siteID
    left outer join membercentral.platformmail.dbo.sendgrid_subuserDomains sud
        on sud.subuserID = su.subuserID;

    insert into @neededSubusersAndDomains (siteID, sitecode, sendingHostname)
    select sites.siteID, sites.sitecode, sendingHostname=s.DomainName_
    from lists_ l
    inner join topics_ t
        on t.Title_ = l.Topic_
        and l.AdminSend_ = 'F'
    inner join sites_ s 
        on t.SiteName_ = s.Name_
    inner join lists_format lf
        on lf.name = l.name_
    inner join membercentral.membercentral.dbo.lists_lists ml
        on ml.listname = lf.name  collate Latin1_General_CI_AI
    inner join membercentral.membercentral.dbo.cms_siteresources sr
        on sr.siteresourceID = ml.siteresourceID
    inner join membercentral.membercentral.dbo.sites 
        on sites.siteID = sr.siteID
        and sites.sitecode = lf.orgcode collate Latin1_General_CI_AI
    union

    select sites.siteID, sites.sitecode, sendingHostname = case n.networkName when 'TrialSmith' then 'lists.trialsmith.com' else 'lists.membercentral.com' end
    from lists_ l
    inner join topics_ t
        on t.Title_ = l.Topic_
        and l.AdminSend_ = 'F'
    inner join lists_format lf
        on lf.name = l.name_
    inner join membercentral.membercentral.dbo.lists_lists ml
        on ml.listname = lf.name  collate Latin1_General_CI_AI
    inner join membercentral.membercentral.dbo.cms_siteresources sr
        on sr.siteresourceID = ml.siteresourceID
    inner join membercentral.membercentral.dbo.sites 
        on sites.siteID = sr.siteID
        and sites.sitecode = lf.orgcode collate Latin1_General_CI_AI
    inner join membercentral.membercentral.dbo.networksites ns
        on ns.siteID = sites.siteID
        and ns.isloginNetwork=1
    inner join membercentral.membercentral.dbo.networks n
        on n.networkID = ns.networkID;

	-- subusers needed

	-- Create Listservers subuser
	insert into membercentral.platformMail.dbo.sendgrid_subusers (siteID, firstname, lastname, email, username, [password], environmentID, statusID)
	select siteID, sitecode, 'Listserv', '<EMAIL>', 'membercentral_' + LOWER(sitecode) + '_listservers', sitecode + '-' + CAST(NEWID() as varchar(36)), 5, @statusID
	from (
		select siteID, sitecode from @neededSubusersAndDomains
		except
		select siteID, sitecode from @existingSubusersAndDomains
	) x;

	-- Get the new created subusers locally so we don't need to keep querying cross database.
	insert into @createdSubusers (siteID, siteCode, subuserID)
	select x.siteID, x.siteCode, ss.subuserID
	from membercentral.platformMail.dbo.sendgrid_subusers ss
	inner join (
		select siteID, sitecode from @neededSubusersAndDomains
		except
		select siteID, sitecode from @existingSubusersAndDomains
	) x on x.siteID = ss.siteID and x.siteCode = ss.firstName collate Latin1_General_CI_AI and ss.lastname = 'Listserv' and ss.statusID =  @statusID;
	

	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for listservers
	insert into membercentral.platformMail.dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, 4 as mailstreamId, 2 as ipPoolID, 'noreply' as defaultFromUsername
	from @createdSubusers;

	
	-- populate sendgrid_subuserDomains and activeSubuserDomainID for subuser
	insert into membercentral.platformMail.dbo.sendgrid_subuserDomains (subuserID, dateCreated, sendingHostname, linkBrandHostname, statusID)
	select cs.subuserID, getdate(), x.sendingHostName, x.sendingHostName, @statusID
	from (
		select siteID, sitecode, sendingHostname from @neededSubusersAndDomains
		except
		select siteID, sitecode, sendingHostname from @existingSubusersAndDomains
	) x
	inner join @createdSubusers cs on cs.siteID = x.siteID and cs.sitecode = x.sitecode;

    
    -- listservers subusers

    INSERT INTO membercentral.platformQueue.dbo.queue_sendgridSubuserCreate (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
    select siteID, subuserid, 'subuserListserversID', @sysMemberID, @statusReadyToCreate, getdate(), getdate() 
    from @createdSubusers
    
    INSERT INTO membercentral.platformQueue.dbo.queue_SendgridSubuserDomainAuth (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
    select siteID, subuserid, 'subuserListserversID', @sysMemberID, @statusWaitingForSubuserCreation, getdate(), getdate() 
    from @createdSubusers


    -- resume Subuser Create task
    EXEC membercentral.memberCentral.dbo.sched_resumeTask @name='Sendgrid Subuser Create Queue', @engine='BERLinux';

    -- resume Domain Auth task
    EXEC membercentral.memberCentral.dbo.sched_resumeTask @name='Sendgrid Domain Auth Queue', @engine='BERLinux';

    on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	RETURN -1;
END CATCH
GO